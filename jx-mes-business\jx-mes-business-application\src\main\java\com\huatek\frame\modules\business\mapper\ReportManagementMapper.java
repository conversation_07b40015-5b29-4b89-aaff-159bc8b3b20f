package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.ReportManagement;
import com.huatek.frame.modules.business.domain.vo.ReportManagementVO;
import com.huatek.frame.modules.business.service.dto.ReportManagementDTO;
import org.apache.ibatis.annotations.Param;
import java.util.Map;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 报告管理mapper
* <AUTHOR>
* @date 2025-08-15
**/
public interface ReportManagementMapper extends BaseMapper<ReportManagement> {

     /**
	 * 报告管理分页
	 * @param dto
	 * @return
	 */
	Page<ReportManagementVO> selectReportManagementPage(ReportManagementDTO dto);

    /**
	 * 外键关联表: awaiting_production_order - work_order_number
     **/
    @ApiModelProperty("外键 awaiting_production_order - work_order_number")
	Page<SelectOptionsVO> selectOptionsByWorkOrderNumber(String workOrderNumber);
    Map<String,String> selectDataLinkageByWorkOrderNumber(@Param("work_order_number") String work_order_number);

    /**
     * 根据条件查询报告管理列表
     *
     * @param dto 报告管理信息
     * @return 报告管理集合信息
     */
    List<ReportManagementVO> selectReportManagementList(ReportManagementDTO dto);

	/**
	 * 根据IDS查询报告管理列表
	 * @param ids
	 * @return
	 */
    List<ReportManagementVO> selectReportManagementListByIds(@Param("ids") List<String> ids);


}