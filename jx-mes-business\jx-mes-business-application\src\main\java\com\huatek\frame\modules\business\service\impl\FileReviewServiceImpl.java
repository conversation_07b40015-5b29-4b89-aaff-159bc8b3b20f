package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.CapabilityReview;
import com.huatek.frame.modules.business.domain.FileReview;
import com.huatek.frame.modules.business.domain.ProductionOrder;
import com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO;
import com.huatek.frame.modules.business.domain.vo.FileReviewVO;
import com.huatek.frame.modules.business.mapper.AwaitingProductionOrderMapper;
import com.huatek.frame.modules.business.mapper.CapabilityReviewMapper;
import com.huatek.frame.modules.business.mapper.FileReviewMapper;
import com.huatek.frame.modules.business.service.AbnormalfeedbackService;
import com.huatek.frame.modules.business.service.CapabilityReviewService;
import com.huatek.frame.modules.business.service.FileReviewService;
import com.huatek.frame.modules.business.service.dto.AbnormalfeedbackDTO;
import com.huatek.frame.modules.business.service.dto.FileReviewDTO;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import java.sql.Date;
import org.springframework.util.CollectionUtils;



/**
 * 文件评审 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "fileReview")
//@RefreshScope
@Slf4j
public class FileReviewServiceImpl implements FileReviewService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private FileReviewMapper fileReviewMapper;

	@Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

    @Autowired
    private AbnormalfeedbackService abnormalfeedbackService;
    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public FileReviewServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<FileReviewVO>> findFileReviewPage(FileReviewDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<FileReviewVO> fileReviews = fileReviewMapper.selectFileReviewPage(dto);
		TorchResponse<List<FileReviewVO>> response = new TorchResponse<List<FileReviewVO>>();
		response.getData().setData(fileReviews);
		response.setStatus(200);
		response.getData().setCount(fileReviews.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(FileReviewDTO fileReviewDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(fileReviewDto.getCodexTorchDeleted())) {
            fileReviewDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = fileReviewDto.getId();
		FileReview entity = new FileReview();
        BeanUtils.copyProperties(fileReviewDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			fileReviewMapper.insert(entity);
		} else {
			fileReviewMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        FileReviewVO vo = new FileReviewVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<FileReviewVO> findFileReview(String id) {
		FileReviewVO vo = new FileReviewVO();
		if (!HuatekTools.isEmpty(id)) {
			FileReview entity = fileReviewMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<FileReviewVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<FileReview> fileReviewList = fileReviewMapper.selectBatchIds(Arrays.asList(ids));
        for (FileReview fileReview : fileReviewList) {
            fileReview.setCodexTorchDeleted(Constant.DEFAULT_YES);
            fileReviewMapper.updateById(fileReview);
        }
		//fileReviewMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("workOrder",fileReviewMapper::selectOptionsByWorkOrder);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "file_review", convertorFields = "reviewResult")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<FileReviewVO> selectFileReviewList(FileReviewDTO dto) {
        return fileReviewMapper.selectFileReviewList(dto);
    }

    /**
     * 导入文件评审数据
     *
     * @param fileReviewList 文件评审数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "file_review", convertorFields = "reviewResult")
    public TorchResponse importFileReview(List<FileReviewVO> fileReviewList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(fileReviewList) || fileReviewList.size() == 0) {
            throw new ServiceException("导入文件评审数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (FileReviewVO vo : fileReviewList) {
            try {
                FileReview fileReview = new FileReview();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, fileReview);
                QueryWrapper<FileReview> wrapper = new QueryWrapper();
                FileReview oldFileReview = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = FileReviewVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<FileReview> oldFileReviewList = fileReviewMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldFileReviewList) && oldFileReviewList.size() > 1) {
                        fileReviewMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldFileReviewList) && oldFileReviewList.size() == 1) {
                        oldFileReview = oldFileReviewList.get(0);
                    }
                }
                if (StringUtils.isNull(oldFileReview)) {
                    BeanValidators.validateWithException(validator, vo);
                    fileReviewMapper.insert(fileReview);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、产品名称 " + vo.getProductName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldFileReview, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    fileReviewMapper.updateById(oldFileReview);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、产品名称 " + vo.getProductName() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、产品名称 " + vo.getProductName() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、产品名称 " + vo.getProductName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(FileReviewVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (!HuatekTools.isEmpty(vo.getWorkOrder())) {
            List<String> productionWorkOrderList = Arrays.asList(vo.getWorkOrder().split(","));
            List<ProductionOrder> list = awaitingProductionOrderMapper.selectList(new QueryWrapper<ProductionOrder>().in("work_order_number", productionWorkOrderList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("生产工单=" + vo.getWorkOrder() + "; ");
            }
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectFileReviewListByIds(List<String> ids) {
        List<FileReviewVO> fileReviewList = fileReviewMapper.selectFileReviewListByIds(ids);

		TorchResponse<List<FileReviewVO>> response = new TorchResponse<List<FileReviewVO>>();
		response.getData().setData(fileReviewList);
		response.setStatus(200);
		response.getData().setCount((long)fileReviewList.size());
		return response;
    }

    @Override
    public TorchResponse issueProductCreateFileVeiw(List<ProductionOrder> orders) {
        orders.stream().forEach(order->{
            FileReview fileReview = new FileReview();
            fileReview.setWorkOrder(order.getWorkOrderNumber());
            fileReview.setProduct(order.getProduct());
            fileReview.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
            fileReview.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
            fileReviewMapper.insert(fileReview);
            //TODO: 文件评审若存在同分类同规范且审核通过的记录，则生成数据时默认审批通过，
        });
        TorchResponse response = new TorchResponse();
        response.setStatus(200);
        return response;
    }

    @Override
    public TorchResponse reviewUpdateFileReview(FileReviewDTO fileReviewDto) {
        for(String id :fileReviewDto.getIds()){
            FileReview review = fileReviewMapper.selectById(id);
            if(StringUtils.isNotEmpty(fileReviewDto.getChangeFile())){
                review.setChangeFile(fileReviewDto.getChangeFile());
            }else{
                review.setReviewResult(fileReviewDto.getReviewResult());
                review.setReviewRemark(fileReviewDto.getReviewRemark());
                review.setReviewer(SecurityContextHolder.getCurrentUserId());
                review.setReviewTime(new Timestamp(System.currentTimeMillis()));
            }
            review.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
            review.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
            fileReviewMapper.updateById(review);
        }
        TorchResponse response = new TorchResponse();
        response.setStatus(200);
        return response;
    }

    @Override
    public TorchResponse abnormalfeedback(AbnormalfeedbackDTO abnormalfeedbackDto) {
        abnormalfeedbackDto.setSourceModule(BusinessConstant.CAPABILITY_REVIEW_FILE);
        FileReview entity = fileReviewMapper.selectById(abnormalfeedbackDto.getSourceId());
        TorchResponse<AbnormalfeedbackVO> torchResponse = abnormalfeedbackService.saveOrUpdate(abnormalfeedbackDto);
        AbnormalfeedbackVO vo = torchResponse.getData().getData();
        if (HuatekTools.isEmpty(entity.getAssocExceptionFeedbackNum())) {
            entity.setAssocExceptionFeedbackNum(vo.getAbnormalNumber());
        }else{
            entity.setAssocExceptionFeedbackNum(entity.getAssocExceptionFeedbackNum() + "," + vo.getAbnormalNumber());
        }
        fileReviewMapper.updateById(entity);
        TorchResponse response = new TorchResponse();
        response.setStatus(200);
        return response;
    }


}
