# 生产任务状态恢复功能改进

## 问题描述

原有的生产任务恢复功能存在问题：无论任务暂停前是什么状态，恢复时都固定恢复到"进行中"状态。这不符合业务需求，应该恢复到暂停前的原始状态。

例如：
- 未开始 → 暂停 → 恢复，应该恢复到"未开始"
- 进行中 → 暂停 → 恢复，应该恢复到"进行中"

## 解决方案

### 1. 修复操作记录保存逻辑

**文件**: `ProductionTaskServiceImpl.java`
**方法**: `insertOperationHistory`

**问题**: 虽然方法参数中有`previousStatus`，但没有设置到DTO中保存到数据库。

**修复**:
```java
private void insertOperationHistory(String taskNumber, String operationType, String reason,
                                    Integer completedQuantity, String comment,String previousStatus) {
    try {
        ProdTaskOpHistDTO histDto = new ProdTaskOpHistDTO();
        histDto.setTaskNumber(taskNumber);
        histDto.setOperationType(operationType);
        histDto.setReason(reason);
        histDto.setCompletedQuantity(completedQuantity);
        histDto.setComment(comment);
        histDto.setPreviousStatus(previousStatus);  // 新增：设置上一个状态
        prodTaskOpHistService.saveOrUpdate(histDto);
    } catch (Exception e) {
        log.error("插入操作记录失败", e);
    }
}
```

### 2. 改进恢复任务逻辑

**文件**: `ProductionTaskServiceImpl.java`
**方法**: `resumeTask`

**改进内容**:
1. 添加状态检查，确保只有暂停状态的任务才能恢复
2. 从操作记录中查找最近一次暂停操作的`previousStatus`
3. 恢复到暂停前的原始状态
4. 如果没有找到暂停记录，默认恢复到"进行中"状态
5. 改进返回消息，显示恢复到的具体状态

**核心逻辑**:
```java
@Override
public TorchResponse resumeTask(String id) {
    // 1. 检查任务是否存在
    ProductionTask productionTask = productionTaskMapper.selectById(id);
    if (productionTask == null) {
        throw new ServiceException("未找到对应的生产任务");
    }

    // 2. 检查当前状态是否为暂停
    if (!DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING.equals(productionTask.getStatus())) {
        throw new ServiceException("任务当前状态不是暂停，无法恢复");
    }

    String currentStatus = productionTask.getStatus();
    
    // 3. 从操作记录中查找最近一次暂停操作的previousStatus
    String resumeToStatus = getLastPauseOperationPreviousStatus(productionTask.getTaskNumber());
    
    // 4. 如果没有找到暂停记录的previousStatus，默认恢复到进行中
    if (HuatekTools.isEmpty(resumeToStatus)) {
        resumeToStatus = DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG;
    }
    
    // 5. 修改状态为恢复到的状态
    productionTask.setStatus(resumeToStatus);
    productionTaskMapper.updateById(productionTask);

    // 6. 插入操作记录
    insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_RESUME,
            null, productionTask.getCompletedQuantity(), null, currentStatus);

    TorchResponse response = new TorchResponse();
    response.setStatus(Constant.REQUEST_SUCCESS);
    response.setMessage("任务已恢复到状态：" + getStatusName(resumeToStatus));
    return response;
}
```

### 3. 新增辅助方法

#### 3.1 查询最近暂停操作记录

```java
private String getLastPauseOperationPreviousStatus(String taskNumber) {
    try {
        // 查询该任务最近一次暂停操作的记录
        QueryWrapper<ProdTaskOpHist> wrapper = new QueryWrapper<>();
        wrapper.eq("task_number", taskNumber);
        wrapper.eq("operation_type", DicConstant.ProductionOrder.OPERATION_TYPE_PAUSE);
        wrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
        wrapper.orderByDesc("codex_torch_create_datetime");
        wrapper.last("LIMIT 1");
        
        ProdTaskOpHist lastPauseRecord = prodTaskOpHistMapper.selectOne(wrapper);
        if (lastPauseRecord != null && !HuatekTools.isEmpty(lastPauseRecord.getPreviousStatus())) {
            return lastPauseRecord.getPreviousStatus();
        }
    } catch (Exception e) {
        log.error("查询最近一次暂停操作记录失败", e);
    }
    return null;
}
```

#### 3.2 状态名称转换

```java
private String getStatusName(String statusCode) {
    switch (statusCode) {
        case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI:
            return "未开始";
        case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG:
            return "进行中";
        case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE:
            return "待审批";
        case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI:
            return "驳回";
        case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING:
            return "暂停";
        case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_QUXIAO:
            return "取消";
        case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG:
            return "完成";
        case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_YIWAIXIE:
            return "已外协";
        default:
            return "未知状态";
    }
}
```

## 测试场景

### 场景1：未开始 → 暂停 → 恢复
1. 任务状态：未开始
2. 执行暂停操作，记录previousStatus = "0"（未开始）
3. 执行恢复操作，应恢复到"未开始"状态

### 场景2：进行中 → 暂停 → 恢复
1. 任务状态：进行中
2. 执行暂停操作，记录previousStatus = "1"（进行中）
3. 执行恢复操作，应恢复到"进行中"状态

### 场景3：没有暂停记录的恢复
1. 任务状态：暂停（但没有暂停操作记录）
2. 执行恢复操作，应默认恢复到"进行中"状态

### 场景4：非暂停状态的恢复
1. 任务状态：进行中
2. 执行恢复操作，应抛出异常

## 数据库表结构

确保`prod_task_op_hist`表包含`previous_status`字段：

```sql
ALTER TABLE prod_task_op_hist ADD COLUMN previous_status VARCHAR(10) COMMENT '上一个状态';
```

## 影响范围

1. **核心修改**: `ProductionTaskServiceImpl.java`
2. **数据库**: `prod_task_op_hist`表需要包含`previous_status`字段
3. **API**: `/resumeTask/{id}`接口行为改变，返回消息更详细
4. **向后兼容**: 对于没有`previous_status`记录的历史数据，会默认恢复到"进行中"状态

## 部署注意事项

1. 确保数据库表结构已更新
2. 测试各种状态转换场景
3. 检查现有暂停的任务是否能正确恢复
