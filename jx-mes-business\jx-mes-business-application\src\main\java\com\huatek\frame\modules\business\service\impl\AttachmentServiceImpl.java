package com.huatek.frame.modules.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.annotation.datascope.DataScope;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.modules.business.domain.Attachment;
import com.huatek.frame.modules.business.domain.vo.AttachmentVO;
import com.huatek.frame.modules.business.mapper.AttachmentMapper;
import com.huatek.frame.modules.business.service.AttachmentService;
import com.huatek.frame.modules.business.service.dto.AttachmentDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
* @description 设备原始数据ServiceImpl
* <AUTHOR>
* @date 2025-08-18
**/
@Service
public class AttachmentServiceImpl implements AttachmentService {

	@Autowired
	private AttachmentMapper attachmentMapper;

	public AttachmentServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<AttachmentVO>> findAttachmentPage(AttachmentDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<AttachmentVO> attachments = attachmentMapper.selectAttachmentPage(dto);
		TorchResponse<List<AttachmentVO>> response = new TorchResponse<List<AttachmentVO>>();
		response.getData().setData(attachments);
		response.setStatus(200);
		response.getData().setCount(attachments.getTotal());
		return response;
	}

	@Override
	@Transactional
	public TorchResponse saveOrUpdate(AttachmentDTO attachmentDto) {
		TorchResponse response = new TorchResponse();
		try {
			Attachment attachment = new Attachment();
			BeanUtils.copyProperties(attachmentDto, attachment);
			
			if (StringUtils.isNotEmpty(attachmentDto.getId())) {
				// 更新
				attachment.setUpdateTime(new Timestamp(System.currentTimeMillis()));
				attachmentMapper.updateById(attachment);
			} else {
				// 新增
				Timestamp now = new Timestamp(System.currentTimeMillis());
				attachment.setUploadTime(now);
				attachment.setUpdateTime(now);
				attachmentMapper.insert(attachment);
			}
			response.setStatus(200);
			response.setMessage("操作成功");
		} catch (Exception e) {
			response.setStatus(500);
			response.setMessage("操作失败：" + e.getMessage());
		}
		return response;
	}

	@Override
	public TorchResponse<AttachmentVO> findAttachment(String id) {
		TorchResponse<AttachmentVO> response = new TorchResponse<AttachmentVO>();
		try {
			Attachment attachment = attachmentMapper.selectById(id);
			if (attachment != null) {
				AttachmentVO attachmentVO = new AttachmentVO();
				BeanUtils.copyProperties(attachment, attachmentVO);
				response.getData().setData(attachmentVO);
				response.setStatus(200);
			} else {
				response.setStatus(404);
				response.setMessage("未找到数据");
			}
		} catch (Exception e) {
			response.setStatus(500);
			response.setMessage("查询失败：" + e.getMessage());
		}
		return response;
	}

	@Override
	@Transactional
	public TorchResponse delete(String[] ids) {
		TorchResponse response = new TorchResponse();
		try {
			List<String> idList = Arrays.asList(ids);
			attachmentMapper.deleteBatchIds(idList);
			response.setStatus(200);
			response.setMessage("删除成功");
		} catch (Exception e) {
			response.setStatus(500);
			response.setMessage("删除失败：" + e.getMessage());
		}
		return response;
	}

	@Override
	public TorchResponse<List<AttachmentVO>> getOptionsList(String id) {
		TorchResponse<List<AttachmentVO>> response = new TorchResponse<List<AttachmentVO>>();
		// 根据业务需求实现选项列表查询逻辑
		response.setStatus(200);
		return response;
	}

	@Override
	@DataScope(groupAlias = "t", userAlias = "t")
	public List<AttachmentVO> selectAttachmentList(AttachmentDTO dto) {
		return attachmentMapper.selectAttachmentList(dto);
	}

	@Override
	@Transactional
	public TorchResponse importAttachment(List<AttachmentVO> attachmentList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
		TorchResponse response = new TorchResponse();
		// 根据业务需求实现导入逻辑
		response.setStatus(200);
		response.setMessage("导入成功");
		return response;
	}

	@Override
	@DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse selectAttachmentListByIds(List<String> ids) {
		TorchResponse<List<AttachmentVO>> response = new TorchResponse<List<AttachmentVO>>();
		try {
			List<AttachmentVO> attachmentList = attachmentMapper.selectAttachmentListByIds(ids);
			response.getData().setData(attachmentList);
			response.setStatus(200);
		} catch (Exception e) {
			response.setStatus(500);
			response.setMessage("查询失败：" + e.getMessage());
		}
		return response;
	}

	@Override
	@DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<AttachmentVO>> selectAttachmentListByWorkOrderCode(String workOrderCode) {
		TorchResponse<List<AttachmentVO>> response = new TorchResponse<List<AttachmentVO>>();
		try {
			List<AttachmentVO> attachmentList = attachmentMapper.selectAttachmentListByWorkOrderCode(workOrderCode);
			response.getData().setData(attachmentList);
			response.setStatus(200);
		} catch (Exception e) {
			response.setStatus(500);
			response.setMessage("查询失败：" + e.getMessage());
		}
		return response;
	}

	@Override
	@DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<AttachmentVO>> selectAttachmentListByWorkOrderAndProcess(String workOrderCode, String processCode) {
		TorchResponse<List<AttachmentVO>> response = new TorchResponse<List<AttachmentVO>>();
		try {
			List<AttachmentVO> attachmentList = attachmentMapper.selectAttachmentListByWorkOrderAndProcess(workOrderCode, processCode);
			response.getData().setData(attachmentList);
			response.setStatus(200);
		} catch (Exception e) {
			response.setStatus(500);
			response.setMessage("查询失败：" + e.getMessage());
		}
		return response;
	}
}
