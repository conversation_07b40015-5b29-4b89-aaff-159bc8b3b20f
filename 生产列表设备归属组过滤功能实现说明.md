# 生产列表设备归属组过滤功能实现说明

## 需求描述
生产列表需要增加过滤条件，只有生产任务下的 `prod_task_eq_info`（存在多条，按照时间取第一条）的 `device_serial_number` 对应 `equipment_inventory` 的 `belonging_group` 才能看到。

## 实现方案

### 1. 修改 ProductionTaskServiceImpl.java

在 `queryExportCommon` 方法中添加设备归属组过滤逻辑：

**文件位置：** `jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java`

**修改内容：**
```java
// 在 queryExportCommon 方法末尾添加
// 设置设备归属组过滤条件：只显示当前用户所属组织能看到的生产任务
// 通过生产任务关联的设备信息来过滤
dto.setBelongingGroup(SecurityContextHolder.getCurrentUserGroupId());
```

**修改位置：** 第 245-247 行

### 2. 修改 ProductionTaskMapper.xml

在两个查询方法中添加设备归属组的过滤条件：

**文件位置：** `jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/mapping/ProductionTaskMapper.xml`

#### 2.1 selectProductionTaskPage 方法

**修改位置：** 第 338-349 行

**添加的 SQL 条件：**
```xml
<!-- 设备归属组过滤条件：只显示当前用户所属组织能看到的生产任务 -->
<if test="belongingGroup != null and belongingGroup != ''">
    and exists (
        select 1 from prod_task_eq_info ptei
        left join equipment_inventory ei on ptei.device_serial_number = ei.device_serial_number
        where ptei.task_number = t.task_number
        and ptei.codex_torch_deleted = '0'
        and ei.belonging_group = #{belongingGroup}
        order by ptei.codex_torch_create_datetime asc
        limit 1
    )
</if>
```

#### 2.2 selectProductionTaskList 方法

**修改位置：** 第 560-571 行

**添加的 SQL 条件：** 与上面相同

## 实现逻辑说明

### 1. 数据流程
1. 用户访问生产列表页面
2. `ProductionTaskServiceImpl.queryExportCommon()` 方法被调用
3. 通过 `SecurityContextHolder.getCurrentUserGroupId()` 获取当前用户的组织ID
4. 将组织ID设置到 DTO 的 `belongingGroup` 字段
5. MyBatis 执行查询时，会根据 `belongingGroup` 参数添加过滤条件

### 2. SQL 过滤逻辑
- 使用 `EXISTS` 子查询确保生产任务关联的设备存在
- 通过 `prod_task_eq_info` 表关联设备信息
- 通过 `equipment_inventory` 表获取设备的归属组
- 按照 `codex_torch_create_datetime` 升序排序，取第一条记录（`LIMIT 1`）
- 只显示设备归属组与当前用户组织匹配的生产任务

### 3. 关键表关联关系
```
production_task (生产任务)
    ↓ task_number
prod_task_eq_info (生产任务设备信息)
    ↓ device_serial_number  
equipment_inventory (设备台账)
    ↓ belonging_group
```

## 注意事项

1. **权限控制：** 该过滤条件会在现有的数据权限控制基础上进一步限制数据可见性
2. **性能考虑：** 使用了 `EXISTS` 子查询和 `LIMIT 1`，确保查询效率
3. **数据完整性：** 过滤条件会检查 `prod_task_eq_info` 表的 `codex_torch_deleted = '0'` 确保数据有效性
4. **时间排序：** 按照 `codex_torch_create_datetime` 升序排序，确保取到最早的设备记录

## 测试建议

1. 测试不同用户组织下的数据可见性
2. 测试生产任务没有关联设备的情况
3. 测试设备归属组为空的情况
4. 测试查询性能，确保添加过滤条件后查询效率可接受
