package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.sql.Date;

/**
 * 监制验收工单分页查询产品信息请求DTO
 */
@Data
public class SawOrderProductPageDTO {
    /**
     * 监制验收工单ID
     **/
    @ApiModelProperty("监制验收工单ID")
    private String sawOrderId;


    /**
     * 监制验收产品状态
     **/
    @ApiModelProperty("监制验收产品状态")
    private String sawStatus;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 生产批次
     **/
    @ApiModelProperty("生产批次")
    private String productionBatch;

    /**
     * 数量
     **/
    @ApiModelProperty("数量")
    private Integer quantity;



    /**
     * 质量等级
     **/
    @ApiModelProperty("质量等级")
    private String qualityGrade;


    /**
     * 请求页码
     */
    private Integer page;

    /**
     * 每页显示数量
     */
    private Integer limit;
}
