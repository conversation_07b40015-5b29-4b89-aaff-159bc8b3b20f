package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;
import com.huatek.frame.modules.business.domain.MaterialsLibrary;
import  com.huatek.frame.modules.business.domain.vo.MaterialsLibraryVO;
import com.huatek.frame.modules.business.service.dto.MaterialsLibraryDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 材料库mapper
* <AUTHOR>
* @date 2025-08-08
**/
public interface MaterialsLibraryMapper extends BaseMapper<MaterialsLibrary> {

     /**
	 * 材料库分页
	 * @param dto
	 * @return
	 */
	Page<MaterialsLibraryVO> selectMaterialsLibraryPage(MaterialsLibraryDTO dto);


    /**
     * 根据条件查询材料库列表
     *
     * @param dto 材料库信息
     * @return 材料库集合信息
     */
    List<MaterialsLibraryVO> selectMaterialsLibraryList(MaterialsLibraryDTO dto);

	/**
	 * 根据IDS查询材料库列表
	 * @param ids
	 * @return
	 */
    List<MaterialsLibraryVO> selectMaterialsLibraryListByIds(@Param("ids") List<String> ids);

}