package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.AccountingStandardDetails;
import com.huatek.frame.modules.business.domain.vo.AccountingStandardDetailsVO;
import com.huatek.frame.modules.business.service.dto.AccountingStandardDetailsDTO;
import com.huatek.frame.modules.business.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 核算标准明细mapper
* <AUTHOR>
* @date 2025-08-22
**/
public interface AccountingStandardDetailsMapper extends BaseMapper<AccountingStandardDetails> {

     /**
	 * 核算标准明细分页
	 * @param dto
	 * @return
	 */
	Page<AccountingStandardDetailsVO> selectAccountingStandardDetailsPage(AccountingStandardDetailsDTO dto);

    /**
	 * 外键关联表: standard_process_management - step_number
     **/
    @ApiModelProperty("外键 standard_process_management - step_number")
	Page<SelectOptionsVO> selectOptionsByExperimentProject(String experimentProject);

	/**
	 * 外键关联表: product_category - category_name
	 **/
	@ApiModelProperty("外键 product_category - category_name")
	Page<SelectOptionsVO> selectOptionsByProductCategory(String productCategory);

    /**
     * 根据条件查询核算标准明细列表
     *
     * @param dto 核算标准明细信息
     * @return 核算标准明细集合信息
     */
    List<AccountingStandardDetailsVO> selectAccountingStandardDetailsList(AccountingStandardDetailsDTO dto);

	/**
	 * 根据IDS查询核算标准明细列表
	 * @param ids
	 * @return
	 */
    List<AccountingStandardDetailsVO> selectAccountingStandardDetailsListByIds(@Param("ids") List<String> ids);


}