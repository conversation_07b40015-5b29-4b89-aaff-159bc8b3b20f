<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.huatek.frame.modules.system.mapper.SysGroupMapper">

	<resultMap type="com.huatek.frame.modules.system.domain.vo.SysGroupVO"
		id="SysGroupLists">
		<id column="id" property="id" />
		<result column="group_code" property="groupCode" />
		<result column="group_name" property="groupName" />
		<result column="group_parent_id" property="groupParentId" />
		<result column="deleted" property="deleted" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<!-- CODEX-TORCH-MAPPER-XML-RESULT-TAG-REPLACE-POSITION-EXTENDED-FIELDS -->
		<collection property="children" column="{deleted=deleted,groupParentId=id}"
			ofType="com.huatek.frame.modules.system.domain.vo.SysGroupVO"
			select="getParentGroups" />
	</resultMap>

	<select id="getParentGroups" resultMap="SysGroupLists" parameterType="HashMap">
		select
			parent.id,parent.group_code,parent.group_name,parent.group_parent_id,parent.deleted ,parent.create_date
		<!-- CODEX-TORCH-MAPPER-XML-REPLACE-POSITION-EXTENDED-FIELDS -->
		from
			sys_group parent
		where
			parent.deleted = #{deleted}
		<if test="groupParentId != null and groupParentId != '' ">
			and parent.group_parent_id = #{groupParentId}
		</if>
		<if test="groupName != null and groupName != '' ">
			and parent.group_name like concat('%',#{groupName}, '%')
		</if>
		<!-- CODEX-GENERATE-MAPPER-XML-REPLACE-POSITION-EXTENDED-QUERY-CONDITION -->
		order by
		parent.create_date desc
	</select>

	<select id="selectGroupCodeByGroupId" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT group_code 
		FROM sys_group 
		WHERE deleted = '0' 
		AND (
			id = #{groupId} 
			OR FIND_IN_SET(id, (
				SELECT REPLACE(ancestors, ',', ',') 
				FROM sys_group 
				WHERE id = #{groupId} AND deleted = '0'
			))
		)
		ORDER BY 
			CASE WHEN id = #{groupId} THEN 999999 
			ELSE CHAR_LENGTH(ancestors) - CHAR_LENGTH(REPLACE(ancestors, ',', '')) 
			END
	</select>

</mapper>
