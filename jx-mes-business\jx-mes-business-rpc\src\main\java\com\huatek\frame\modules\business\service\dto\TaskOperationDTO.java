package com.huatek.frame.modules.business.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 任务操作DTO实体类
 * <AUTHOR>
 * @date 2025-08-14
 **/
@Data
@ApiModel("任务操作DTO实体类")
public class TaskOperationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 生产任务ID
     **/
    @ApiModelProperty("生产任务ID")
    private String id;

    /**
     * 原因
     **/
    @ApiModelProperty("原因")
    private String reason;

    /**
     * 已完成数量
     **/
    @ApiModelProperty("已完成数量")
    private Integer completedQuantity;

    /**
     * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;

}
