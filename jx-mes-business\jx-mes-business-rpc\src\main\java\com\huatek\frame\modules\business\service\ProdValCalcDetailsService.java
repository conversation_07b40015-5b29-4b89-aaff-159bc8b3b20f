package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.ProdValCalcDetailsVO;
import com.huatek.frame.modules.business.service.dto.ProdValCalcDetailsDTO;

import java.util.List;


/**
* @description 产值计算明细Service
* <AUTHOR>
* @date 2025-08-22
**/
public interface ProdValCalcDetailsService {
    
    /**
	 * 分页查找查找 产值计算明细
	 * 
	 * @param dto 产值计算明细dto实体对象
	 * @return 
	 */
	TorchResponse<List<ProdValCalcDetailsVO>> findProdValCalcDetailsPage(ProdValCalcDetailsDTO dto);

    /**
	 * 添加 \修改 产值计算明细
	 * 
	 * @param prodValCalcDetailsDto 产值计算明细dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(ProdValCalcDetailsDTO prodValCalcDetailsDto);
	
	/**
	 * 通过id查找产值计算明细
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<ProdValCalcDetailsVO> findProdValCalcDetails(String id);
	
	/**
	 * 删除 产值计算明细
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 产值计算明细
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<ProdValCalcDetailsVO>> getOptionsList(String id);




    /**
     * 根据条件查询产值计算明细列表
     *
     * @param dto 产值计算明细信息
     * @return 产值计算明细集合信息
     */
    List<ProdValCalcDetailsVO> selectProdValCalcDetailsList(ProdValCalcDetailsDTO dto);

    /**
     * 导入产值计算明细数据
     *
     * @param prodValCalcDetailsList 产值计算明细数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importProdValCalcDetails(List<ProdValCalcDetailsVO> prodValCalcDetailsList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取产值计算明细数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectProdValCalcDetailsListByIds(List<String> ids);

	/**
	 * 获取产值计算明细数据
	 * @param productionValueCalculationId
	 * @return
	 */
	List<ProdValCalcDetailsVO> selectPVCDListByPVCIdJunXin(String productionValueCalculationId);
	List<ProdValCalcDetailsVO> selectPVCDListByPVCIdCustomer(String productionValueCalculationId);



}