package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;
import com.huatek.frame.modules.business.domain.SalesOutbound;
import  com.huatek.frame.modules.business.domain.vo.SalesOutboundVO;
import com.huatek.frame.modules.business.service.dto.SalesOutboundDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 销售出库mapper
* <AUTHOR>
* @date 2025-08-06
**/
public interface SalesOutboundMapper extends BaseMapper<SalesOutbound> {

     /**
	 * 销售出库分页
	 * @param dto
	 * @return
	 */
	Page<SalesOutboundVO> selectSalesOutboundPage(SalesOutboundDTO dto);


    /**
     * 根据条件查询销售出库列表
     *
     * @param dto 销售出库信息
     * @return 销售出库集合信息
     */
    List<SalesOutboundVO> selectSalesOutboundList(SalesOutboundDTO dto);

	/**
	 * 根据IDS查询销售出库列表
	 * @param ids
	 * @return
	 */
    List<SalesOutboundVO> selectSalesOutboundListByIds(@Param("ids") List<String> ids);

	/**
	 * 批量插入销售出库记录
	 * @param salesOutboundList 销售出库记录列表
	 * @return 插入成功的条数
	 */
	int batchInsert(List<SalesOutbound> salesOutboundList);

}