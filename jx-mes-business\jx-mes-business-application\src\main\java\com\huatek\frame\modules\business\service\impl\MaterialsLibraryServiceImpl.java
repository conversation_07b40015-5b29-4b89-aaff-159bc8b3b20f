package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.MaterialsLibrary;
import com.huatek.frame.modules.business.domain.vo.MaterialsLibraryVO;
import com.huatek.frame.modules.business.mapper.MaterialsLibraryMapper;
import com.huatek.frame.modules.business.service.MaterialsLibraryService;
import com.huatek.frame.modules.business.service.dto.MaterialsLibraryDTO;
import java.sql.Date;
import org.springframework.util.CollectionUtils;



/**
 * 材料库 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "materialsLibrary")
//@RefreshScope
@Slf4j
public class MaterialsLibraryServiceImpl implements MaterialsLibraryService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private MaterialsLibraryMapper materialsLibraryMapper;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public MaterialsLibraryServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<MaterialsLibraryVO>> findMaterialsLibraryPage(MaterialsLibraryDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<MaterialsLibraryVO> materialsLibrarys = materialsLibraryMapper.selectMaterialsLibraryPage(dto);
		TorchResponse<List<MaterialsLibraryVO>> response = new TorchResponse<List<MaterialsLibraryVO>>();
		response.getData().setData(materialsLibrarys);
		response.setStatus(200);
		response.getData().setCount(materialsLibrarys.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(MaterialsLibraryDTO materialsLibraryDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(materialsLibraryDto.getCodexTorchDeleted())) {
            materialsLibraryDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = materialsLibraryDto.getId();
		MaterialsLibrary entity = new MaterialsLibrary();
        BeanUtils.copyProperties(materialsLibraryDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			materialsLibraryMapper.insert(entity);
		} else {
			materialsLibraryMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        MaterialsLibraryVO vo = new MaterialsLibraryVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<MaterialsLibraryVO> findMaterialsLibrary(String id) {
		MaterialsLibraryVO vo = new MaterialsLibraryVO();
		if (!HuatekTools.isEmpty(id)) {
			MaterialsLibrary entity = materialsLibraryMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<MaterialsLibraryVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<MaterialsLibrary> materialsLibraryList = materialsLibraryMapper.selectBatchIds(Arrays.asList(ids));
        for (MaterialsLibrary materialsLibrary : materialsLibraryList) {
            materialsLibrary.setCodexTorchDeleted(Constant.DEFAULT_YES);
            materialsLibraryMapper.updateById(materialsLibrary);
        }
		//materialsLibraryMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "materials_library", convertorFields = "")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<MaterialsLibraryVO> selectMaterialsLibraryList(MaterialsLibraryDTO dto) {
        return materialsLibraryMapper.selectMaterialsLibraryList(dto);
    }

    /**
     * 导入材料库数据
     *
     * @param materialsLibraryList 材料库数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "materials_library", convertorFields = "")
    public TorchResponse importMaterialsLibrary(List<MaterialsLibraryVO> materialsLibraryList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(materialsLibraryList) || materialsLibraryList.size() == 0) {
            throw new ServiceException("导入材料库数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (MaterialsLibraryVO vo : materialsLibraryList) {
            try {
                MaterialsLibrary materialsLibrary = new MaterialsLibrary();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, materialsLibrary);
                QueryWrapper<MaterialsLibrary> wrapper = new QueryWrapper();
                MaterialsLibrary oldMaterialsLibrary = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = MaterialsLibraryVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<MaterialsLibrary> oldMaterialsLibraryList = materialsLibraryMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldMaterialsLibraryList) && oldMaterialsLibraryList.size() > 1) {
                        materialsLibraryMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldMaterialsLibraryList) && oldMaterialsLibraryList.size() == 1) {
                        oldMaterialsLibrary = oldMaterialsLibraryList.get(0);
                    }
                }
                if (StringUtils.isNull(oldMaterialsLibrary)) {
                    BeanValidators.validateWithException(validator, vo);
                    materialsLibraryMapper.insert(materialsLibrary);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、物料代码 " + vo.getMaterialCode() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldMaterialsLibrary, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    materialsLibraryMapper.updateById(oldMaterialsLibrary);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、物料代码 " + vo.getMaterialCode() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料代码 " + vo.getMaterialCode() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、物料代码 " + vo.getMaterialCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(MaterialsLibraryVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getMaterialCode())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>物料代码不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getMaterialName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>物料名称不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getSpecificationModel())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>规格型号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getInventoryQuantity())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>库存数量不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getQuantityOnOrderForPurchase())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>采购在订量不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getAvailableStockQuantity())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>可用库存量不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getBasicUnitName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>基本单位名称不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectMaterialsLibraryListByIds(List<String> ids) {
        List<MaterialsLibraryVO> materialsLibraryList = materialsLibraryMapper.selectMaterialsLibraryListByIds(ids);

		TorchResponse<List<MaterialsLibraryVO>> response = new TorchResponse<List<MaterialsLibraryVO>>();
		response.getData().setData(materialsLibraryList);
		response.setStatus(200);
		response.getData().setCount((long)materialsLibraryList.size());
		return response;
    }



}
