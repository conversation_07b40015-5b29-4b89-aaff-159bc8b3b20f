package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description 产值报表VO实体类
* <AUTHOR>
* @date 2025-08-28
**/
@Data
@ApiModel("产值报表DTO实体类")
public class OutputValueReportVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 结算单位
     **/
    @ApiModelProperty("结算单位")
    @Excel(name = "结算单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String settlementUnit;

    /**
	 * 年度
     * eg.2025
     **/
    @ApiModelProperty("年度")
    @Excel(name = "年度",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String annual;

    /**
	 * 月份
     * eg.01 02 03 ~12
     **/
    @ApiModelProperty("月份")
    @Excel(name = "月份",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String month;

    /**
	 * 内部核算金额
     **/
    @ApiModelProperty("内部核算金额")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "内部核算金额",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal internalAccountingAmount;

    /**
	 * 客户对账价格
     **/
    @ApiModelProperty("客户对账价格")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "客户对账价格",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal customerReconciliationPrice;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}