package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 文件评审DTO 实体类
* <AUTHOR>
* @date 2025-08-20
**/
@Data
@ApiModel("文件评审DTO实体类")
public class FileReviewDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    private String[] ids;
    /**
	 * 生产工单
     **/
    @ApiModelProperty("生产工单")
    private String workOrder;
    
    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    private String productName;
    
    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    private String productModel;
    
    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    
    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    private String productCategory;
    
    /**
	 * 标准规范号
     **/
    @ApiModelProperty("标准规范号")
    private String standardSpecificationNumber;
    
    /**
	 * 关联异常反馈编号
     **/
    @ApiModelProperty("关联异常反馈编号")
    private String assocExceptionFeedbackNum;
    
    /**
	 * 变更文件
     **/
    @ApiModelProperty("变更文件")
    private String changeFile;
    
    /**
	 * 评审结果
     **/
    @ApiModelProperty("评审结果")
    private String reviewResult;
    
    /**
	 * 评审备注
     **/
    @ApiModelProperty("评审备注")
    private String reviewRemark;
    
    /**
	 * 评审人
     **/
    @ApiModelProperty("评审人")
    private String reviewer;
    
    /**
	 * 评审时间
     **/
    @ApiModelProperty("评审时间")
    private String reviewTime;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;


}