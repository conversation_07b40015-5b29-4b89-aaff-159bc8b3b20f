package com.huatek.frame.modules.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.SysUser;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.domain.vo.SysUserVO;
import com.huatek.frame.modules.system.mapper.SysGroupMapper;
import com.huatek.frame.modules.system.mapper.SysUserMapper;
import com.huatek.frame.modules.system.service.SysGroupService;
import com.huatek.frame.modules.system.service.dto.SysGroupDTO;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 系统_组织 ServiceImpl
 *
 * <AUTHOR>
 * @date 2018-7-11 14:03:46
 */
@DubboService

//@CacheConfig(cacheNames = "group")
public class SysGroupServiceImpl implements SysGroupService {

    private static final String GROUPPARENTID = "0";

    @Autowired
    private SysGroupMapper sysGroupMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
//	@Cacheable(keyGenerator = "keyGenerator")
    public TorchResponse<List<SysGroupVO>> findGroupPage(SysGroupDTO dto) {
        Map<String, Object> map = Maps.newConcurrentMap();
        map.put("deleted", Constant.DEFAULT_NO);
        map.put("groupParentId", GROUPPARENTID);
        List<SysGroupVO> vos = sysGroupMapper.getParentGroups(map);
        TorchResponse<List<SysGroupVO>> response = new TorchResponse<List<SysGroupVO>>();
        response.getData().setData(vos);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @SuppressWarnings("rawtypes")
    @Override
//	@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveOrUpdate(SysGroup sysGroup) {
        String id = sysGroup.getId();
        List<SysGroup> lists = null;
        if (HuatekTools.isEmpty(id)) {
            lists = sysGroupMapper.selectList(new QueryWrapper<SysGroup>().eq("deleted", Constant.DEFAULT_NO).eq("group_code",
                    sysGroup.getGroupCode()));
            if (!ObjectUtils.isEmpty(lists)) {
                throw new ServiceException("组织编码重复");
            }
            lists = sysGroupMapper.selectList(new QueryWrapper<SysGroup>().eq("deleted", Constant.DEFAULT_NO).eq("group_name",
                    sysGroup.getGroupName()));
            if (!ObjectUtils.isEmpty(lists)) {
                throw new ServiceException("组织名称重复");
            }
            SysGroup parentSysGroup = sysGroupMapper.selectById(sysGroup.getGroupParentId());
            if (ObjectUtils.isEmpty(parentSysGroup)) {
                sysGroup.setAncestors(sysGroup.getGroupParentId());
            } else {
                sysGroup.setAncestors(parentSysGroup.getAncestors() + "," + sysGroup.getGroupParentId());
            }
            sysGroup.setDeleted(Constant.DEFAULT_NO);
            sysGroupMapper.insert(sysGroup);
        } else {
            lists = sysGroupMapper.selectList(new QueryWrapper<SysGroup>().eq("deleted", Constant.DEFAULT_NO)
                    .ne("id", sysGroup.getId()).eq("group_code", sysGroup.getGroupCode()));
            if (!ObjectUtils.isEmpty(lists)) {
                throw new ServiceException("组织编码重复");
            }
            lists = sysGroupMapper.selectList(new QueryWrapper<SysGroup>().eq("deleted", Constant.DEFAULT_NO)
                    .ne("id", sysGroup.getId()).eq("group_name", sysGroup.getGroupName()));
            if (!ObjectUtils.isEmpty(lists)) {
                throw new ServiceException("组织名称重复");
            }
            SysGroup parentSysGroup = sysGroupMapper.selectById(sysGroup.getGroupParentId());
            if (ObjectUtils.isEmpty(parentSysGroup)) {
                sysGroup.setAncestors(sysGroup.getGroupParentId());
            } else {
                sysGroup.setAncestors(parentSysGroup.getAncestors() + "," + sysGroup.getGroupParentId());
            }
            sysGroupMapper.updateById(sysGroup);
        }
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
//	@Cacheable(key = "#p0")
    public TorchResponse<SysGroupVO> findGroup(String id) {
        SysGroupVO vo = new SysGroupVO();
        if (!HuatekTools.isEmpty(id)) {
            SysGroup entity = sysGroupMapper.selectById(id);
            BeanUtils.copyProperties(entity, vo);
        }
        Map<String, Object> map = Maps.newConcurrentMap();
        map.put("deleted", Constant.DEFAULT_NO);
        map.put("groupParentId", GROUPPARENTID);
        List<SysGroupVO> datas = sysGroupMapper.getParentGroups(map);
        vo.setChildren(datas);
        TorchResponse<SysGroupVO> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(vo);
        return response;
    }

    @SuppressWarnings("rawtypes")
    @Override
//	@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse delete(String[] ids) {
        for (String id : ids) {
            List<SysGroup> lists = sysGroupMapper
                    .selectList(new QueryWrapper<SysGroup>().eq("deleted", Constant.DEFAULT_NO).eq("group_parent_id", id));
            if (!ObjectUtils.isEmpty(lists)) {
                SysGroup sysGroup = sysGroupMapper.selectById(id);
                throw new ServiceException("[" + sysGroup.getGroupName() + "]组织存在下级组织，请先取消与下级组织的关联");
            }
            List<SysUser> isSysUser = sysUserMapper
                    .selectList(new QueryWrapper<SysUser>().eq("deleted", Constant.DEFAULT_NO).eq("group_id", id));
            if (!ObjectUtils.isEmpty(isSysUser)) {
                SysGroup sysGroup = sysGroupMapper.selectById(id);
                throw new ServiceException("[" + sysGroup.getGroupName() + "]组织下被用户引用，先解除关系");
            }
            SysGroup sysGroup = new SysGroup();
            sysGroup.setDeleted(Constant.DEFAULT_YES);
            sysGroupMapper.update(sysGroup, new QueryWrapper<SysGroup>().eq("id", id));
        }
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse findGroupsCascade() {
        Map<String, Object> map = Maps.newConcurrentMap();
        map.put("deleted", Constant.DEFAULT_NO);
        map.put("groupParentId", GROUPPARENTID);
        List<SysGroupVO> sysGroupVOList = sysGroupMapper.getParentGroups(map);

        List<CascadeOptionsVO> cascadeOptionsVOS = buildSelectOptions(sysGroupVOList);
        TorchResponse<List<CascadeOptionsVO>> response = new TorchResponse<List<CascadeOptionsVO>>();
        response.getData().setData(cascadeOptionsVOS);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse findCascadeDataByLabelName(String labelName) {
        if (HuatekTools.isEmpty(labelName)) {
            return findGroupsCascade();
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("groupParentId", GROUPPARENTID);
        map.put("deleted", Constant.DEFAULT_NO);
        List<SysGroupVO> vos = sysGroupMapper.getParentGroups(map);
        List<CascadeOptionsVO> cascadeOptionsVOS = buildSelectOptions(vos, labelName);
        TorchResponse<List<CascadeOptionsVO>> response = new TorchResponse<List<CascadeOptionsVO>>();
        response.getData().setData(cascadeOptionsVOS);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    /**
     * 根据部门id获取用户
     * @param groupId
     * @return
     */
    @Override
    public TorchResponse findUserByGroupId(String groupId) {
        List<SysUserVO> userList = sysUserMapper.findUserByGroupId(groupId);
        TorchResponse<List<SysUserVO>> response = new TorchResponse<>();
        response.getData().setData(userList);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public SysGroup findGroupsByCode(String groupCode) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("group_code",groupCode);
        SysGroup gtoup = sysGroupMapper.selectOne(wrapper);
        return gtoup;
    }

    private List<CascadeOptionsVO> buildSelectOptions(List<SysGroupVO> vos, String labelColumnName) {
		List<CascadeOptionsVO> cascadeOptionsVOS = new ArrayList<>();
		if (!CollectionUtils.isEmpty(vos)) {
			vos.forEach(organizationalManagementVO -> {
				JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(organizationalManagementVO));
				CascadeOptionsVO cascadeOptionsVO = new CascadeOptionsVO();
				if (jsonObject.containsKey(labelColumnName)) {
					cascadeOptionsVO.setLabel(jsonObject.getString(labelColumnName));
				} else {

					cascadeOptionsVO.setLabel(organizationalManagementVO.getGroupCode());
				}
				cascadeOptionsVO.setValue(organizationalManagementVO.getGroupCode());
				cascadeOptionsVO.setChildren(buildSelectOptions(organizationalManagementVO.getChildren(), labelColumnName));
				cascadeOptionsVOS.add(cascadeOptionsVO);
			});
		}
		return cascadeOptionsVOS;
	}

    private List<CascadeOptionsVO> buildSelectOptions(List<SysGroupVO> vos) {
        List<CascadeOptionsVO> cascadeOptionsVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(vos)) {
            vos.forEach(groupVO -> {
                CascadeOptionsVO cascadeOptionsVO = new CascadeOptionsVO();
                cascadeOptionsVO.setLabel(groupVO.getGroupName());
                cascadeOptionsVO.setValue(groupVO.getId());
                cascadeOptionsVO.setChildren(buildSelectOptions(groupVO.getChildren()));
                cascadeOptionsVOS.add(cascadeOptionsVO);
            });
        }
        return cascadeOptionsVOS;
    }
    @Override
    public SysGroup selectById(String id) {
        return sysGroupMapper.selectById(id);
    }
    //根据sys_group的id 查询当前和它的所有上级group_code 
    @Override
    public List<String> selectGroupCodeByGroupId(String groupId) {
        return sysGroupMapper.selectGroupCodeByGroupId(groupId);
    }
}

