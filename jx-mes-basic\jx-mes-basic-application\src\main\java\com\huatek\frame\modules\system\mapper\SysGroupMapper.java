package com.huatek.frame.modules.system.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;

/**
 * 系统_组织 Mapper
 *
 * <AUTHOR>
 * @date 2018-7-11 14:03:45
 */

public interface SysGroupMapper extends BaseMapper<SysGroup>{
	
	/**
	 * 组织集合--树形
	 * @param map 
	 * @return
	 */
	List<SysGroupVO> getParentGroups(Map<String, Object> map);
	
	/**
	 * 根据组织ID查询当前和所有上级的group_code
	 * @param groupId
	 * @return
	 */
	List<String> selectGroupCodeByGroupId(String groupId);
}

