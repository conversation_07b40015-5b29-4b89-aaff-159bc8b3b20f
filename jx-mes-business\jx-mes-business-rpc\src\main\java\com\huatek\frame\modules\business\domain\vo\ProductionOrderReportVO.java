package com.huatek.frame.modules.business.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
* @description 生成报告VO实体类
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("生成报告VO实体类")
public class ProductionOrderReportVO implements Serializable {

	private static final long serialVersionUID = 1L;


    /**
     * 工单编号
     **/
    private String workOrderNumber;

    /**
     * 委托单位
     **/
    private String entrustedUnit;
    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 生产批次
     **/
    private String productionBatch;


    /**
     * 数量
     **/
    private Integer quantity;


    /**
     * 产品名称
     **/
    private String productName;
    /**
     * 生产厂家
     **/
    private String manufacturer;


    /**
     * 工程代码
     **/
    private String engineeringCode;

    /**
     * 质量等级
     **/
    private String qualityGrade;
    /**
     * 联系人
     **/
    private String contact;

    /**
     * 电话
     **/
    private String phone;
    /**
     * 委托日期
     */
    private String entrustmentDate;
    /**
     * 筛选依据
     */
    private String screeningBasis;
    /**
     * 完成日期
     */
    private String completionDate;

    /**
     * 试验项目
     */
    List<ProductionTaskVO> experimentProjects;

    /**
     * 设备列表
     */
    List<EquipmentInventoryVO> equipments;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合格数量
     **/
    private Integer qualifiedQuantity;

    /**
     * 不合格数量
     **/
    private Integer unqualifiedQuantity;
    /**
     * 不合格率
     */
    private BigDecimal defectiveRate;
    /**
     * 编制人
     */
    private String preparedBy;
    /**
     * 编制时间
     */
    private String preparedDate;
    /**
     * 审核人
     */
    private String reviewer;

    /**
     * 审核时间
     */
    private String reviewDate;
    /**
     * 批准人
     */
    private String approver;
    /**
     * 批准时间
     */
    private String approveDate;
}