package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 生产工单相关信息VO实体
 */
@Data
public class ProductionOrderInfoVO {
    /**
     * 工单ID
     **/
    @ApiModelProperty("工单ID")
    @Excel(name = "工单ID",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String orderId;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderNumber;

    /**
     * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productModel;

    /**
     * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productName;

    /**
     * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productCategory;

    /**
     * 生产批次
     **/
    @ApiModelProperty("生产批次")
    @Excel(name = "生产批次",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productionBatch;

    /**
     * 标准规范编号
     */
    @Excel(name = "标准规范编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String specificationNumber;

    /**
     * 生产批次
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String manufacturer;

    /**
     * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testType;

    /**
     * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String orderNumber;

    /**
     * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String entrustedUnit;

//    /**
//     * 试验依据
//     **/
//    @ApiModelProperty("试验依据")
//    @Excel(name = "试验依据",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
//    private String testBasis;
}
