package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 板卡存放VO实体类
* <AUTHOR>
* @date 2025-08-04
**/
@Data
@ApiModel("板卡存放DTO实体类")
public class CardinventoryVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * DUT板名称
     **/
    @ApiModelProperty("DUT板名称")
    @Excel(name = "DUT板名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String dutBoardName;

    /**
	 * DUT板编号
     **/
    @ApiModelProperty("DUT板编号")
    @Excel(name = "DUT板编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String dutBoardNumber;

    /**
	 * DUT板类型
     **/
    @ApiModelProperty("DUT板类型")
    @Excel(name = "DUT板类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String dutBoardType;

    /**
	 * 每板能力
     **/
    @ApiModelProperty("每板能力")
    @Excel(name = "每板能力",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String boardCapacity;

    /**
	 * 存放位置
     **/
    @ApiModelProperty("存放位置")
    @Excel(name = "存放位置",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String storageLocation;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}