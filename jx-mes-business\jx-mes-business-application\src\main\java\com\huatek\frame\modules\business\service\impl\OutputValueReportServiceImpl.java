package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.business.domain.vo.OutputValueDashboardVO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.OutputValueReport;
import com.huatek.frame.modules.business.domain.vo.OutputValueReportVO;
import com.huatek.frame.modules.business.mapper.OutputValueReportMapper;
import com.huatek.frame.modules.business.service.OutputValueReportService;
import com.huatek.frame.modules.business.service.dto.OutputValueReportDTO;
import java.sql.Date;
import org.springframework.util.CollectionUtils;



/**
 * 产值报表 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "outputValueReport")
//@RefreshScope
@Slf4j
public class OutputValueReportServiceImpl implements OutputValueReportService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private OutputValueReportMapper outputValueReportMapper;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public OutputValueReportServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<OutputValueReportVO>> findOutputValueReportPage(OutputValueReportDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<OutputValueReportVO> outputValueReports = outputValueReportMapper.selectOutputValueReportPage(dto);
		TorchResponse<List<OutputValueReportVO>> response = new TorchResponse<List<OutputValueReportVO>>();
		response.getData().setData(outputValueReports);
		response.setStatus(200);
		response.getData().setCount(outputValueReports.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(OutputValueReportDTO outputValueReportDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(outputValueReportDto.getCodexTorchDeleted())) {
            outputValueReportDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = outputValueReportDto.getId();
		OutputValueReport entity = new OutputValueReport();
        BeanUtils.copyProperties(outputValueReportDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			outputValueReportMapper.insert(entity);
		} else {
			outputValueReportMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        OutputValueReportVO vo = new OutputValueReportVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<OutputValueReportVO> findOutputValueReport(String id) {
		OutputValueReportVO vo = new OutputValueReportVO();
		if (!HuatekTools.isEmpty(id)) {
			OutputValueReport entity = outputValueReportMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<OutputValueReportVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<OutputValueReport> outputValueReportList = outputValueReportMapper.selectBatchIds(Arrays.asList(ids));
        for (OutputValueReport outputValueReport : outputValueReportList) {
            outputValueReport.setCodexTorchDeleted(Constant.DEFAULT_YES);
            outputValueReportMapper.updateById(outputValueReport);
        }
		//outputValueReportMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "output_value_report", convertorFields = "")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<OutputValueReportVO> selectOutputValueReportList(OutputValueReportDTO dto) {
        return outputValueReportMapper.selectOutputValueReportList(dto);
    }

    /**
     * 导入产值报表数据
     *
     * @param outputValueReportList 产值报表数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "output_value_report", convertorFields = "")
    public TorchResponse importOutputValueReport(List<OutputValueReportVO> outputValueReportList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(outputValueReportList) || outputValueReportList.size() == 0) {
            throw new ServiceException("导入产值报表数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (OutputValueReportVO vo : outputValueReportList) {
            try {
                OutputValueReport outputValueReport = new OutputValueReport();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, outputValueReport);
                QueryWrapper<OutputValueReport> wrapper = new QueryWrapper();
                OutputValueReport oldOutputValueReport = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = OutputValueReportVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<OutputValueReport> oldOutputValueReportList = outputValueReportMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldOutputValueReportList) && oldOutputValueReportList.size() > 1) {
                        outputValueReportMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldOutputValueReportList) && oldOutputValueReportList.size() == 1) {
                        oldOutputValueReport = oldOutputValueReportList.get(0);
                    }
                }
                if (StringUtils.isNull(oldOutputValueReport)) {
                    BeanValidators.validateWithException(validator, vo);
                    outputValueReportMapper.insert(outputValueReport);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、结算单位 " + vo.getSettlementUnit() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldOutputValueReport, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    outputValueReportMapper.updateById(oldOutputValueReport);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、结算单位 " + vo.getSettlementUnit() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、结算单位 " + vo.getSettlementUnit() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、结算单位 " + vo.getSettlementUnit() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(OutputValueReportVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectOutputValueReportListByIds(List<String> ids) {
        List<OutputValueReportVO> outputValueReportList = outputValueReportMapper.selectOutputValueReportListByIds(ids);

		TorchResponse<List<OutputValueReportVO>> response = new TorchResponse<List<OutputValueReportVO>>();
		response.getData().setData(outputValueReportList);
		response.setStatus(200);
		response.getData().setCount((long)outputValueReportList.size());
		return response;
    }

    @Override
    public TorchResponse getDashboardStats() {
        TorchResponse response = new TorchResponse();
        try {
            OutputValueDashboardVO stats = outputValueReportMapper.selectDashboardStats();
            response.setStatus(Constant.REQUEST_SUCCESS);
            response.getData().setData(stats);
        } catch (Exception e) {
            log.error("Get dashboard stats error", e);
            response.setStatus(Constant.BAD_REQUEST);
            response.setMessage("获取统计数据失败：" + e.getMessage());
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse generateOutputVal() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(System.currentTimeMillis()));
        String year = String.valueOf(calendar.get(Calendar.YEAR));
        String month = String.format("%02d", calendar.get(Calendar.MONTH) + 1);

        ProductionValueCalculationDTO queryDto = new ProductionValueCalculationDTO();
        queryDto.setStatus(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES);

        
        TorchResponse response = new TorchResponse();
        
        try {
            List<ProductionValueCalculationDTO> calculationList = outputValueReportMapper.selectProductionValueCalculations(queryDto);
            
            if (calculationList == null || calculationList.isEmpty()) {
                response.setStatus(Constant.BAD_REQUEST);
                response.setMessage("未找到已对账的产值计算数据");
                return response;
            }

            QueryWrapper<OutputValueReport> wrapper = new QueryWrapper<>();
            wrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
            wrapper.eq("annual", year);
            wrapper.eq("month", month);
            outputValueReportMapper.delete(wrapper);

            Map<String, SettlementStats> settlementStatsMap = new HashMap<>();
            
            for (ProductionValueCalculationDTO calc : calculationList) {
                String settlementUnit = calc.getSettlementUnit();
                SettlementStats stats = settlementStatsMap.computeIfAbsent(
                    settlementUnit, 
                    k -> new SettlementStats()
                );
                
                // 累加内部结算金额和客户对账金额
                stats.addInternalAmount(calc.getInternalAccountingPrice());
                stats.addCustomerAmount(calc.getCustomerAccountingPrice());
            }

            for (Map.Entry<String, SettlementStats> entry : settlementStatsMap.entrySet()) {
                String settlementUnit = entry.getKey();
                SettlementStats stats = entry.getValue();
                
                OutputValueReport report = new OutputValueReport();

                report.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
                report.setCodexTorchDeleted(Constant.DEFAULT_NO);
                report.setAnnual(year);
                report.setMonth(month);
                report.setSettlementUnit(settlementUnit);
                report.setInternalAccountingAmount(stats.getTotalInternalAmount());    // 内部结算总金额
                report.setCustomerReconciliationPrice(stats.getTotalCustomerAmount());    // 客户对账总金额
                
                outputValueReportMapper.insert(report);
            }
            
            response.setStatus(Constant.REQUEST_SUCCESS);
            response.setMessage("产值报表生成成功");
            return response;
            
        } catch (Exception e) {
            log.error("Generate output value report error", e);
            throw new ServiceException("生成产值报表失败：" + e.getMessage());
        }
    }

    /**
     * 结算单位统计数据辅助类
     */
    private static class SettlementStats {
        private BigDecimal totalInternalAmount = BigDecimal.ZERO;    // 内部结算总金额
        private BigDecimal totalCustomerAmount = BigDecimal.ZERO;    // 客户对账总金额

        public void addInternalAmount(BigDecimal amount) {
            if (amount != null) {
                totalInternalAmount = totalInternalAmount.add(amount);
            }
        }

        public void addCustomerAmount(BigDecimal amount) {
            if (amount != null) {
                totalCustomerAmount = totalCustomerAmount.add(amount);
            }
        }

        public BigDecimal getTotalInternalAmount() {
            return totalInternalAmount;
        }

        public BigDecimal getTotalCustomerAmount() {
            return totalCustomerAmount;
        }
    }



}
