<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.CapabilityVerificationMapper">
	<sql id="Base_Column_List">
		t.id as id,
        t.customer_infomation_id as customerInfomationId,
		t.capability_verification_number as capabilityVerificationNumber,
		t.confirmation_of_capability as confirmationOfCapability,
		t.product_model as productModel,
		t.product_name as productName,
		t.manufacturer as manufacturer,
		t.product_information as productInformation,
		t.status as status,
		t.verification_result as verificationResult,
		t.`comment` as `comment`,
		t.capability_feedback as capabilityFeedback,
		t.feedbacker as feedbacker,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
    <update id="batchUpdateCapabilityVerification">
        UPDATE capability_verification t
        SET
            t.customer_infomation_id = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.customerInfomationId}
                </foreach>
            else t.customer_infomation_id
            end,
            t.capability_verification_number = case t.id
            <foreach collection="param" item="item">
                when #{item.id} then #{item.capabilityVerificationNumber}
            </foreach>
            else t.capability_verification_number
            end,
            t.confirmation_of_capability = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.confirmationOfCapability}
                </foreach>
            else t.confirmation_of_capability
            end,
            t.product_model = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.productModel}
                </foreach>
            else t.product_model
            end,
            t.product_name = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.productName}
                </foreach>
            else t.product_name
            end,
            t.manufacturer = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.manufacturer}
                </foreach>
            else t.manufacturer
            end,
            t.product_information = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.productInformation}
                </foreach>
            else t.product_information
            end,
            t.status = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.status}
                </foreach>
            else t.status
            end,
            t.verification_result = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.verificationResult}
                </foreach>
            else t.verification_result
            end,
            t.comment = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.comment}
                </foreach>
            else t.comment
            end,
            t.capability_feedback = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.capabilityFeedback}
                </foreach>
            else t.capability_feedback
            end,
            t.codex_torch_creator_id = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then  #{item.codexTorchCreatorId}
                </foreach>
            else t.codex_torch_creator_id
            end,
            t.codex_torch_updater = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then  #{item.codexTorchUpdater}
                </foreach>
            else t.codex_torch_updater
            end,
            t.codex_torch_group_id = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then  #{item.codexTorchGroupId}
                </foreach>
            else t.codex_torch_group_id
            end,
            t.codex_torch_update_datetime = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.codexTorchUpdateDatetime}
                </foreach>
            else t.codex_torch_update_datetime
            end,
            t.codex_torch_deleted = case t.id
                <foreach collection="param" item="item">
                    when #{item.id} then #{item.codexTorchDeleted}
                </foreach>
            else t.codex_torch_deleted
            end

        WHERE t.id in (
            <foreach collection="param" item="item" separator=",">
                #{item.id}
            </foreach>
        )
    </update>

    <select id="selectCapabilityVerificationPage" parameterType="com.huatek.frame.modules.business.service.dto.CapabilityVerificationDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.CapabilityVerificationVO">
		select
		<include refid="Base_Column_List" />, c.entrusted_unit as entrustedUnit
			from capability_verification t
            LEFT JOIN customer_information_management c ON c.id = t.customer_infomation_id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="customerInfomationId != null and customerInfomationId != ''">
                    and t.customer_infomation_id  = #{customerInfomationId}
                </if>
                <if test="capabilityVerificationNumber != null and capabilityVerificationNumber != ''">
                    and t.capability_verification_number  like concat('%', #{capabilityVerificationNumber} ,'%')
                </if>
                <if test="confirmationOfCapability != null and confirmationOfCapability != ''">
                    and t.confirmation_of_capability  = #{confirmationOfCapability}
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="productInformation != null and productInformation != ''">
                    and t.product_information  = #{productInformation}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="verificationResult != null and verificationResult != ''">
                    and t.verification_result  = #{verificationResult}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and c.id =  #{entrustedUnit}
                </if>
                <if test="capabilityFeedback != null and capabilityFeedback != ''">
                    and t.capability_feedback  like concat('%', #{capabilityFeedback} ,'%')
                </if>
                <if test="feedbacker != null and feedbacker != ''">
                    and t.feedbacker  like concat('%', #{feedbacker} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
        ORDER BY t.CODEX_TORCH_CREATE_DATETIME DESC
	</select>
     <select id="selectOptionsByEntrustedUnit" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.entrusted_unit label,
        	t.id value
        from customer_information_management t
        WHERE t.entrusted_unit != ''
         and t.codex_torch_deleted = '0'
     </select>

    <select id="selectCapabilityVerificationList" parameterType="com.huatek.frame.modules.business.service.dto.CapabilityVerificationDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.CapabilityVerificationVO">
		select
		<include refid="Base_Column_List" />
			from capability_verification t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="customerInfomationId != null and customerInfomationId != ''">
                    and t.customer_infomation_id  = #{customerInfomationId}
                </if>
                <if test="capabilityVerificationNumber != null and capabilityVerificationNumber != ''">
                    and t.capability_verification_number  like concat('%', #{capabilityVerificationNumber} ,'%')
                </if>
                <if test="confirmationOfCapability != null and confirmationOfCapability != ''">
                    and t.confirmation_of_capability  = #{confirmationOfCapability}
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="productInformation != null and productInformation != ''">
                    and t.product_information  = #{productInformation}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="verificationResult != null and verificationResult != ''">
                    and t.verification_result  = #{verificationResult}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="capabilityFeedback != null and capabilityFeedback != ''">
                    and t.capability_feedback  like concat('%', #{capabilityFeedback} ,'%')
                </if>
                <if test="feedbacker != null and feedbacker != ''">
                    and t.feedbacker  like concat('%', #{feedbacker} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectCapabilityVerificationListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.CapabilityVerificationVO">
		select
		<include refid="Base_Column_List" />, c.entrusted_unit as entrustedUnit
			from capability_verification t
            LEFT JOIN customer_information_management c ON c.id = t.customer_infomation_id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
    <select id="findCapabilityVerification"
            resultType="com.huatek.frame.modules.business.domain.vo.CapabilityVerificationVO">
        select
        <include refid="Base_Column_List" />, c.entrusted_unit as entrustedUnit
        from capability_verification t
        LEFT JOIN customer_information_management c ON c.id = t.customer_infomation_id
        where t.id = #{id}
    </select>
</mapper>