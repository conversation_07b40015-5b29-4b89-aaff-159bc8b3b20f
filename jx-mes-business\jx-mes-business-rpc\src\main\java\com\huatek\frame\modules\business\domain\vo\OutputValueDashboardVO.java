package com.huatek.frame.modules.business.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.io.Serializable;

/**
 * 产值统计看板VO
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
@ApiModel("产值统计看板VO")
public class OutputValueDashboardVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("本月内部核算额")
    private BigDecimal monthInternalAmount;

    @ApiModelProperty("本月客户对账金额")
    private BigDecimal monthCustomerAmount;

    @ApiModelProperty("本年度内部核算累计总额")
    private BigDecimal yearInternalTotalAmount;

    @ApiModelProperty("本年度客户对账累计总额")
    private BigDecimal yearCustomerTotalAmount;
}
