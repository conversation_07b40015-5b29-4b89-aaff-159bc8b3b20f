package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.WorkReportTableVO;
import com.huatek.frame.modules.business.service.WorkReportTableService;
import com.huatek.frame.modules.business.service.dto.WorkReportTableDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-18
**/
@Api(tags = "工时报表管理")
@RestController
@RequestMapping("/api/workReportTable")
public class WorkReportTableController {

	@Autowired
    private WorkReportTableService workReportTableService;

	/**
	 * 工时报表列表
	 * 
	 * @param dto 工时报表DTO 实体对象
	 * @return
	 */
    @Log("工时报表列表")
    @ApiOperation(value = "工时报表列表查询")
    @PostMapping(value = "/workReportTableList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("workReportTable:list")
    public TorchResponse<List<WorkReportTableVO>> query(@RequestBody WorkReportTableDTO dto){
        return workReportTableService.findWorkReportTablePage(dto);
    }
//
//	/**
//	 * 新增/修改工时报表
//	 *
//	 * @param workReportTableDto 工时报表DTO实体对象
//	 * @return
//	 * @throws Exception
//	 */
//    @SuppressWarnings("rawtypes")
//    @Log("新增/修改工时报表")
//    @ApiOperation(value = "工时报表新增/修改操作")
//    @PostMapping(value = "/workReportTable", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("workReportTable:add#workReportTable:edit")
//    public TorchResponse add(@RequestBody WorkReportTableDTO workReportTableDto) throws Exception {
//		// BeanValidatorFactory.validate(workReportTableDto);
//		return workReportTableService.saveOrUpdate(workReportTableDto);
//	}
//
//	/**
//	 * 查询工时报表详情
//	 *
//	 * @param id 主键id
//	 * @return
//	 */
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//    @Log("工时报表详情")
//    @ApiOperation(value = "工时报表详情查询")
//    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("workReportTable:detail")
//	public TorchResponse detail(@PathVariable(value = "id") String id) {
//		return workReportTableService.findWorkReportTable(id);
//	}
//
//	/**
//	 * 删除工时报表
//	 *
//	 * @param ids
//	 * @return
//	 */
//	@SuppressWarnings("rawtypes")
//    @Log("删除工时报表")
//    @ApiOperation(value = "工时报表删除操作")
//    @TorchPerm("workReportTable:del")
//    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
//	public TorchResponse delete(@RequestBody String[] ids) {
//		return workReportTableService.delete(ids);
//	}
//
//    @ApiOperation(value = "工时报表联动选项值查询")
//    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
//	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
//		return workReportTableService.getOptionsList(id);
//	}


    @Log("工时报表生成")
    @ApiOperation(value = "工时报表生成")
    @TorchPerm("workReportTable:add")
    @PostMapping("/generateWorkReportTable")
    public TorchResponse generateWorkReportTable()
    {
        return workReportTableService.generateWorkReportTable();
    }


    @Log("工时报表导出")
    @ApiOperation(value = "工时报表导出")
    @TorchPerm("workReportTable:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody WorkReportTableDTO dto)
    {
        List<WorkReportTableVO> list = workReportTableService.selectWorkReportTableList(dto);
        ExcelUtil<WorkReportTableVO> util = new ExcelUtil<WorkReportTableVO>(WorkReportTableVO.class);
        util.exportExcel(response, list, "工时报表数据");
    }
//
//    @Log("工时报表导入")
//    @ApiOperation(value = "工时报表导入")
//    @TorchPerm("workReportTable:import")
//    @PostMapping("/importData")
//    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
//    {
//        ExcelUtil<WorkReportTableVO> util = new ExcelUtil<WorkReportTableVO>(WorkReportTableVO.class);
//        List<WorkReportTableVO> list = util.importExcel(file.getInputStream());
//        return workReportTableService.importWorkReportTable(list, unionColumns, true, "");
//    }
//
//    @Log("工时报表导入模板")
//    @ApiOperation(value = "工时报表导入模板下载")
//    @PostMapping("/importTemplate")
//    public void importTemplate(HttpServletResponse response) throws IOException
//    {
//        ExcelUtil<WorkReportTableVO> util = new ExcelUtil<WorkReportTableVO>(WorkReportTableVO.class);
//        util.importTemplateExcel(response, "工时报表数据");
//    }
//
//    @Log("根据Ids获取工时报表列表")
//    @ApiOperation(value = "工时报表 根据Ids批量查询")
//    @PostMapping(value = "/workReportTableList/ids", produces = {"application/json;charset=utf-8"})
//    public TorchResponse getWorkReportTableListByIds(@RequestBody List<String> ids) {
//        return workReportTableService.selectWorkReportTableListByIds(ids);
//    }




}