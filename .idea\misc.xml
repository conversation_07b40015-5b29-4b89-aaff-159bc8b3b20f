<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/../JXMES_2025_07_01_15_24_07__sourceCode/j-x-m-e-s-basic/pom.xml" />
        <option value="$PROJECT_DIR$/huatek-torch-job/pom.xml" />
        <option value="$PROJECT_DIR$/huatek-torch-job/torch-job-admin/pom.xml" />
        <option value="$PROJECT_DIR$/huatek-torch-job/torch-job-core/pom.xml" />
        <option value="$PROJECT_DIR$/huatek-torch-job/torch-job-executor/pom.xml" />
        <option value="$PROJECT_DIR$/j-x-m-e-s-basic/pom.xml" />
        <option value="$PROJECT_DIR$/j-x-m-e-s-basic/j-x-m-e-s-basic-application/pom.xml" />
        <option value="$PROJECT_DIR$/j-x-m-e-s-basic/j-x-m-e-s-basic-rpc/pom.xml" />
        <option value="$PROJECT_DIR$/j-x-m-e-s-gateway/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/../JXMES_2025_07_01_15_24_07__sourceCode/j-x-m-e-s-basic/j-x-m-e-s-basic-application/pom.xml" />
        <option value="$PROJECT_DIR$/../JXMES_2025_07_01_15_24_07__sourceCode/j-x-m-e-s-basic/j-x-m-e-s-basic-rpc/pom.xml" />
        <option value="$PROJECT_DIR$/../JXMES_2025_07_01_15_24_07__sourceCode/j-x-m-e-s-basic/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>