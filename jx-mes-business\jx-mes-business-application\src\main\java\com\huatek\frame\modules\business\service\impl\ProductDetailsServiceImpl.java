package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.ProductDetails;
import com.huatek.frame.modules.business.domain.vo.ProductDetailsVO;
import com.huatek.frame.modules.business.mapper.ProductDetailsMapper;
import com.huatek.frame.modules.business.service.ProductDetailsService;
import com.huatek.frame.modules.business.service.dto.ProductDetailsDTO;
import java.sql.Date;
import com.huatek.frame.modules.business.domain.SawOrder;
import com.huatek.frame.modules.business.mapper.SawOrderMapper;
import org.springframework.util.CollectionUtils;



/**
 * 产品明细 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "productDetails")
//@RefreshScope
@Slf4j
public class ProductDetailsServiceImpl implements ProductDetailsService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private ProductDetailsMapper productDetailsMapper;

	@Autowired
    private SawOrderMapper sawOrderMapper;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public ProductDetailsServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ProductDetailsVO>> findProductDetailsPage(ProductDetailsDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ProductDetailsVO> productDetailss = productDetailsMapper.selectProductDetailsPage(dto);
		TorchResponse<List<ProductDetailsVO>> response = new TorchResponse<List<ProductDetailsVO>>();
		response.getData().setData(productDetailss);
		response.setStatus(200);
		response.getData().setCount(productDetailss.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ProductDetailsDTO productDetailsDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(productDetailsDto.getCodexTorchDeleted())) {
            productDetailsDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = productDetailsDto.getId();
		ProductDetails entity = new ProductDetails();
        BeanUtils.copyProperties(productDetailsDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			productDetailsMapper.insert(entity);
		} else {
			productDetailsMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        ProductDetailsVO vo = new ProductDetailsVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductDetailsVO> findProductDetails(String id) {
		ProductDetailsVO vo = new ProductDetailsVO();
		if (!HuatekTools.isEmpty(id)) {
			ProductDetails entity = productDetailsMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<ProductDetailsVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<ProductDetails> productDetailsList = productDetailsMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductDetails productDetails : productDetailsList) {
            productDetails.setCodexTorchDeleted(Constant.DEFAULT_YES);
            productDetailsMapper.updateById(productDetails);
        }
		//productDetailsMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("workOrderNumber",productDetailsMapper::selectOptionsByWorkOrderNumber);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "product_details", convertorFields = "status")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductDetailsVO> selectProductDetailsList(ProductDetailsDTO dto) {
        return productDetailsMapper.selectProductDetailsList(dto);
    }

    /**
     * 导入产品明细数据
     *
     * @param productDetailsList 产品明细数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "product_details", convertorFields = "status")
    public TorchResponse importProductDetails(List<ProductDetailsVO> productDetailsList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(productDetailsList) || productDetailsList.size() == 0) {
            throw new ServiceException("导入产品明细数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProductDetailsVO vo : productDetailsList) {
            try {
                ProductDetails productDetails = new ProductDetails();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, productDetails);
                QueryWrapper<ProductDetails> wrapper = new QueryWrapper();
                ProductDetails oldProductDetails = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = ProductDetailsVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<ProductDetails> oldProductDetailsList = productDetailsMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldProductDetailsList) && oldProductDetailsList.size() > 1) {
                        productDetailsMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldProductDetailsList) && oldProductDetailsList.size() == 1) {
                        oldProductDetails = oldProductDetailsList.get(0);
                    }
                }
                if (StringUtils.isNull(oldProductDetails)) {
                    BeanValidators.validateWithException(validator, vo);
                    productDetailsMapper.insert(productDetails);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、规格型号 " + vo.getSpecificationModel() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldProductDetails, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    productDetailsMapper.updateById(oldProductDetails);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、规格型号 " + vo.getSpecificationModel() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、规格型号 " + vo.getSpecificationModel() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、规格型号 " + vo.getSpecificationModel() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(ProductDetailsVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (!HuatekTools.isEmpty(vo.getWorkOrderNumber())) {
            List<String> workOrderNumberList = Arrays.asList(vo.getWorkOrderNumber().split(","));
            List<SawOrder> list = sawOrderMapper.selectList(new QueryWrapper<SawOrder>().in("work_order_number", workOrderNumberList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("工单编号=" + vo.getWorkOrderNumber() + "; ");
            }
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProductDetailsListByIds(List<String> ids) {
        List<ProductDetailsVO> productDetailsList = productDetailsMapper.selectProductDetailsListByIds(ids);

		TorchResponse<List<ProductDetailsVO>> response = new TorchResponse<List<ProductDetailsVO>>();
		response.getData().setData(productDetailsList);
		response.setStatus(200);
		response.getData().setCount((long)productDetailsList.size());
		return response;
    }




}
