<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" id="Definitions_0fr9mxs" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.24.0">
  <bpmn:process id="TriWayMultiApproverProcess1" name="三向单级审批流程" isExecutable="true" camunda:historyTimeToLive="180">
    <bpmn:startEvent id="StartEvent" name="开始" camunda:initiator="${initiator}">
      <bpmn:outgoing>StartToApplyFlow</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="StartToApplyFlow" sourceRef="StartEvent" targetRef="ApplyTask" />
    <bpmn:endEvent id="EndEvent" name="结束">
      <bpmn:incoming>RejectToEndFlow</bpmn:incoming>
      <bpmn:incoming>AgreeToEndFlow</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SubmitApplySequenceFlow" name="提交审批" sourceRef="ApplyTask" targetRef="ApprovalTask1">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.SubmitApplyListener" event="take" />
      </bpmn:extensionElements>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="ApplyTask" name="申请" camunda:formKey="${formId}" camunda:assignee="${initiator}" camunda:candidateUsers="${applicant}">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.ApplyEndEventListener" event="end" />
        <camunda:taskListener class="com.huatek.frame.modules.bpm.listener.ApplyTaskListener" event="create" />
      </bpmn:extensionElements>
      <bpmn:incoming>StartToApplyFlow</bpmn:incoming>
      <bpmn:incoming>ReapplyToApplyFlow</bpmn:incoming>
      <bpmn:outgoing>SubmitApplySequenceFlow</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="ToApprovalGateway" sourceRef="ApprovalTask1" targetRef="ApprovalGateway" />
    <bpmn:userTask id="ApprovalTask1" name="审批" camunda:assignee="${multiApprovalAssigneeService.getAssignee(execution)}" camunda:candidateUsers="${multiApprovalAssigneeService.getCandidateUsers(execution)}">
      <bpmn:extensionElements>
        <camunda:taskListener class="com.huatek.frame.modules.bpm.listener.MultiApprovalTaskListener" event="update" />
      </bpmn:extensionElements>
      <bpmn:incoming>SubmitApplySequenceFlow</bpmn:incoming>
      <bpmn:outgoing>ToApprovalGateway</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ApprovalGateway" name="审批结果网关" default="RejectFlow1">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.GatewayEndEventListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>ToApprovalGateway</bpmn:incoming>
      <bpmn:outgoing>PassFlow1</bpmn:outgoing>
      <bpmn:outgoing>RejectFlow1</bpmn:outgoing>
      <bpmn:outgoing>ReapplyFlow1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="PassFlow1" name="同意" sourceRef="ApprovalGateway" targetRef="AgreeNotifyTask">
      <bpmn:extensionElements />
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approvalAct == 'AGREE'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="RejectFlow1" name="不同意" sourceRef="ApprovalGateway" targetRef="RejectNotifyTask" />
    <bpmn:sequenceFlow id="ReapplyFlow1" name="重新申请" sourceRef="ApprovalGateway" targetRef="ReapplyNotifyTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approvalAct== 'REAPPLY'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="ReapplyToApplyFlow" sourceRef="ReapplyNotifyTask" targetRef="ApplyTask" />
    <bpmn:sequenceFlow id="RejectToEndFlow" sourceRef="RejectNotifyTask" targetRef="EndEvent" />
    <bpmn:sequenceFlow id="AgreeToEndFlow" sourceRef="AgreeNotifyTask" targetRef="EndEvent" />
    <bpmn:sendTask id="AgreeNotifyTask" name="最终结果：批准" camunda:class="com.huatek.frame.modules.bpm.service.impl.ApprovalNotifyServiceImpl">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.ApprovalNotifyStartEventListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>PassFlow1</bpmn:incoming>
      <bpmn:outgoing>AgreeToEndFlow</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sendTask id="RejectNotifyTask" name="最终结果：拒绝" camunda:class="com.huatek.frame.modules.bpm.service.impl.ApprovalNotifyServiceImpl">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.ApprovalNotifyStartEventListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>RejectFlow1</bpmn:incoming>
      <bpmn:outgoing>RejectToEndFlow</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sendTask id="ReapplyNotifyTask" name="最终结果：驳回" camunda:class="com.huatek.frame.modules.bpm.service.impl.ApprovalNotifyServiceImpl">
      <bpmn:extensionElements />
      <bpmn:incoming>ReapplyFlow1</bpmn:incoming>
      <bpmn:outgoing>ReapplyToApplyFlow</bpmn:outgoing>
    </bpmn:sendTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TriWayMultiApproverProcess1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent">
        <dc:Bounds x="152" y="272" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="158" y="283" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0x6ir2l_di" bpmnElement="EndEvent">
        <dc:Bounds x="912" y="268" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="918" y="279" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_08mft2c_di" bpmnElement="ApplyTask">
        <dc:Bounds x="230" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1prp1jo_di" bpmnElement="ApprovalTask1">
        <dc:Bounds x="400" y="250" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_13k1pru_di" bpmnElement="ApprovalGateway" isMarkerVisible="true">
        <dc:Bounds x="585" y="265" width="50" height="50" />
        <bpmndi:BPMNLabel color:color="#6b3c00">
          <dc:Bounds x="526" y="269" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s30o8n_di" bpmnElement="AgreeNotifyTask">
        <dc:Bounds x="720" y="246" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0p7iagc_di" bpmnElement="RejectNotifyTask">
        <dc:Bounds x="720" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0yeevdg_di" bpmnElement="ReapplyNotifyTask">
        <dc:Bounds x="560" y="400" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1fp17al_di" bpmnElement="StartToApplyFlow">
        <di:waypoint x="188" y="290" />
        <di:waypoint x="230" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_16gzt2m_di" bpmnElement="SubmitApplySequenceFlow">
        <di:waypoint x="330" y="290" />
        <di:waypoint x="400" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="342" y="272" width="45" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r99iaa_di" bpmnElement="ToApprovalGateway">
        <di:waypoint x="500" y="290" />
        <di:waypoint x="585" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zllfn9_di" bpmnElement="PassFlow1">
        <di:waypoint x="635" y="290" />
        <di:waypoint x="720" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="676" y="269" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v454sy_di" bpmnElement="RejectFlow1">
        <di:waypoint x="610" y="265" />
        <di:waypoint x="610" y="120" />
        <di:waypoint x="720" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="656" y="103" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10ynvku_di" bpmnElement="ReapplyFlow1">
        <di:waypoint x="610" y="315" />
        <di:waypoint x="610" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="557" y="343" width="45" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_119mg90_di" bpmnElement="ReapplyToApplyFlow">
        <di:waypoint x="560" y="440" />
        <di:waypoint x="280" y="440" />
        <di:waypoint x="280" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vf8ogx_di" bpmnElement="RejectToEndFlow">
        <di:waypoint x="820" y="120" />
        <di:waypoint x="930" y="120" />
        <di:waypoint x="930" y="268" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jlcof5_di" bpmnElement="AgreeToEndFlow">
        <di:waypoint x="820" y="286" />
        <di:waypoint x="912" y="286" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
