package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 监制验收工单VO实体类
* <AUTHOR>
* @date 2025-08-20
**/
@Data
@ApiModel("监制验收工单DTO实体类")
public class SawOrderVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderNumber;

    /**
     * 订单类型
     */
    @ApiModelProperty("订单类型")
    private String orderType;

    /**
     * 试验类型
     */
    @ApiModelProperty("试验类型")
    private String testType;

    /**
     * 委托单位
     */
    @ApiModelProperty("委托单位")
    private String entrustedUnit;

    /**
     * 要求完成日期
     */
    @ApiModelProperty("要求完成日期")
    @Excel(name = "要求完成日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadline;

    /**
     * 生产厂家
     */
    @ApiModelProperty("生产厂家")
    private String manufacturer;


    /**
	 * 客户经理
     **/
    @ApiModelProperty("客户经理")
    @Excel(name = "客户经理",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String customerManager;

    /**
     * 订单合同号
     **/
    @ApiModelProperty("订单合同号")
    @Excel(name = "订单合同号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String orderContractNumber;

    /**
	 * 工程代码
     **/
    @ApiModelProperty("工程代码")
    @Excel(name = "工程代码",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String engineeringCode;

    /**
	 * 紧急程度
     **/
    @ApiModelProperty("紧急程度")
    @Excel(name = "紧急程度",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String urgencyLevel;

    /**
	 * 验收通知单
     **/
    @ApiModelProperty("验收通知单")
    @Excel(name = "验收通知单",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String acceptanceNotice;

    /**
	 * 订单备注
     **/
    @ApiModelProperty("订单备注")
    @Excel(name = "订单备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderRemarks;

    /**
	 * 订单送检编号
     **/
    @ApiModelProperty("订单送检编号")
    @Excel(name = "订单送检编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderInspectionNumber;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")

    private String workOrderNumber;

    /**
	 * 验收类型
     **/
    @ApiModelProperty("验收类型")
    @Excel(name = "验收类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String acceptanceType;

    /**
	 * 负责人
     **/
    @ApiModelProperty("负责人")
    @Excel(name = "负责人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String responsiblePerson;

    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String status;

    /**
	 * 完成日期
     **/
    @ApiModelProperty("完成日期")
    @Excel(name = "完成日期",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completionDate;

    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String comment;

    /**
	 * 上传附件
     **/
    @ApiModelProperty("上传附件")
    @Excel(name = "上传附件",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String attachment;


    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String creator;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属部门")
    private String belongGroup;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp createDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}