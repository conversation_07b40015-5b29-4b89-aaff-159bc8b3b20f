package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.FileReviewVO;
import com.huatek.frame.modules.business.service.FileReviewService;
import com.huatek.frame.modules.business.service.dto.AbnormalfeedbackDTO;
import com.huatek.frame.modules.business.service.dto.FileReviewDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-20
**/
@Api(tags = "文件评审管理")
@RestController
@RequestMapping("/api/fileReview")
public class FileReviewController {

	@Autowired
    private FileReviewService fileReviewService;

	/**
	 * 文件评审列表
	 * 
	 * @param dto 文件评审DTO 实体对象
	 * @return
	 */
    @Log("文件评审列表")
    @ApiOperation(value = "文件评审列表查询")
    @PostMapping(value = "/fileReviewList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("fileReview:list")
    public TorchResponse<List<FileReviewVO>> query(@RequestBody FileReviewDTO dto){
        return fileReviewService.findFileReviewPage(dto);
    }

	/**
	 * 新增/修改文件评审
	 * 
	 * @param fileReviewDto 文件评审DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改文件评审")
    @ApiOperation(value = "文件评审新增/修改操作")
    @PostMapping(value = "/fileReview", produces = { "application/json;charset=utf-8" })
    @TorchPerm("fileReview:add#fileReview:edit")
    public TorchResponse add(@RequestBody FileReviewDTO fileReviewDto) throws Exception {
		// BeanValidatorFactory.validate(fileReviewDto);
		return fileReviewService.saveOrUpdate(fileReviewDto);
	}

	/**
	 * 查询文件评审详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("文件评审详情")
    @ApiOperation(value = "文件评审详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("fileReview:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return fileReviewService.findFileReview(id);
	}

	/**
	 * 删除文件评审
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除文件评审")
    @ApiOperation(value = "文件评审删除操作")
    @TorchPerm("fileReview:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return fileReviewService.delete(ids);
	}

    @ApiOperation(value = "文件评审联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return fileReviewService.getOptionsList(id);
	}





    @Log("文件评审导出")
    @ApiOperation(value = "文件评审导出")
    @TorchPerm("fileReview:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody FileReviewDTO dto)
    {
        List<FileReviewVO> list = fileReviewService.selectFileReviewList(dto);
        ExcelUtil<FileReviewVO> util = new ExcelUtil<FileReviewVO>(FileReviewVO.class);
        util.exportExcel(response, list, "文件评审数据");
    }

    @Log("文件评审导入")
    @ApiOperation(value = "文件评审导入")
    @TorchPerm("fileReview:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<FileReviewVO> util = new ExcelUtil<FileReviewVO>(FileReviewVO.class);
        List<FileReviewVO> list = util.importExcel(file.getInputStream());
        return fileReviewService.importFileReview(list, unionColumns, true, "");
    }

    @Log("文件评审导入模板")
    @ApiOperation(value = "文件评审导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<FileReviewVO> util = new ExcelUtil<FileReviewVO>(FileReviewVO.class);
        util.importTemplateExcel(response, "文件评审数据");
    }

    @Log("根据Ids获取文件评审列表")
    @ApiOperation(value = "文件评审 根据Ids批量查询")
    @PostMapping(value = "/fileReviewList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getFileReviewListByIds(@RequestBody List<String> ids) {
        return fileReviewService.selectFileReviewListByIds(ids);
    }


    /**
     * 评审/上传变更文件评审
     *
     * @param fileReviewDto 文件评审DTO实体对象
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("评审/上传变更文件评审")
    @ApiOperation(value = "评审/上传变更文件评审")
    @PostMapping(value = "/reviewUpdateFileReview", produces = { "application/json;charset=utf-8" })
    @TorchPerm("fileReview:review#fileReview:updateChangeFile")
    public TorchResponse reviewUpdateFileReview(@RequestBody FileReviewDTO fileReviewDto) throws Exception {
        // BeanValidatorFactory.validate(fileReviewDto);
        return fileReviewService.reviewUpdateFileReview(fileReviewDto);
    }

    /**
     * 文件评审异常反馈
     *
     * @param abnormalfeedbackDto 文件评审DTO实体对象
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("文件评审异常反馈")
    @ApiOperation(value = "文件评审异常反馈")
    @PostMapping(value = "/fileReviewAbnormalFeedback", produces = { "application/json;charset=utf-8" })
    @TorchPerm("fileReview:edit")
    public TorchResponse abnormalfeedback(@RequestBody AbnormalfeedbackDTO abnormalfeedbackDto) throws Exception {
        return fileReviewService.abnormalfeedback(abnormalfeedbackDto);
    }

}