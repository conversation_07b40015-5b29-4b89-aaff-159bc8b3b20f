<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProductionOrderResultMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.work_order as workOrder,
		t.inspection_conclusion as inspectionConclusion,
		t.other_explanation_remark as otherExplanationRemark,
		t.unqualified_quantity as unqualifiedQuantity,
		t.inspection_result as inspectionResult,
		t.inspection_from as inspectionFrom,
		t.test_specifications as testSpecifications,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>

	<select id="selectByWorkOrder"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderResultVO">
        select
        <include refid="Base_Column_List" />
        from production_order_result t where t.work_order=#{workOrder}
    </select>

	<select id="selectPrcessTestDataByWorkOrder"
			resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderProcessTestVO">
		select * from  production_order_process_test t WHERE t.work_order =#{workOrder}
	</select>

</mapper>