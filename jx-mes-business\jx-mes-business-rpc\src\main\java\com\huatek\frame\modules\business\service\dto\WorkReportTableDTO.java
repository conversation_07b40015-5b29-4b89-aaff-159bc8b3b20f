package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description 工时报表DTO 实体类
* <AUTHOR>
* @date 2025-08-18
**/
@Data
@ApiModel("工时报表DTO实体类")
public class WorkReportTableDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 合同编号
     **/
    @ApiModelProperty("合同编号")
    private String contractNumber;
    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;
    
    /**
	 * 送检数量
     **/
    @ApiModelProperty("送检数量")
    private Long inspectionQuantity2;
    
    /**
	 * 标准工序名称
     **/
    @ApiModelProperty("标准工序名称")
    private String standardProcessName;
    
    /**
	 * 客户试验名称
     **/
    @ApiModelProperty("客户试验名称")
    private String customerTestName;
    
    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    private String productName;
    
    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    private String productModel;
    
    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    
    /**
	 * 批次号
     **/
    @ApiModelProperty("批次号")
    private String batchNumber;
    
    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    private String productCategory;
    
    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    private String productInformation1;
    
    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    private String entrustedUnit;
    
    /**
	 * 试验方式
     **/
    @ApiModelProperty("试验方式")
    private String testMethodology;
    
    /**
	 * 工序开始时间
     **/
    @ApiModelProperty("工序开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp processStartTime;
    
    /**
	 * 工序结束时间
     **/
    @ApiModelProperty("工序结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp processEndTime;
    
    /**
	 * 工序时长
     **/
    @ApiModelProperty("工序时长")
    private BigDecimal processDuration;
    
    /**
	 * 报工人
     **/
    @ApiModelProperty("报工人")
    private String reporter4;
    
    /**
	 * 研发任务编号
     **/
    @ApiModelProperty("研发任务编号")
    private String rdTaskNumber;
    
    /**
	 * 是否参与核算
     **/
    @ApiModelProperty("是否参与核算")
    private String wipaia;
    
    /**
	 * 是否外协工序
     **/
    @ApiModelProperty("是否外协工序")
    private String whetherOutsourcedProcess6;
    
    /**
	 * 设备共用工单
     **/
    @ApiModelProperty("设备共用工单")
    private String sharedEquipmentWorkOrder;
    
    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;
    
    /**
	 * 样本总数
     **/
    @ApiModelProperty("样本总数")
    private Long sampleTotalCount;
    
    /**
	 * 设备编号
     **/
    @ApiModelProperty("设备编号")
    private String deviceSerialNumber;
    
    /**
	 * 设备名称
     **/
    @ApiModelProperty("设备名称")
    private String deviceName;
    
    /**
	 * 固定资产编码
     **/
    @ApiModelProperty("固定资产编码")
    private String fixedAssetCoding;
    
    /**
	 * 同工序时长
     **/
    @ApiModelProperty("同工序时长")
    private String durationOfTheSameProcess;
    
    /**
	 * 设备开始时间
     **/
    @ApiModelProperty("设备开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp equipmentEndTime;
    
    /**
	 * 设备结束时间
     **/
    @ApiModelProperty("设备结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp equipmentEndTime27;
    
    /**
	 * 设备用时（h）
     **/
    @ApiModelProperty("设备用时（h）")
    private BigDecimal equipmentRunningTimeh;
    
    /**
	 * 设备能耗
     **/
    @ApiModelProperty("设备能耗")
    private Long equipmentEnergyConsumption;
    
    /**
	 * 设备功耗
     **/
    @ApiModelProperty("设备功耗")
    private Long equipmentPowerConsumption;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;


}