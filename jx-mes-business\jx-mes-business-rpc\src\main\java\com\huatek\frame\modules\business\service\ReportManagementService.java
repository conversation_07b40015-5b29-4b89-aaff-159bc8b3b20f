package com.huatek.frame.modules.business.service;

import com.huatek.frame.modules.business.domain.vo.ReportManagementVO;
import com.huatek.frame.modules.business.service.dto.ReportManagementDTO;
import com.huatek.frame.common.response.TorchResponse;
import java.util.List;

import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import java.util.Map;

/**
* @description 报告管理Service
* <AUTHOR>
* @date 2025-08-15
**/
public interface ReportManagementService {
    
    /**
	 * 分页查找查找 报告管理
	 * 
	 * @param dto 报告管理dto实体对象
	 * @return 
	 */
	TorchResponse<List<ReportManagementVO>> findReportManagementPage(ReportManagementDTO dto);

    /**
	 * 添加 \修改 报告管理
	 * 
	 * @param reportManagementDto 报告管理dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(ReportManagementDTO reportManagementDto);
	
	/**
	 * 通过id查找报告管理
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<ReportManagementVO> findReportManagement(String id);
	
	/**
	 * 删除 报告管理
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 报告管理
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<ReportManagementVO>> getOptionsList(String id);

	/**
	 * 表单审批
	 *
	 * @param formApprovalDTO
	 * @param token
	 * @return
	 */
    TorchResponse approve(FormApprovalDTO formApprovalDTO, String token);

	/**
	 * 审批审批 报告管理
	 *
	 * @param reportManagementDto 用户管理dto实体对象
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse apply(ReportManagementDTO reportManagementDto,String token);




    /**
     * 联动数据查询
     *
     * @param linkageDataTableName
     * @param conditionalValue
     * @return
     */
    TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue);

    Map<String,String> selectDataLinkageByWorkOrderNumber(String work_order_number);
    /**
     * 根据条件查询报告管理列表
     *
     * @param dto 报告管理信息
     * @return 报告管理集合信息
     */
    List<ReportManagementVO> selectReportManagementList(ReportManagementDTO dto);

    /**
     * 导入报告管理数据
     *
     * @param reportManagementList 报告管理数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importReportManagement(List<ReportManagementVO> reportManagementList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取报告管理数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectReportManagementListByIds(List<String> ids);

	/**
     * 提交报告
     *
     * @param ids
     * @param token
     * @return
     */
	TorchResponse submit(String[] ids, String token);
}