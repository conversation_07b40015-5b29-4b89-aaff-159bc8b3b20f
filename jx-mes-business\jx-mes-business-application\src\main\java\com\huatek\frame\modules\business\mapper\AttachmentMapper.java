package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.MapKey;
import java.util.Map;
import com.huatek.frame.modules.business.domain.Attachment;
import com.huatek.frame.modules.business.domain.vo.AttachmentVO;
import com.huatek.frame.modules.business.service.dto.AttachmentDTO;
import io.swagger.annotations.ApiModelProperty;

/**
* @description 附件表Mapper
* <AUTHOR>
* @date 2025-08-18
**/
public interface AttachmentMapper extends BaseMapper<Attachment> {

    /**
     * 分页查询附件表
     *
     * @param dto 附件表信息
     * @return 附件表集合信息
     */
    Page<AttachmentVO> selectAttachmentPage(AttachmentDTO dto);

    /**
     * 根据条件查询附件表列表
     *
     * @param dto 附件表信息
     * @return 附件表集合信息
     */
    List<AttachmentVO> selectAttachmentList(AttachmentDTO dto);

	/**
	 * 根据IDS查询附件表列表
	 * @param ids
	 * @return
	 */
    List<AttachmentVO> selectAttachmentListByIds(@Param("ids") List<String> ids);
    
    /**
     * 根据工单编码查询附件列表
     * @param workOrderCode 工单编码
     * @return 附件列表
     */
    List<AttachmentVO> selectAttachmentListByWorkOrderCode(@Param("workOrderCode") String workOrderCode);
    
    /**
     * 根据工单编码和工序编码查询附件列表
     * @param workOrderCode 工单编码
     * @param processCode 工序编码
     * @return 附件列表
     */
    List<AttachmentVO> selectAttachmentListByWorkOrderAndProcess(@Param("workOrderCode") String workOrderCode, @Param("processCode") String processCode);
}
