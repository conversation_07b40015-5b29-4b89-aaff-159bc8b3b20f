<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" id="Definitions_0fr9mxs" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.24.0">
  <bpmn:process id="TriWayMultiApproverProcess2" name="三向二级审批流程" isExecutable="true" camunda:historyTimeToLive="180">
    <bpmn:extensionElements/>
    <bpmn:startEvent id="StartEvent" name="开始" camunda:initiator="${initiator}">
      <bpmn:outgoing>ToApplyFlow</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="ToApplyFlow" sourceRef="StartEvent" targetRef="ApplyTask"/>
    <bpmn:sequenceFlow id="SubmitApplySequenceFlow" name="提交审批" sourceRef="ApplyTask" targetRef="ApprovalTask1">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.SubmitApplyListener" event="take"/>
      </bpmn:extensionElements>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="ApplyTask" name="申请" camunda:formKey="${formId}" camunda:assignee="${initiator}" camunda:candidateUsers="${applicant}">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.ApplyEndEventListener" event="end"/>
        <camunda:taskListener class="com.huatek.frame.modules.bpm.listener.ApplyTaskListener" event="create"/>
      </bpmn:extensionElements>
      <bpmn:incoming>ToApplyFlow</bpmn:incoming>
      <bpmn:incoming>Flow_0kl0ty2</bpmn:incoming>
      <bpmn:outgoing>SubmitApplySequenceFlow</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="ToApproval1GatewayFlow" sourceRef="ApprovalTask1" targetRef="ApprovalGateway1"/>
    <bpmn:userTask id="ApprovalTask1" name="一级审批" camunda:assignee="${multiApprovalAssigneeService.getAssignee(execution)}" camunda:candidateUsers="${multiApprovalAssigneeService.getCandidateUsers(execution)}">
      <bpmn:extensionElements>
        <camunda:taskListener class="com.huatek.frame.modules.bpm.listener.MultiApprovalTaskListener" event="update"/>
      </bpmn:extensionElements>
      <bpmn:incoming>SubmitApplySequenceFlow</bpmn:incoming>
      <bpmn:outgoing>ToApproval1GatewayFlow</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ApprovalGateway1" name="一级审批结果" default="Reject1Flow">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.GatewayEndEventListener" event="end"/>
      </bpmn:extensionElements>
      <bpmn:incoming>ToApproval1GatewayFlow</bpmn:incoming>
      <bpmn:outgoing>Agree1Flow</bpmn:outgoing>
      <bpmn:outgoing>Reject1Flow</bpmn:outgoing>
      <bpmn:outgoing>ReapplyFlow1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Agree1Flow" name="同意" sourceRef="ApprovalGateway1" targetRef="ApprovalTask2">
      <bpmn:extensionElements/>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approvalAct == 'AGREE'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sendTask id="AgreeNotifyTask" name="最终结果：批准" camunda:class="com.huatek.frame.modules.bpm.service.impl.ApprovalNotifyServiceImpl">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.ApprovalNotifyStartEventListener" event="start"/>
      </bpmn:extensionElements>
      <bpmn:incoming>Agree2Flow</bpmn:incoming>
      <bpmn:outgoing>AfterAgreeEndFlow</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sendTask id="RejectNotifyTask" name="最终结果：拒绝" camunda:class="com.huatek.frame.modules.bpm.service.impl.ApprovalNotifyServiceImpl">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.ApprovalNotifyStartEventListener" event="start"/>
      </bpmn:extensionElements>
      <bpmn:incoming>Reject1Flow</bpmn:incoming>
      <bpmn:incoming>Reject2Flow</bpmn:incoming>
      <bpmn:outgoing>AfterRejectEndFlow</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:endEvent id="EndEvent" name="结束">
      <bpmn:incoming>AfterAgreeEndFlow</bpmn:incoming>
      <bpmn:incoming>AfterRejectEndFlow</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="AfterAgreeEndFlow" sourceRef="AgreeNotifyTask" targetRef="EndEvent"/>
    <bpmn:sequenceFlow id="AfterRejectEndFlow" sourceRef="RejectNotifyTask" targetRef="EndEvent"/>
    <bpmn:exclusiveGateway id="ApprovalGateway2" name="二级审批结果" default="Reject2Flow">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.huatek.frame.modules.bpm.listener.GatewayEndEventListener" event="end"/>
      </bpmn:extensionElements>
      <bpmn:incoming>ToApproval2GatewayFlow</bpmn:incoming>
      <bpmn:outgoing>Reject2Flow</bpmn:outgoing>
      <bpmn:outgoing>Agree2Flow</bpmn:outgoing>
      <bpmn:outgoing>ReapplyFlow2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="ToApproval2GatewayFlow" sourceRef="ApprovalTask2" targetRef="ApprovalGateway2"/>
    <bpmn:sequenceFlow id="Reject1Flow" name="不同意" sourceRef="ApprovalGateway1" targetRef="RejectNotifyTask"/>
    <bpmn:sequenceFlow id="Reject2Flow" name="不同意" sourceRef="ApprovalGateway2" targetRef="RejectNotifyTask"/>
    <bpmn:userTask id="ApprovalTask2" name="二级审批" camunda:assignee="${multiApprovalAssigneeService.getAssignee(execution)}" camunda:candidateUsers="${multiApprovalAssigneeService.getCandidateUsers(execution)}">
      <bpmn:extensionElements>
        <camunda:taskListener class="com.huatek.frame.modules.bpm.listener.MultiApprovalTaskListener" event="update"/>
      </bpmn:extensionElements>
      <bpmn:incoming>Agree1Flow</bpmn:incoming>
      <bpmn:outgoing>ToApproval2GatewayFlow</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Agree2Flow" name="同意" sourceRef="ApprovalGateway2" targetRef="AgreeNotifyTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approvalAct == 'AGREE'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="ReapplyFlow1" name="重新申请" sourceRef="ApprovalGateway1" targetRef="ReapplyNotifyTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approvalAct== 'REAPPLY'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sendTask id="ReapplyNotifyTask" name="最终结果：驳回" camunda:class="com.huatek.frame.modules.bpm.service.impl.ApprovalNotifyServiceImpl">
      <bpmn:incoming>ReapplyFlow1</bpmn:incoming>
      <bpmn:incoming>ReapplyFlow2</bpmn:incoming>
      <bpmn:outgoing>Flow_0kl0ty2</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sequenceFlow id="Flow_0kl0ty2" sourceRef="ReapplyNotifyTask" targetRef="ApplyTask"/>
    <bpmn:sequenceFlow id="ReapplyFlow2" name="重新申请" sourceRef="ApprovalGateway2" targetRef="ReapplyNotifyTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approvalAct== 'REAPPLY'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TriWayMultiApproverProcess2">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent" bioc:stroke="#6caf21">
        <dc:Bounds x="152" y="262" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="158" y="273" width="23" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_08mft2c_di" bpmnElement="ApplyTask" bioc:stroke="#6caf21">
        <dc:Bounds x="250" y="240" width="100" height="80"/>
        <bpmndi:BPMNLabel/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1prp1jo_di" bpmnElement="ApprovalTask1" bioc:stroke="#6caf21">
        <dc:Bounds x="430" y="240" width="100" height="80"/>
        <bpmndi:BPMNLabel/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_13k1pru_di" bpmnElement="ApprovalGateway1" isMarkerVisible="true" bioc:stroke="#6caf21">
        <dc:Bounds x="595" y="255" width="50" height="50"/>
        <bpmndi:BPMNLabel color:color="#6b3c00">
          <dc:Bounds x="628" y="303" width="67" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s30o8n_di" bpmnElement="AgreeNotifyTask" bioc:stroke="#6caf21">
        <dc:Bounds x="430" y="500" width="100" height="80"/>
        <bpmndi:BPMNLabel/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0p7iagc_di" bpmnElement="RejectNotifyTask">
        <dc:Bounds x="430" y="370" width="100" height="80"/>
        <bpmndi:BPMNLabel/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0x6ir2l_di" bpmnElement="EndEvent" bioc:stroke="#6caf21">
        <dc:Bounds x="252" y="522" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="258" y="533" width="23" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ohyd3r_di" bpmnElement="ApprovalGateway2" isMarkerVisible="true" bioc:stroke="#6caf21">
        <dc:Bounds x="755" y="385" width="50" height="50"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="706" y="433" width="67" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0knck7i_di" bpmnElement="ApprovalTask2" bioc:stroke="#6caf21">
        <dc:Bounds x="730" y="240" width="100" height="80"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_15mqdd5_di" bpmnElement="ReapplyNotifyTask">
        <dc:Bounds x="570" y="120" width="100" height="80"/>
        <bpmndi:BPMNLabel/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1fp17al_di" bpmnElement="ToApplyFlow" bioc:stroke="#6caf21">
        <di:waypoint x="188" y="280"/>
        <di:waypoint x="250" y="280"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_16gzt2m_di" bpmnElement="SubmitApplySequenceFlow" bioc:stroke="#6caf21">
        <di:waypoint x="350" y="280"/>
        <di:waypoint x="430" y="280"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="367" y="262" width="45" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r99iaa_di" bpmnElement="ToApproval1GatewayFlow" bioc:stroke="#6caf21">
        <di:waypoint x="530" y="280"/>
        <di:waypoint x="595" y="280"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zllfn9_di" bpmnElement="Agree1Flow" bioc:stroke="#6caf21">
        <di:waypoint x="645" y="280"/>
        <di:waypoint x="730" y="280"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="650" y="265" width="23" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jlcof5_di" bpmnElement="AfterAgreeEndFlow" bioc:stroke="#6caf21">
        <di:waypoint x="430" y="540"/>
        <di:waypoint x="288" y="540"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vf8ogx_di" bpmnElement="AfterRejectEndFlow">
        <di:waypoint x="430" y="410"/>
        <di:waypoint x="270" y="410"/>
        <di:waypoint x="270" y="522"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r6b6we_di" bpmnElement="ToApproval2GatewayFlow" bioc:stroke="#6caf21">
        <di:waypoint x="780" y="320"/>
        <di:waypoint x="780" y="385"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10odkzd_di" bpmnElement="Reject1Flow">
        <di:waypoint x="608" y="293"/>
        <di:waypoint x="524" y="371"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="573" y="333" width="34" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_063sdb9_di" bpmnElement="Reject2Flow">
        <di:waypoint x="755" y="410"/>
        <di:waypoint x="530" y="410"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="706" y="395" width="34" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14nv3vv_di" bpmnElement="Agree2Flow" bioc:stroke="#6caf21">
        <di:waypoint x="780" y="435"/>
        <di:waypoint x="780" y="540"/>
        <di:waypoint x="530" y="540"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="707" y="523" width="23" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11ztai2_di" bpmnElement="ReapplyFlow1">
        <di:waypoint x="620" y="255"/>
        <di:waypoint x="620" y="200"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="567" y="233" width="45" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kl0ty2_di" bpmnElement="Flow_0kl0ty2">
        <di:waypoint x="570" y="160"/>
        <di:waypoint x="300" y="160"/>
        <di:waypoint x="300" y="240"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18s85wq_di" bpmnElement="ReapplyFlow2">
        <di:waypoint x="805" y="410"/>
        <di:waypoint x="900" y="410"/>
        <di:waypoint x="900" y="160"/>
        <di:waypoint x="670" y="160"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="817" y="395" width="45" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>