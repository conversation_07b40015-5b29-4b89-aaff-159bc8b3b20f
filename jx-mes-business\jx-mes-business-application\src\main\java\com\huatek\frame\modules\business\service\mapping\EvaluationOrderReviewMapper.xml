<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.EvaluationOrderReviewMapper">
    <sql id="Base_Info">
        t.id as id,
        t.evaluation_order_id as evaluationOrderId,
        t.product_list_id as productListId,
        t.review_result as reviewResult,
        t.review_comment as reviewComment,
        t.reviewer as reviewer,
        t.codex_torch_update_datetime as codexTorchUpdateDatetime
    </sql>

    <select id="findAll" resultType="com.huatek.frame.modules.business.domain.vo.EvaluationOrderReviewVO">
        select
        p.serial_number as serialNumber,
        p.test_type as testType,
        p.product_name as productName ,
        p.product_model as productModel,
        p.manufacturer as manufacture,
        p.production_batch as productionBatch,
        p.quantity as quantity,
        p.quality_grade as qualityGrade,
        <include refid="Base_Info"/>
        FROM evaluation_order_review t
        LEFT JOIN product_list p
        ON p.id = t.product_list_id
        WHERE t.evaluation_order_id = #{requestParam.evaluationOrderId}
    </select>

</mapper>