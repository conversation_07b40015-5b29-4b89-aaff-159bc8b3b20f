package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 附件表
* <AUTHOR>
* @date 2025-08-18
**/
@Setter
@Getter
@TableName("设备原始数据")
public class Attachment implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 工单编码
     **/
    @TableField(value = "work_order_code"
    )
    private String workOrderCode;

    
    /**
	 * 工序编码
     **/
    @TableField(value = "process_code"
    )
    private String processCode;

    
    /**
	 * 原始文件名称
     **/
    @TableField(value = "original_file_name"
    )
    private String originalFileName;

    
    /**
	 * 服务器文件名称
     **/
    @TableField(value = "server_file_name"
    )
    private String serverFileName;

    
    /**
	 * 文件存储路径
     **/
    @TableField(value = "file_path"
    )
    private String filePath;

    
    /**
	 * 文件大小(字节)
     **/
    @TableField(value = "file_size"
    )
    private Long fileSize;

    
    /**
	 * 文件类型
     **/
    @TableField(value = "file_type"
    )
    private String fileType;

    
    /**
	 * 文件后缀
     **/
    @TableField(value = "suffix"
    )
    private String suffix;

    
    /**
	 * 文件描述
     **/
    @TableField(value = "description"
    )
    private String description;

    
    /**
	 * 业务类型
     **/
    @TableField(value = "business_type"
    )
    private String businessType;

    
    /**
	 * 上传时间
     **/
    @TableField(value = "upload_time"
            ,fill = FieldFill.INSERT
    )
    private Timestamp uploadTime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "update_time"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp updateTime;
}
