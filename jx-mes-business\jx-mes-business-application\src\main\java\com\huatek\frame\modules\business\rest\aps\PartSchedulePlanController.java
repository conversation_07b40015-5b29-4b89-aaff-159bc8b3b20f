package com.huatek.frame.modules.business.rest.aps;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 排产计划
 */
@Api(tags = "排产计划")
@RestController
@RequestMapping("/api/SchedulePlan")
public class PartSchedulePlanController {

    @Autowired
    private HttpClientUtil httpClientUtil;
    /**
     * 新增排产计划
     */
    @Log("新增排产计划")
    @ApiOperation(value = "新增排产计划")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    public Object CreateSchedulePlanAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/CreateSchedule");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 试算
     */
    @Log("试算")
    @ApiOperation(value = "试算")
    @PostMapping(value = "/TrialSchedule", produces = { "application/json;charset=utf-8" })
    public Object TrialSchedulePlanAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/TrialSchedule");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 拖拉拽后保存接口
     */
    @Log("拖拉拽后保存接口")
    @ApiOperation(value = "拖拉拽后保存接口")
    @PostMapping(value = "/changeplan", produces = { "application/json;charset=utf-8" })
    public Object ChangeScheduleStatusAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/changeplan");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 列表分页
     */
    @Log("列表分页")
    @ApiOperation(value = "列表分页")
    @PostMapping(value = "/SchedulePlanList", produces = { "application/json;charset=utf-8" })
    public Object GetPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/page");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 修改排产计划状态
     */
    @Log("修改排产计划状态")
    @ApiOperation(value = "修改排产计划状态")
    @PostMapping(value = "/changeState", produces = { "application/json;charset=utf-8" })
    public Object ChangeStatusAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/changeState");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     *
     */
    @Log("检查来源冲突")
    @ApiOperation(value = "检查来源冲突")
    @PostMapping(value = "/CheckSourceConfilct", produces = { "application/json;charset=utf-8" })
    public Object CheckSourceConfilctAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/CheckSourceConfilct");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 检查来源
     */
    @Log("检查来源")
    @ApiOperation(value = "检查来源")
    @PostMapping(value = "/CheckSource", produces = { "application/json;charset=utf-8" })
    public Object CheckSourceEffiveAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/CheckSource");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 设备甘特图
     */
    @Log("设备甘特图")
    @ApiOperation(value = "设备甘特图")
    @PostMapping(value = "/deviceGantt", produces = { "application/json;charset=utf-8" })
    public Object GetDeviceGanttPLanAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/deviceGantt");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 报工保存
     */
    @Log("报工保存")
    @ApiOperation(value = "报工保存")
    @PostMapping(value = "/ChangeProgress", produces = { "application/json;charset=utf-8" })
    public Object ChangeStepProgressAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/ChangeProgress");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 分析资源冲突列表
     */
    @Log("分析资源冲突列表")
    @ApiOperation(value = "分析资源冲突列表")
    @PostMapping(value = "/QuerySourceConflict", produces = { "application/json;charset=utf-8" })
    public Object CheckSourceConflictAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/QuerySourceConflict");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 批量删除
     */
    @Log("批量删除")
    @ApiOperation(value = "批量删除")
    @PostMapping(value = "/batchDel", produces = { "application/json;charset=utf-8" })
    public Object DeleteAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlan/batchDel");
        return httpClientUtil.callMes(request, inputParamDto);
    }


}
