package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;
import com.huatek.frame.modules.business.domain.ProductDetails;
import  com.huatek.frame.modules.business.domain.vo.ProductDetailsVO;
import com.huatek.frame.modules.business.service.dto.ProductDetailsDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 产品明细mapper
* <AUTHOR>
* @date 2025-08-20
**/
public interface ProductDetailsMapper extends BaseMapper<ProductDetails> {

     /**
	 * 产品明细分页
	 * @param dto
	 * @return
	 */
	Page<ProductDetailsVO> selectProductDetailsPage(ProductDetailsDTO dto);

    /**
	 * 外键关联表: saw_order - work_order_number
     **/
    @ApiModelProperty("外键 saw_order - work_order_number")
	Page<SelectOptionsVO> selectOptionsByWorkOrderNumber(String workOrderNumber);

    /**
     * 根据条件查询产品明细列表
     *
     * @param dto 产品明细信息
     * @return 产品明细集合信息
     */
    List<ProductDetailsVO> selectProductDetailsList(ProductDetailsDTO dto);

	/**
	 * 根据IDS查询产品明细列表
	 * @param ids
	 * @return
	 */
    List<ProductDetailsVO> selectProductDetailsListByIds(@Param("ids") List<String> ids);


}