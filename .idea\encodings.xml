<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/../JXMES_2025_07_01_15_24_07__sourceCode/j-x-m-e-s-basic/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../JXMES_2025_07_01_15_24_07__sourceCode/j-x-m-e-s-basic/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-job/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-job/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-job/torch-job-admin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-job/torch-job-admin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-job/torch-job-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-job/torch-job-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-log/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-log/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-log/torch-log-application/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-log/torch-log-application/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-log/torch-log-rpc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-log/torch-log-rpc/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-log/torch-log-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/huatek-torch-log/torch-log-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/j-x-m-e-s-basic/j-x-m-e-s-basic-application/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/j-x-m-e-s-basic/j-x-m-e-s-basic-rpc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/j-x-m-e-s-basic/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/j-x-m-e-s-basic/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/j-x-m-e-s-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-rpc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-rpc/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-basic/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-basic/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-gateway/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-openapi/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-tool/jx-mes-tool-application/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-tool/jx-mes-tool-application/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-tool/jx-mes-tool-rpc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-tool/jx-mes-tool-rpc/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-tool/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/jx-mes-tool/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
  </component>
</project>