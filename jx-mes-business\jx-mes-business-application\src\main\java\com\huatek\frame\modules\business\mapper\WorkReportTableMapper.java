package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.WorkReportTable;
import com.huatek.frame.modules.business.domain.vo.WorkReportTableVO;
import com.huatek.frame.modules.business.service.dto.WorkReportTableDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 工时报表mapper
* <AUTHOR>
* @date 2025-08-18
**/
public interface WorkReportTableMapper extends BaseMapper<WorkReportTable> {

     /**
	 * 工时报表分页
	 * @param dto
	 * @return
	 */
	Page<WorkReportTableVO> selectWorkReportTablePage(WorkReportTableDTO dto);


    /**
     * 根据条件查询工时报表列表
     *
     * @param dto 工时报表信息
     * @return 工时报表集合信息
     */
    List<WorkReportTableVO> selectWorkReportTableList(WorkReportTableDTO dto);

	/**
	 * 根据IDS查询工时报表列表
	 * @param ids
	 * @return
	 */
    List<WorkReportTableVO> selectWorkReportTableListByIds(@Param("ids") List<String> ids);


}