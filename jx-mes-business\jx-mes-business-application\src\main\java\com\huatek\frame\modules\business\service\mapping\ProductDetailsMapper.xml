<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProductDetailsMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.work_order_number as workOrderNumber,
		t.specification_model as specificationModel,
		t.name_of_product as nameOfProduct,
		t.quality_grade as qualityGrade,
		t.production_batch as productionBatch,
		t.quality_assurance_basis as qualityAssuranceBasis,
		t.quantity_of_contract_units as quantityOfContractUnits,
		t.status as status,
		t.completion_date as completionDate,
		t.`comment` as `comment`,
		t.codex_torch_master_form_id as codexTorchMasterFormId,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectProductDetailsPage" parameterType="com.huatek.frame.modules.business.service.dto.ProductDetailsDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductDetailsVO">
		select
		<include refid="Base_Column_List" />
			from product_details t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="specificationModel != null and specificationModel != ''">
                    and t.specification_model  like concat('%', #{specificationModel} ,'%')
                </if>
                <if test="nameOfProduct != null and nameOfProduct != ''">
                    and t.name_of_product  like concat('%', #{nameOfProduct} ,'%')
                </if>
                <if test="qualityGrade != null and qualityGrade != ''">
                    and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
                </if>
                <if test="productionBatch != null and productionBatch != ''">
                    and t.production_batch  like concat('%', #{productionBatch} ,'%')
                </if>
                <if test="qualityAssuranceBasis != null and qualityAssuranceBasis != ''">
                    and t.quality_assurance_basis  like concat('%', #{qualityAssuranceBasis} ,'%')
                </if>
                <if test="quantityOfContractUnits != null and quantityOfContractUnits != ''">
                    and t.quantity_of_contract_units  = #{quantityOfContractUnits}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="completionDate != null">
                    and t.completion_date  = #{completionDate}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="codexTorchMasterFormId != null and codexTorchMasterFormId != ''">
                    and t.codex_torch_master_form_id  like concat('%', #{codexTorchMasterFormId} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>
     <select id="selectOptionsByWorkOrderNumber" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.work_order_number label,
        	t.work_order_number value
        from saw_order t
        WHERE t.work_order_number != ''
         and t.codex_torch_deleted = '0'
     </select>

    <select id="selectProductDetailsList" parameterType="com.huatek.frame.modules.business.service.dto.ProductDetailsDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductDetailsVO">
		select
		<include refid="Base_Column_List" />
			from product_details t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="specificationModel != null and specificationModel != ''">
                    and t.specification_model  like concat('%', #{specificationModel} ,'%')
                </if>
                <if test="nameOfProduct != null and nameOfProduct != ''">
                    and t.name_of_product  like concat('%', #{nameOfProduct} ,'%')
                </if>
                <if test="qualityGrade != null and qualityGrade != ''">
                    and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
                </if>
                <if test="productionBatch != null and productionBatch != ''">
                    and t.production_batch  like concat('%', #{productionBatch} ,'%')
                </if>
                <if test="qualityAssuranceBasis != null and qualityAssuranceBasis != ''">
                    and t.quality_assurance_basis  like concat('%', #{qualityAssuranceBasis} ,'%')
                </if>
                <if test="quantityOfContractUnits != null and quantityOfContractUnits != ''">
                    and t.quantity_of_contract_units  = #{quantityOfContractUnits}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="completionDate != null">
                    and t.completion_date  = #{completionDate}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="codexTorchMasterFormId != null and codexTorchMasterFormId != ''">
                    and t.codex_torch_master_form_id  like concat('%', #{codexTorchMasterFormId} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectProductDetailsListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductDetailsVO">
		select
		<include refid="Base_Column_List" />
			from product_details t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>