<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProductionValueCalculationMapper">
    <sql id="Base_Column_List">
        t.id as id,
        t.work_order_number as workOrderNumber,
        t.ticket_type as ticketType,
        t.status as status,
        t.`type` as `type`,
        t.settlement_date as settlementDate,
        t.product_name as productName,
        t.product_model as productModel,
        t.manufacturer as manufacturer,
        t.batch_number as batchNumber,
        if(pc.category_name is null,t.product_category,pc.category_name) as productCategory,
        t.product_information_name as productInformationName,
        t.product_information1 as productInformation1,
        w.entrusted_unit as entrustedUnit,
        t.product_quantity as productQuantity,
        t.charging_standard_name as chargingStandardName,
        t.internal_accounting_price as internalAccountingPrice,
        t.discount as discount,
        t.customer_accounting_price as customerAccountingPrice,
        t.settlement_price as settlementPrice,
        t.internal_price_classification as internalPriceClassification,
        t.customer_price_classification as customerPriceClassification,
        t.order_number as orderNumber,
        t.settlement_unit as settlementUnit,
        t.number_of_qualified_productions as numberOfQualifiedProductions,
        t.num_non_qual_prod as numNonQualProd,
        t.non_quality_process as nonQualityProcess,
        t.test_type as testType,
        t.date_of_entrustment as dateOfEntrustment,
        t.order_inspection_number as orderInspectionNumber,
        t.work_order_inspection_number1 as workOrderInspectionNumber1,
        t.bill_statement_number as billStatementNumber,
        t.contract_number as contractNumber,
        t.codex_torch_creator_id as codexTorchCreatorId,
        t.codex_torch_updater as codexTorchUpdater,
        t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_create_datetime as codexTorchCreateDatetime,
        t.codex_torch_update_datetime as codexTorchUpdateDatetime,
        t.codex_torch_deleted as codexTorchDeleted,
        t.completion_time as completionTime,
        t.shipping_date as shippingDate,
        t.recipient_customer as recipientCustomer

    </sql>
    <select id="selectProductionValueCalculationPage"
            parameterType="com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationVO">
        select
        <include refid="Base_Column_List"/>
        from production_value_calculation t
        left join customer_information_management w on t.entrusted_unit = w.id
        left join product_category pc on t.product_category = pc.id
        <where>
            and t.codex_torch_deleted = '0'
            <if test="workOrderNumber != null and workOrderNumber != ''">
                and t.work_order_number like concat('%', #{workOrderNumber} ,'%')
            </if>
            <if test="ticketType != null and ticketType != ''">
                and t.ticket_type like concat('%', #{ticketType} ,'%')
            </if>
            <if test="status != null and status != ''">
                and t.status = #{status}
            </if>
            <if test="type != null and type != ''">
                and t.type = #{type}
            </if>
            <if test="settlementDate != null">
                and t.settlement_date = #{settlementDate}
            </if>
            <if test="productName != null and productName != ''">
                and t.product_name like concat('%', #{productName} ,'%')
            </if>
            <if test="productModel != null and productModel != ''">
                and t.product_model like concat('%', #{productModel} ,'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and t.manufacturer like concat('%', #{manufacturer} ,'%')
            </if>
            <if test="batchNumber != null and batchNumber != ''">
                and t.batch_number like concat('%', #{batchNumber} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pc.category_name like concat('%', #{productCategory} ,'%')
            </if>
            <if test="productInformationName != null and productInformationName != ''">
                and t.product_information_name like concat('%', #{productInformationName} ,'%')
            </if>
            <if test="productInformation1 != null and productInformation1 != ''">
                and t.product_information1 = #{productInformation1}
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and w.entrusted_unit like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="productQuantity != null and productQuantity != ''">
                and t.product_quantity = #{productQuantity}
            </if>
            <if test="chargingStandardName != null and chargingStandardName != ''">
                and t.charging_standard_name like concat('%', #{chargingStandardName} ,'%')
            </if>
            <if test="internalAccountingPrice != null and internalAccountingPrice != ''">
                and t.internal_accounting_price = #{internalAccountingPrice}
            </if>
            <if test="discount != null and discount != ''">
                and t.discount = #{discount}
            </if>
            <if test="customerAccountingPrice != null and customerAccountingPrice != ''">
                and t.customer_accounting_price = #{customerAccountingPrice}
            </if>
            <if test="settlementPrice != null and settlementPrice != ''">
                and t.settlement_price = #{settlementPrice}
            </if>
            <if test="internalPriceClassification != null and internalPriceClassification != ''">
                and t.internal_price_classification like concat('%', #{internalPriceClassification} ,'%')
            </if>
            <if test="customerPriceClassification != null and customerPriceClassification != ''">
                and t.customer_price_classification like concat('%', #{customerPriceClassification} ,'%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and t.order_number like concat('%', #{orderNumber} ,'%')
            </if>
            <if test="settlementUnit != null and settlementUnit != ''">
                and t.settlement_unit like concat('%', #{settlementUnit} ,'%')
            </if>
            <if test="numberOfQualifiedProductions != null and numberOfQualifiedProductions != ''">
                and t.number_of_qualified_productions = #{numberOfQualifiedProductions}
            </if>
            <if test="numNonQualProd != null and numNonQualProd != ''">
                and t.num_non_qual_prod = #{numNonQualProd}
            </if>
            <if test="nonQualityProcess != null and nonQualityProcess != ''">
                and t.non_quality_process like concat('%', #{nonQualityProcess} ,'%')
            </if>
            <if test="testType != null and testType != ''">
                and t.test_type = #{testType}
            </if>
            <if test="dateOfEntrustment != null">
                and t.date_of_entrustment = #{dateOfEntrustment}
            </if>
            <if test="orderInspectionNumber != null and orderInspectionNumber != ''">
                and t.order_inspection_number like concat('%', #{orderInspectionNumber} ,'%')
            </if>
            <if test="workOrderInspectionNumber1 != null and workOrderInspectionNumber1 != ''">
                and t.work_order_inspection_number1 like concat('%', #{workOrderInspectionNumber1} ,'%')
            </if>
            <if test="billStatementNumber != null and billStatementNumber != ''">
                and t.bill_statement_number like concat('%', #{billStatementNumber} ,'%')
            </if>
            <if test="contractNumber != null and contractNumber != ''">
                and t.contract_number like concat('%', #{contractNumber} ,'%')
            </if>
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>

        order by  t.codex_torch_create_datetime desc
    </select>

    <select id="selectProductionValueCalculationList"
            parameterType="com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationVO">
        select
        <include refid="Base_Column_List"/>
        from production_value_calculation t
        left join customer_information_management w on t.entrusted_unit = w.id
        left join product_category pc on t.product_category = pc.id
        <where>
            and t.codex_torch_deleted = '0'
            <if test="workOrderNumber != null and workOrderNumber != ''">
                and t.work_order_number like concat('%', #{workOrderNumber} ,'%')
            </if>
            <if test="ticketType != null and ticketType != ''">
                and t.ticket_type like concat('%', #{ticketType} ,'%')
            </if>
            <if test="status != null and status != ''">
                and t.status = #{status}
            </if>
            <if test="type != null and type != ''">
                and t.type = #{type}
            </if>
            <if test="settlementDate != null">
                and t.settlement_date = #{settlementDate}
            </if>
            <if test="productName != null and productName != ''">
                and t.product_name like concat('%', #{productName} ,'%')
            </if>
            <if test="productModel != null and productModel != ''">
                and t.product_model like concat('%', #{productModel} ,'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and t.manufacturer like concat('%', #{manufacturer} ,'%')
            </if>
            <if test="batchNumber != null and batchNumber != ''">
                and t.batch_number like concat('%', #{batchNumber} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pc.category_name like concat('%', #{productCategory} ,'%')
            </if>
            <if test="productInformationName != null and productInformationName != ''">
                and t.product_information_name like concat('%', #{productInformationName} ,'%')
            </if>
            <if test="productInformation1 != null and productInformation1 != ''">
                and t.product_information1 = #{productInformation1}
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and w.entrusted_unit like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="productQuantity != null and productQuantity != ''">
                and t.product_quantity = #{productQuantity}
            </if>
            <if test="chargingStandardName != null and chargingStandardName != ''">
                and t.charging_standard_name like concat('%', #{chargingStandardName} ,'%')
            </if>
            <if test="internalAccountingPrice != null and internalAccountingPrice != ''">
                and t.internal_accounting_price = #{internalAccountingPrice}
            </if>
            <if test="discount != null and discount != ''">
                and t.discount = #{discount}
            </if>
            <if test="customerAccountingPrice != null and customerAccountingPrice != ''">
                and t.customer_accounting_price = #{customerAccountingPrice}
            </if>
            <if test="settlementPrice != null and settlementPrice != ''">
                and t.settlement_price = #{settlementPrice}
            </if>
            <if test="internalPriceClassification != null and internalPriceClassification != ''">
                and t.internal_price_classification like concat('%', #{internalPriceClassification} ,'%')
            </if>
            <if test="customerPriceClassification != null and customerPriceClassification != ''">
                and t.customer_price_classification like concat('%', #{customerPriceClassification} ,'%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and t.order_number like concat('%', #{orderNumber} ,'%')
            </if>
            <if test="settlementUnit != null and settlementUnit != ''">
                and t.settlement_unit like concat('%', #{settlementUnit} ,'%')
            </if>
            <if test="numberOfQualifiedProductions != null and numberOfQualifiedProductions != ''">
                and t.number_of_qualified_productions = #{numberOfQualifiedProductions}
            </if>
            <if test="numNonQualProd != null and numNonQualProd != ''">
                and t.num_non_qual_prod = #{numNonQualProd}
            </if>
            <if test="nonQualityProcess != null and nonQualityProcess != ''">
                and t.non_quality_process like concat('%', #{nonQualityProcess} ,'%')
            </if>
            <if test="testType != null and testType != ''">
                and t.test_type = #{testType}
            </if>
            <if test="dateOfEntrustment != null">
                and t.date_of_entrustment = #{dateOfEntrustment}
            </if>
            <if test="orderInspectionNumber != null and orderInspectionNumber != ''">
                and t.order_inspection_number like concat('%', #{orderInspectionNumber} ,'%')
            </if>
            <if test="workOrderInspectionNumber1 != null and workOrderInspectionNumber1 != ''">
                and t.work_order_inspection_number1 like concat('%', #{workOrderInspectionNumber1} ,'%')
            </if>
            <if test="billStatementNumber != null and billStatementNumber != ''">
                and t.bill_statement_number like concat('%', #{billStatementNumber} ,'%')
            </if>
            <if test="contractNumber != null and contractNumber != ''">
                and t.contract_number like concat('%', #{contractNumber} ,'%')
            </if>
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectProductionValueCalculationListByIds"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationVO">
        select
        <include refid="Base_Column_List"/>
        from production_value_calculation t
        left join customer_information_management w on t.entrusted_unit = w.id
        left join product_category pc on t.product_category = pc.id
        <where>
            <if test="ids != null and ids.size > 0">
                t.id in
                <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>