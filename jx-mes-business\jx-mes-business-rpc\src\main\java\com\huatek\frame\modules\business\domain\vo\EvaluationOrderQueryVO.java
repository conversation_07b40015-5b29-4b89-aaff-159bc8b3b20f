package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 新增测评订单根据委托单位+订单类型 返回报告相关字段VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationOrderQueryVO {
    /**
     * 报告需求
     **/

    private String reportRequirements;


    /**
     * 报告形式
     **/

    private String reportFormat;


    /**
     * 数据形式
     **/

    private String dataFormat;


    /**
     * 电子版报告数据要求
     **/

    private String dataReqERep;


    /**
     * 纸质版报告数据要求
     **/

    private String dataReqsPapereport;


    /**
     * 其他要求
     **/

    private String otherRequirements;


    /**
     * 备注
     **/

    private String comment;

}
