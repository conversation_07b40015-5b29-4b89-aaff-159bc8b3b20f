package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.modules.business.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* @description 设备台账DTO 实体类
* <AUTHOR>
* @date 2025-07-18
**/
@Data
@ApiModel("设备台账 sample DTO实体类")
public class EquipmentInventorySampleVO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 设备编号
     **/
    @ApiModelProperty("设备编号")
    private String deviceSerialNumber;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;


}