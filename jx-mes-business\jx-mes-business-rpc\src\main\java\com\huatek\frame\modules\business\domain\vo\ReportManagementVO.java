package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 报告管理VO实体类
* <AUTHOR>
* @date 2025-08-15
**/
@Data
@ApiModel("报告管理DTO实体类")
public class ReportManagementVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 报告编号
     **/
    @ApiModelProperty("报告编号")
    @Excel(name = "报告编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reportNumber;

    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workOrderNumber;

    /**
	 * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String testType;

    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderNumber;

    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String entrustedUnit;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productName;

    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;

    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productCategory;

    /**
	 * 报告编制人
     **/
    @ApiModelProperty("报告编制人")
    @Excel(name = "报告编制人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String preparerOfTheReport;

    /**
	 * 编制时间
     **/
    @ApiModelProperty("编制时间")
    @Excel(name = "编制时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd HH:mm:ss",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp compilationTime;

    /**
	 * 报告状态
     **/
    @ApiModelProperty("报告状态")
    @Excel(name = "报告状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reportStatus;

    /**
	 * 附件
     **/
    @ApiModelProperty("附件")
    @Excel(name = "附件",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String attachment;

    /**
	 * 申请人
     **/
    @ApiModelProperty("申请人")
    @Excel(name = "申请人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String codexTorchApplicant;

    /**
	 * 待审批人
     **/
    @ApiModelProperty("待审批人")
    @Excel(name = "待审批人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String codexTorchApprover;

    /**
	 * 审批人列表
     **/
    @ApiModelProperty("审批人列表")
    @Excel(name = "审批人列表",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String codexTorchApprovers;

    /**
	 * 流程状态
     **/
    @ApiModelProperty("流程状态")
    @Excel(name = "流程状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String codexTorchApprovalStatus;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}