package com.huatek.frame.modules.business.service;

import com.huatek.frame.modules.business.domain.MaterialsLibrary;
import com.huatek.frame.modules.business.service.dto.MaterialsLibraryDTO;
import com.huatek.frame.modules.business.domain.vo.MaterialsLibraryVO;
import com.huatek.frame.common.response.TorchResponse;
import java.util.List;


/**
* @description 材料库Service
* <AUTHOR>
* @date 2025-08-08
**/
public interface MaterialsLibraryService {
    
    /**
	 * 分页查找查找 材料库
	 * 
	 * @param dto 材料库dto实体对象
	 * @return 
	 */
	TorchResponse<List<MaterialsLibraryVO>> findMaterialsLibraryPage(MaterialsLibraryDTO dto);

    /**
	 * 添加 \修改 材料库
	 * 
	 * @param materialsLibraryDto 材料库dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(MaterialsLibraryDTO materialsLibraryDto);
	
	/**
	 * 通过id查找材料库
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<MaterialsLibraryVO> findMaterialsLibrary(String id);
	
	/**
	 * 删除 材料库
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 材料库
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<MaterialsLibraryVO>> getOptionsList(String id);




    /**
     * 根据条件查询材料库列表
     *
     * @param dto 材料库信息
     * @return 材料库集合信息
     */
    List<MaterialsLibraryVO> selectMaterialsLibraryList(MaterialsLibraryDTO dto);

    /**
     * 导入材料库数据
     *
     * @param materialsLibraryList 材料库数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importMaterialsLibrary(List<MaterialsLibraryVO> materialsLibraryList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取材料库数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectMaterialsLibraryListByIds(List<String> ids);


}