<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.SystemFileMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.controlled_number as controlledNumber,
		t.file_type as fileType,
		t.file_name as fileName,
<!--		t.department as department,-->
		t.effective_time as effectiveTime,
		t.attachment as attachment,
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectSystemFilePage" parameterType="com.huatek.frame.modules.business.service.dto.SystemFileDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.SystemFileVO">
		select
		<include refid="Base_Column_List" />,sg.group_name as department
			from system_file t left join sys_group sg on t.department = sg.id
            <where>
                and 1=1
                <if test="controlledNumber != null and controlledNumber != ''">
                    and t.controlled_number  like concat('%', #{controlledNumber} ,'%')
                </if>
                <if test="fileType != null and fileType != ''">
                    and t.file_type  = #{fileType}
                </if>
                <if test="fileName != null and fileName != ''">
                    and t.file_name  like concat('%', #{fileName} ,'%')
                </if>
                <if test="department != null and department != ''">
                    and t.department  = #{department}
                </if>
                <if test="effectiveTime != null">
                    and t.effective_time  = #{effectiveTime}
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
        order by t.CODEX_TORCH_CREATE_DATETIME desc
	</select>
     <select id="selectOptionsByDepartment" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.group_name label,
        	t.id value
        from sys_group t
        WHERE t.group_code != ''
     </select>

    <select id="selectSystemFileList" parameterType="com.huatek.frame.modules.business.service.dto.SystemFileDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.SystemFileVO">
		select
		<include refid="Base_Column_List" />,sg.group_name as department
        from system_file t left join sys_group sg on t.department = sg.id
            <where>
                and 1=1
                <if test="controlledNumber != null and controlledNumber != ''">
                    and t.controlled_number  like concat('%', #{controlledNumber} ,'%')
                </if>
                <if test="fileType != null and fileType != ''">
                    and t.file_type  = #{fileType}
                </if>
                <if test="fileName != null and fileName != ''">
                    and t.file_name  like concat('%', #{fileName} ,'%')
                </if>
                <if test="department != null and department != ''">
                    and t.department  = #{department}
                </if>
                <if test="effectiveTime != null">
                    and t.effective_time  = #{effectiveTime}
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectSystemFileListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.SystemFileVO">
		select
		<include refid="Base_Column_List" />,sg.group_name as department
        from system_file t left join sys_group sg on t.department = sg.id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>