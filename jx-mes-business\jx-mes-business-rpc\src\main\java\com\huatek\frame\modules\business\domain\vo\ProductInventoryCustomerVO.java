package com.huatek.frame.modules.business.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("发货信息DTO实体类")
public class ProductInventoryCustomerVO {

    @ApiModelProperty("发货ID")
    private String id;        // id

    @ApiModelProperty("收件人姓名")
    private String recipientsName;        // 收件人姓名

    @ApiModelProperty("收件人电话")
    private String recipientsPhoneNumber; // 收件人电话

    @ApiModelProperty("收件人地址")
    private String recipientsAddress;     // 收件人地址

    @ApiModelProperty("收件人地址信息")
    private String customerInfo;     // 收件人地址信息


}
