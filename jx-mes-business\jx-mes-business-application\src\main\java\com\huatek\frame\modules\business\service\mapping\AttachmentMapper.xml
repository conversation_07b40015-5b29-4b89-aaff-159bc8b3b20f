<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.AttachmentMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.work_order_code as workOrderCode,
		t.process_code as processCode,
		t.original_file_name as originalFileName,
		t.server_file_name as serverFileName,
		t.file_path as filePath,
		t.file_size as fileSize,
		t.file_type as fileType,
		t.suffix as suffix,
		t.description as description,
		t.business_type as businessType,
		t.upload_time as uploadTime,
		t.update_time as updateTime
	</sql>

	<select id="selectAttachmentPage" parameterType="com.huatek.frame.modules.business.service.dto.AttachmentDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.AttachmentVO">
		select
		<include refid="Base_Column_List" />
			from attachment t
            <where>
                <if test="workOrderCode != null and workOrderCode != ''">
                    and t.work_order_code like concat('%', #{workOrderCode} ,'%')
                </if>
                <if test="processCode != null and processCode != ''">
                    and t.process_code like concat('%', #{processCode} ,'%')
                </if>
                <if test="originalFileName != null and originalFileName != ''">
                    and t.original_file_name like concat('%', #{originalFileName} ,'%')
                </if>
                <if test="fileType != null and fileType != ''">
                    and t.file_type = #{fileType}
                </if>
                <if test="businessType != null and businessType != ''">
                    and t.business_type = #{businessType}
                </if>
                <if test="suffix != null and suffix != ''">
                    and t.suffix = #{suffix}
                </if>
            </where>
            order by t.upload_time desc
	</select>

    <select id="selectAttachmentList" parameterType="com.huatek.frame.modules.business.service.dto.AttachmentDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.AttachmentVO">
		select
		<include refid="Base_Column_List" />
			from attachment t
            <where>
                <if test="workOrderCode != null and workOrderCode != ''">
                    and t.work_order_code like concat('%', #{workOrderCode} ,'%')
                </if>
                <if test="processCode != null and processCode != ''">
                    and t.process_code like concat('%', #{processCode} ,'%')
                </if>
                <if test="originalFileName != null and originalFileName != ''">
                    and t.original_file_name like concat('%', #{originalFileName} ,'%')
                </if>
                <if test="fileType != null and fileType != ''">
                    and t.file_type = #{fileType}
                </if>
                <if test="businessType != null and businessType != ''">
                    and t.business_type = #{businessType}
                </if>
                <if test="suffix != null and suffix != ''">
                    and t.suffix = #{suffix}
                </if>
            </where>
            order by t.upload_time desc
	</select>

	<select id="selectAttachmentListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.AttachmentVO">
		select
		<include refid="Base_Column_List" />
			from attachment t
            <where>
                and t.id in
                <foreach collection="ids" separator="," open="(" close=")" item="id">
                    #{id}
                </foreach>
            </where>
            order by t.upload_time desc
	</select>
	
	<select id="selectAttachmentListByWorkOrderCode"
		resultType="com.huatek.frame.modules.business.domain.vo.AttachmentVO">
		select
		<include refid="Base_Column_List" />
			from attachment t
            <where>
                and t.work_order_code = #{workOrderCode}
            </where>
            order by t.upload_time desc
	</select>
	
	<select id="selectAttachmentListByWorkOrderAndProcess"
		resultType="com.huatek.frame.modules.business.domain.vo.AttachmentVO">
		select
		<include refid="Base_Column_List" />
			from attachment t
            <where>
                and t.work_order_code = #{workOrderCode}
                and t.process_code = #{processCode}
            </where>
            order by t.upload_time desc
	</select>
</mapper>
