package com.huatek.frame.modules.business.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
* @description 生产任务VO实体类
* <AUTHOR>
* @date 2025-08-11
**/
@Data
@ApiModel("生产任务View实体类")
public class ProductionTaskViewVO implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
	 * 序号
     **/
    @ApiModelProperty("序号")
    private String displayNumber;
    @ApiModelProperty("执行顺序")
    private Integer executionSequence;

    /**
     * 关联工单前置工序
     **/
    @ApiModelProperty("关联工单前置工序")
    @Excel(name = "关联工单前置工序",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String assoWoPredProc;
    /**
	 * 任务编号
     **/
    @ApiModelProperty("任务编号")
    @Excel(name = "任务编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String taskNumber;

    /**
	 * 工序名称
     **/
    @ApiModelProperty("工序名称")
    @Excel(name = "工序名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String processName2;
    /**
     * 工序编码
     **/
    @ApiModelProperty("工序编码")
    @Excel(name = "工序编码",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String processCode;

    /**
     * 客户工序名称
     **/
    @ApiModelProperty("客户工序名称")
    @Excel(name = "客户工序名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String customerProcessName;

    /**
     * 试验依据
     **/
    @ApiModelProperty("试验依据")
    @Excel(name = "试验依据",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testBasis;

    /**
	 * 试验条件
     **/
    @ApiModelProperty("试验条件")
    @Excel(name = "试验条件",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String testConditions;

    /**
     * 试验次数
     **/
    @ApiModelProperty("试验次数")
    @Excel(name = "试验次数",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer testingTimes;

    /**
     * 试验时长
     **/
    @ApiModelProperty("试验时长")
    @Excel(name = "试验时长",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private BigDecimal durationOfTesting;

    /**
     * 判定依据
     **/
    @ApiModelProperty("判定依据")
    @Excel(name = "判定依据",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String judgmentCriteria;
    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String status;


    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    @Excel(name = "产品资料",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productInformation1;
    private String productInformationAttachment;
    /**
     * 工作站
     **/
    @ApiModelProperty("工作站")
    @Excel(name = "工作站",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workstation;
    private String workstationName;


    /**
	 * 试验方式
     **/
    @ApiModelProperty("试验方式")
    @Excel(name = "试验方式",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String testMethodology;

    /**
     * 组别
     **/
    @ApiModelProperty("组别")
    @Excel(name = "组别",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String grouping;
    private String groupingName;


    /**
     * 是否加入排产
     **/
    @ApiModelProperty("是否加入排产")
    @Excel(name = "是否加入排产",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String whetherToIncludeInScheduling;

    /**
     * 预计开始时间
     **/
    @ApiModelProperty("预计开始时间")
    private Timestamp estimatedStartTime;
    /**
     * 预计结束时间
     **/
    @ApiModelProperty("预计结束时间")
    private Timestamp estimatedEndTime;

    /**
     * 试验班组
     **/
    @ApiModelProperty("试验班组")
    @Excel(name = "试验班组",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testingTeam;
    private String testingTeamName;

    /**
	 * 所属班组
     **/
    @ApiModelProperty("所属班组")
    @Excel(name = "所属班组",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String belongingTeam2;

    /**
	 * 报工时间
     **/
    @ApiModelProperty("报工时间")
    @Excel(name = "报工时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd HH:mm:ss",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp reportingTime0;


    /**
	 * 报工人
     **/
    @ApiModelProperty("报工人")
    @Excel(name = "报工人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reporter4;


    /**
	 * 合格数量
     **/
    @ApiModelProperty("合格数量")
    @Excel(name = "合格数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Integer qualifiedQuantity;

    /**
	 * 不合格数量
     **/
    @ApiModelProperty("不合格数量")
    @Excel(name = "不合格数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Integer unqualifiedQuantity;

    /**
	 * 失效模式
     **/
    @ApiModelProperty("失效模式")
    @Excel(name = "失效模式",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String failureMode;

    /**
	 * PDA
     **/
    @ApiModelProperty("PDA")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "PDA",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal pda;

    /**
     * 送检数量
     **/
    @ApiModelProperty("送检数量")
    @Excel(name = "送检数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer inspectionQuantity2;
    /**
	 * 关联异常反馈编号
     **/
    @ApiModelProperty("关联异常反馈编号")
    @Excel(name = "关联异常反馈编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String assocExceptionFeedbackNum;


    @ApiModelProperty("审批时间")
    @Excel(name = "审批时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd HH:mm:ss",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String approveTime;

    @ApiModelProperty("设备编号")
    private String deviceNumbers;

    @ApiModelProperty("试验数据")
    private List<ProductionTaskTestDataVO> taskTestData;

    @ApiModelProperty("设备信息")
    private List<ProdTaskEqInfoVO> prodTaskEqInfoList;

    @ApiModelProperty("设备原始数据")
    private List<AttachmentVO> attachmentVOList;
    @ApiModelProperty("任务操作历史")
    private List<ProdTaskOpHistVO> taskOpHistDTOS;

}