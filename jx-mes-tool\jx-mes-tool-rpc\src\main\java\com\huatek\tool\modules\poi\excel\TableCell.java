package com.huatek.tool.modules.poi.excel;

import java.io.Serializable;

/**
 * 表格单元格对象
 * @name Cell
 * @description 
 * @version V
 * <AUTHOR>
 * @time 2025年9月9日 上午9:53:37
 */
public class TableCell implements Serializable {

	private static final long serialVersionUID = 1L;
    
	/**
	 * 列跨度
	 */
	private int cellSpan;
	
	/**
	 * 行跨度
	 */
	private int rowSpan;
	
	/**
	 * 内容
	 */
	private String value;

	public int getCellSpan() {
		return cellSpan;
	}

	public void setCellSpan(int cellSpan) {
		this.cellSpan = cellSpan;
	}

	public int getRowSpan() {
		return rowSpan;
	}

	public void setRowSpan(int rowSpan) {
		this.rowSpan = rowSpan;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	
}
