<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.SawOrderMapper">
	<sql id="Base_Column_List">
		t.id as id,
        t.order_id as orderId,
        t.product_list_id asproductListId,
		t.work_order_number as workOrderNumber,
		t.acceptance_type as acceptanceType,
		t.responsible_person as responsible<PERSON><PERSON>,
		t.status as status,
		t.completion_date as completionDate,
		t.`comment` as `comment`,
		t.attachment as Attachment,
		t.codex_torch_detail_item_ids as codexTorchDetailItemIds,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as createDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectSawOrderPage" parameterType="com.huatek.frame.modules.business.service.dto.SawOrderPageDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.SawOrderVO">
		select DISTINCT
		<include refid="Base_Column_List" />,
        e.order_number as orderNumber,
        e.purchase_order_contract_number as orderContractNumber,
        e.engineering_code as engineeringCode,
        e.urgency_level as urgencyLevel,
        e.order_type as orderType,
        e.acceptance_notice as acceptanceNotice,
        e.comment as orderRemarks,
        c.entrusted_unit as entrustedUnit,
        c.customer_manager as customerManager,
        p.test_type as testType,
        p.deadline as deadline,
        p.manufacturer as manufacturer,
        u.user_name as creator,
        g.group_name as belongGroup
        from saw_order t
        LEFT JOIN evaluation_order e ON e.id = t.order_id
        LEFT JOIN product_list p ON p.id = t.product_list_id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        LEFT JOIN sys_user u ON u.id = t.codex_torch_creator_id
        LEFT JOIN sys_group g ON g.id = t.codex_torch_group_id
        <where>
            and t.codex_torch_deleted = '0'
            <if test="workOrderNumber != null and workOrderNumber != ''">
                and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and e.order_number  like concat('%', #{orderNumber} ,'%')
            </if>
            <if test="orderType != null and orderType != ''">
                and e.order_type  like concat('%', #{orderType} ,'%')
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and c.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="deadline != null">
                and p.deadline  = #{deadline}
            </if>
            <if test="urgencyLevel != null and urgencyLevel != ''">
                and e.urgency_level  like concat('%', #{urgencyLevel} ,'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and p.manufacturer  like concat('%', #{manufacturer} ,'%')
            </if>
            <if test="orderContractNumber != null and orderContractNumber != ''">
                and e.purchase_order_contract_number  like concat('%', #{orderContractNumber} ,'%')
            </if>
            <if test="status != null and status != ''">
                and t.status  = #{status}
            </if>
            <if test="completionDate != null">
                and t.completion_date  = #{completionDate}
            </if>
            ${params.dataScope}
        </where>
        GROUP BY t.id
        ORDER BY t.CODEX_TORCH_CREATE_DATETIME DESC
	</select>
     <select id="selectOptionsByResponsiblePerson" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.name label,
        	t.name value
        from sys_user t
        WHERE t.name != ''
         and t.deleted = '0'
     </select>

    <select id="selectSawOrderList" parameterType="com.huatek.frame.modules.business.service.dto.SawOrderDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.SawOrderVO">
		select
		<include refid="Base_Column_List" />
			from saw_order t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="orderType != null and orderType != ''">
                    and t.order_type  like concat('%', #{orderType} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="deadline != null and deadline != ''">
                    and t.deadline  like concat('%', #{deadline} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="customerManager != null and customerManager != ''">
                    and t.customer_manager  like concat('%', #{customerManager} ,'%')
                </if>
                <if test="engineeringCode != null and engineeringCode != ''">
                    and t.engineering_code  like concat('%', #{engineeringCode} ,'%')
                </if>
                <if test="urgencyLevel != null and urgencyLevel != ''">
                    and t.urgency_level  like concat('%', #{urgencyLevel} ,'%')
                </if>
                <if test="acceptanceNotice != null and acceptanceNotice != ''">
                    and t.acceptance_notice  like concat('%', #{acceptanceNotice} ,'%')
                </if>
                <if test="orderContractNumber != null and orderContractNumber != ''">
                    and t.order_contract_number  like concat('%', #{orderContractNumber} ,'%')
                </if>
                <if test="orderRemarks != null and orderRemarks != ''">
                    and t.order_remarks  like concat('%', #{orderRemarks} ,'%')
                </if>
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="acceptanceType != null and acceptanceType != ''">
                    and t.acceptance_type  = #{acceptanceType}
                </if>
                <if test="responsiblePerson != null and responsiblePerson != ''">
                    and t.responsible_person  = #{responsiblePerson}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="completionDate != null">
                    and t.completion_date  = #{completionDate}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="Attachment != null and Attachment != ''">
                    and t.attachment  = #{Attachment}
                </if>
                <if test="codexTorchDetailItemIds != null and codexTorchDetailItemIds != ''">
                    and t.codex_torch_detail_item_ids  like concat('%', #{codexTorchDetailItemIds} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectSawOrderListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.SawOrderVO">
		select
		<include refid="Base_Column_List" />, e.order_number as orderNumber, c.entrusted_unit as entrustedUnit,
        e.order_inspection_number as orderInspectionNumber, e.engineering_code as engineeringCode
			from saw_order t
        left join evaluation_order e on e.id = t.order_id
        left join customer_information_management c on c.id = e.customer_id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
    <select id="findSawOrder" resultType="com.huatek.frame.modules.business.domain.vo.SawOrderVO">
        select
        <include refid="Base_Column_List"/>,
        e.order_number as orderNumber,
        e.purchase_order_contract_number as orderContractNumber,
        e.engineering_code as engineeringCode,
        e.urgency_level as urgencyLevel,
        e.acceptance_notice as acceptanceNotice,
        e.comment as orderRemarks,
        e.order_type as orderType,
        c.entrusted_unit as entrustedUnit,
        c.customer_manager as customerManager,
        p.test_type as testType,
        p.deadline as deadline,
        p.manufacturer as manufacturer
        from saw_order t
        LEFT JOIN evaluation_order e ON e.id = t.order_id
        LEFT JOIN product_list p ON p.id = t.product_list_id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        where t.id = #{id} and t.codex_torch_deleted = '0'
    </select>
</mapper>