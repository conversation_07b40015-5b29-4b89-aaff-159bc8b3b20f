package com.huatek.frame.modules.business.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description 审批驳回任务DTO
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ApiModel("审批驳回任务DTO")
public class RejectTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID列表
     */
    @ApiModelProperty("任务ID列表")
    private List<String> ids;

    /**
     * 驳回原因
     */
    @ApiModelProperty("驳回原因")
    private String reason;
}
