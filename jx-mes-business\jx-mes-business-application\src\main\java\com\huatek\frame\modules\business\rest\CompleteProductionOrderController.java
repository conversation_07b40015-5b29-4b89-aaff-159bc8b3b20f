package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderVO;
import com.huatek.frame.modules.business.service.AwaitingProductionOrderService;
import com.huatek.frame.modules.business.service.ProductionOrderResultService;
import com.huatek.frame.modules.business.service.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* <AUTHOR>
* @date 2025-07-30
**/
@Api(tags = "完成工单管理")
@RestController
@RequestMapping("/api/completeProductionOrder")
public class CompleteProductionOrderController {

	@Autowired
    private AwaitingProductionOrderService awaitingProductionOrderService;

    @Autowired
    private ProductionOrderResultService productionOrderResultService;
	/**
	 * 待制工单列表
	 * 
	 * @param dto 待制工单DTO 实体对象
	 * @return
	 */
    @Log("工单列表")
    @ApiOperation(value = "工单列表查询")
    @PostMapping(value = "/awaitingProductionOrderList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("completeProductionOrder:list")
    public TorchResponse<List<ProductionOrderVO>> query(@RequestBody ProductionOrderDTO dto){
        return awaitingProductionOrderService.findAwaitingProductionOrderPage(dto);
    }

	/**
	 * 新增/修改待制工单
	 * 
	 * @param awaitingProductionOrderDto 待制工单DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改工单")
    @ApiOperation(value = "工单新增/修改操作")
    @PostMapping(value = "/awaitingProductionOrder", produces = { "application/json;charset=utf-8" })
    @TorchPerm("completeProductionOrder:add#completeProductionOrder:edit")
    public TorchResponse add(@RequestBody ProductionOrderDTO awaitingProductionOrderDto) throws Exception {
		// BeanValidatorFactory.validate(awaitingProductionOrderDto);
		return awaitingProductionOrderService.saveOrUpdate(awaitingProductionOrderDto);
	}

	/**
	 * 查询待制工单详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("工单详情")
    @ApiOperation(value = "工单详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("completeProductionOrder:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return awaitingProductionOrderService.findAwaitingProductionOrder(id);
	}

    /**
     * 工单查看
     *
     * @param id 主键id
     * @return
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("工单查看")
    @ApiOperation(value = "工单查看")
    @GetMapping(value = "/view/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("completeProductionOrder:detail")
    public TorchResponse view(@PathVariable(value = "id") String id) {
        return awaitingProductionOrderService.findAwaitingProductionOrderView(id);
    }

    @ApiOperation(value = "待制工单联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return awaitingProductionOrderService.getOptionsList(id);
	}



    @ApiOperation(value = "待制工单 自动填充数据查询")
    @GetMapping(value = "/linkageData/{linkageDataTableName}/{conditionalValue}", produces = { "application/json;charset=utf-8" })
    public TorchResponse getLinkageData(@PathVariable(value = "linkageDataTableName") String linkageDataTableName,
    									@PathVariable(value = "conditionalValue") String conditionalValue) {
		return awaitingProductionOrderService.getLinkageData(linkageDataTableName, conditionalValue);
	}




    @Log("入库")
    @ApiOperation(value = "入库")
    @TorchPerm("completeProductionOrder:instore")
    @PostMapping(value = "/productionOrderInstore", produces = {"application/json;charset=utf-8"})
    public TorchResponse productionOrderInstore(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.productionOrderInstore(productionOrderDTO);
    }

    @Log("查询工单试验数据")
    @ApiOperation(value = "查询工单试验数据")
    @TorchPerm("completeProductionOrder:testData")
    @PostMapping(value = "/getProductionOrderTestData", produces = {"application/json;charset=utf-8"})
    public TorchResponse getProductionOrderTestData(@RequestBody ProductionOrderResultDTO productionOrderResultDTO) {
        return productionOrderResultService.getProductionOrderTestData(productionOrderResultDTO);
    }

    @Log("保存工单试验数据")
    @ApiOperation(value = "保存工单试验数据")
    @TorchPerm("completeProductionOrder:testData")
    @PostMapping(value = "/saveProductionOrderTestData", produces = {"application/json;charset=utf-8"})
    public TorchResponse saveProductionOrderTestData(@RequestBody ProductionOrderResultDTO productionOrderResultDTO) {
        return productionOrderResultService.saveProductionOrderTestData(productionOrderResultDTO);
    }

    @Log("生成报告")
    @ApiOperation(value = "生成报告")
    @TorchPerm("completeProductionOrder:createReport")
    @PostMapping(value = "/createProductionOrderReport", produces = {"application/json;charset=utf-8"})
    public TorchResponse createProductionOrderReport(@RequestBody ProductionOrderDTO productionOrderDTO) throws IOException {
        return awaitingProductionOrderService.createProductionOrderReport(productionOrderDTO);
    }


    @Log("标识卡导出")
    @ApiOperation(value = "标识卡导出")
    @TorchPerm("completeProductionOrder:markCard")
    @PostMapping("/exportCard")
    public void exportCard(HttpServletResponse response, @RequestBody List<String> ids){
         awaitingProductionOrderService.exportCard(response, ids);
    }

    @Log("标识卡导出（自定义列数）")
    @ApiOperation(value = "标识卡导出（自定义列数）", notes = "cardsPerRow参数：1=单列，2=双列，3=三列等")
    @TorchPerm("completeProductionOrder:markCard")
    @PostMapping("/exportCard/{cardsPerRow}")
    public void exportCardWithColumns(HttpServletResponse response,
                                    @RequestBody List<String> ids,
                                    @PathVariable("cardsPerRow") int cardsPerRow){
         awaitingProductionOrderService.exportCard(response, ids, cardsPerRow);
    }
}