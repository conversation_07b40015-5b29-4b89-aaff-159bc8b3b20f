package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 客户工序方案
* <AUTHOR>
* @date 2025-07-17
**/
@Setter
@Getter
@TableName("customer_process_scheme")
public class CustomerProcessScheme implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 生产工单
     **/
    @TableField(value = "work_order"
    )
    private String workOrder;


    /**
     * 试验类型
     **/
    @TableField(value = "test_type"
    )
    private String testType;

    /**
     * 委托单位
     **/
    @TableField(value = "entrusted_unit"
    )
    private String entrustedUnit;


    /**
     * 产品型号
     **/
    @TableField(value = "product_model"
    )
    private String productModel;

    /**
     * 产品分类
     **/
    @TableField(value = "product_category"
    )
    private String productCategory;




    /**
     * 标准规范号
     **/
    @TableField(value = "standard_specification_number"
    )
    private String standardSpecificationNumber;




    /**
     * 生产厂家
     **/
    @TableField(value = "manufacturer"
    )
    private String manufacturer;


    /**
     * 质量等级
     **/
    @TableField(value = "quality_grade"
    )
    private String qualityGrade;


    /**
     * 封装形式
     **/
    @TableField(value = "package_form"
    )
    private String packageForm;


    /**
     * PDA
     **/
    @TableField(value = "pda"
    )
    private BigDecimal pda;


    /**
     * 试验包
     **/
    @TableField(value = "test_package"
    )
    private String testPackage;


    /**
     * 状态
     **/
    @TableField(value = "status"
    )
    private String status;


    /**
     * 备注
     **/
    @TableField(value = "`comment`"
    )
    private String comment;

    /**
     * 报告备注
     **/
    @TableField(value = "`report_comment`"
    )
    private String reportComment;


    /**
     * 所属部门
     **/
    @TableField(value = "department"
    )
    private String department;


    /**
     * 子表明细项ID
     **/
    @TableField(value = "CODEX_TORCH_DETAIL_ITEM_IDS"
    )
    private String codexTorchDetailItemIds;


    /**
     * 创建人
     **/
    @TableField(value = "CODEX_TORCH_CREATOR_ID"
    )
    private String codexTorchCreatorId;


    /**
     * 更新人
     **/
    @TableField(value = "CODEX_TORCH_UPDATER"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;


    /**
     * 所属组织
     **/
    @TableField(value = "CODEX_TORCH_GROUP_ID"
    )
    private String codexTorchGroupId;


    /**
     * 创建时间
     **/
    @TableField(value = "CODEX_TORCH_CREATE_DATETIME"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;


    /**
     * 更新时间
     **/
    @TableField(value = "CODEX_TORCH_UPDATE_DATETIME"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;


    /**
     * 已删除
     **/
    @TableField(value = "CODEX_TORCH_DELETED"
    )
    private String codexTorchDeleted;
}