package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
* @description 核算标准明细
* <AUTHOR>
* @date 2025-08-22
**/
@Setter
@Getter
@TableName("accounting_standard_details")
public class AccountingStandardDetails implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 产品分类
     **/
    @TableField(value = "product_category"
    )
    private String productCategory;

    
    /**
	 * 试验项目
     **/
    @TableField(value = "experiment_project"
    )
    private String experimentProject;

    
    /**
	 * 数值
     **/
    @TableField(value = "quantity"
    )
    private BigDecimal quantity;

    
    /**
	 * 单位
     **/
    @TableField(value = "unit"
    )
    private String unit;

    
    /**
	 * 收费形式
     **/
    @TableField(value = "charging_method"
    )
    private String chargingMethod;

    
    /**
	 * 最低数量要求
     **/
    @TableField(value = "minimum_quantity_requirement"
    )
    private Integer minimumQuantityRequirement;

    
    /**
	 * 基础价格
     **/
    @TableField(value = "base_price"
    )
    private BigDecimal basePrice;

    
    /**
	 * 收费标准编号
     **/
    @TableField(value = "charge_standard_number"
    )
    private String chargeStandardNumber;

    
    /**
	 * 低于要求数量计算倍数
     **/
    @TableField(value = "cfbrq"
    )
    private BigDecimal cfbrq;

    
    /**
	 * 主表单ID
     **/
    @TableField(value = "codex_torch_master_form_id"
    )
    private String codexTorchMasterFormId;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}