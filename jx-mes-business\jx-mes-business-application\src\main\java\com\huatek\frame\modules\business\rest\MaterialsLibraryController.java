package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.domain.MaterialsLibrary;
import com.huatek.frame.modules.business.service.MaterialsLibraryService;
import com.huatek.frame.modules.business.service.dto.MaterialsLibraryDTO;
import com.huatek.frame.modules.business.domain.vo.MaterialsLibraryVO;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-08
**/
@Api(tags = "材料库管理")
@RestController
@RequestMapping("/api/materialsLibrary")
public class MaterialsLibraryController {

	@Autowired
    private MaterialsLibraryService materialsLibraryService;

	/**
	 * 材料库列表
	 * 
	 * @param dto 材料库DTO 实体对象
	 * @return
	 */
    @Log("材料库列表")
    @ApiOperation(value = "材料库列表查询")
    @PostMapping(value = "/materialsLibraryList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("materialsLibrary:list")
    public TorchResponse<List<MaterialsLibraryVO>> query(@RequestBody MaterialsLibraryDTO dto){
        return materialsLibraryService.findMaterialsLibraryPage(dto);
    }

	/**
	 * 新增/修改材料库
	 * 
	 * @param materialsLibraryDto 材料库DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改材料库")
    @ApiOperation(value = "材料库新增/修改操作")
    @PostMapping(value = "/materialsLibrary", produces = { "application/json;charset=utf-8" })
    @TorchPerm("materialsLibrary:add#materialsLibrary:edit")
    public TorchResponse add(@RequestBody MaterialsLibraryDTO materialsLibraryDto) throws Exception {
		// BeanValidatorFactory.validate(materialsLibraryDto);
		return materialsLibraryService.saveOrUpdate(materialsLibraryDto);
	}

	/**
	 * 查询材料库详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("材料库详情")
    @ApiOperation(value = "材料库详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("materialsLibrary:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return materialsLibraryService.findMaterialsLibrary(id);
	}

	/**
	 * 删除材料库
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除材料库")
    @ApiOperation(value = "材料库删除操作")
    @TorchPerm("materialsLibrary:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return materialsLibraryService.delete(ids);
	}

    @ApiOperation(value = "材料库联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return materialsLibraryService.getOptionsList(id);
	}





    @Log("材料库导出")
    @ApiOperation(value = "材料库导出")
    @TorchPerm("materialsLibrary:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody MaterialsLibraryDTO dto)
    {
        List<MaterialsLibraryVO> list = materialsLibraryService.selectMaterialsLibraryList(dto);
        ExcelUtil<MaterialsLibraryVO> util = new ExcelUtil<MaterialsLibraryVO>(MaterialsLibraryVO.class);
        util.exportExcel(response, list, "材料库数据");
    }

    @Log("材料库导入")
    @ApiOperation(value = "材料库导入")
    @TorchPerm("materialsLibrary:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<MaterialsLibraryVO> util = new ExcelUtil<MaterialsLibraryVO>(MaterialsLibraryVO.class);
        List<MaterialsLibraryVO> list = util.importExcel(file.getInputStream());
        return materialsLibraryService.importMaterialsLibrary(list, unionColumns, true, "");
    }

    @Log("材料库导入模板")
    @ApiOperation(value = "材料库导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<MaterialsLibraryVO> util = new ExcelUtil<MaterialsLibraryVO>(MaterialsLibraryVO.class);
        util.importTemplateExcel(response, "材料库数据");
    }

    @Log("根据Ids获取材料库列表")
    @ApiOperation(value = "材料库 根据Ids批量查询")
    @PostMapping(value = "/materialsLibraryList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getMaterialsLibraryListByIds(@RequestBody List<String> ids) {
        return materialsLibraryService.selectMaterialsLibraryListByIds(ids);
    }


}