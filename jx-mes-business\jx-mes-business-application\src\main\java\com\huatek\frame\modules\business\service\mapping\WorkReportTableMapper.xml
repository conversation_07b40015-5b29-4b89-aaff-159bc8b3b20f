<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.WorkReportTableMapper">
	<sql id="Base_Column_List">
		t.id as id,
        t.contract_number as contractNumber,
		t.work_order_number as workOrderNumber,
		t.inspection_quantity2 as inspectionQuantity2,
		t.standard_process_name as standardProcessName,
		t.customer_test_name as customerTestName,
		t.product_name as productName,
		t.product_model as productModel,
		t.manufacturer as manufacturer,
		t.batch_number as batchNumber,
		t.product_category as productCategory,
		t.product_information1 as productInformation1,
		t.entrusted_unit as entrustedUnit,
		t.test_methodology as testMethodology,
		t.process_start_time as processStartTime,
		t.process_end_time as processEndTime,
		t.process_duration as processDuration,
		t.reporter4 as reporter4,
		t.rd_task_number as rdTaskNumber,
		t.wipaia as wipaia,
		t.whether_outsourced_process6 as whetherOutsourcedProcess6,
		t.shared_equipment_work_order as sharedEquipmentWorkOrder,
		t.order_number as orderNumber,
		t.sample_total_count as sampleTotalCount,
		t.device_serial_number as deviceSerialNumber,
		t.device_name as deviceName,
		t.fixed_asset_coding as fixedAssetCoding,
		t.duration_of_the_same_process as durationOfTheSameProcess,
		t.equipment_end_time as equipmentEndTime,
		t.equipment_end_time27 as equipmentEndTime27,
		t.equipment_running_timeh as equipmentRunningTimeh,
		t.equipment_energy_consumption as equipmentEnergyConsumption,
		t.equipment_power_consumption as equipmentPowerConsumption,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectWorkReportTablePage" parameterType="com.huatek.frame.modules.business.service.dto.WorkReportTableDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.WorkReportTableVO">
		select
		<include refid="Base_Column_List" />
			from work_report_table t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="contractNumber != null and contractNumber != ''">
                    and t.contract_number  like concat('%', #{contractNumber} ,'%')
                </if>
                <if test="inspectionQuantity2 != null and inspectionQuantity2 != ''">
                    and t.inspection_quantity2  = #{inspectionQuantity2}
                </if>
                <if test="standardProcessName != null and standardProcessName != ''">
                    and t.standard_process_name  like concat('%', #{standardProcessName} ,'%')
                </if>
                <if test="customerTestName != null and customerTestName != ''">
                    and t.customer_test_name  like concat('%', #{customerTestName} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="batchNumber != null and batchNumber != ''">
                    and t.batch_number  like concat('%', #{batchNumber} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and t.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  like concat('%', #{testMethodology} ,'%')
                </if>
                <if test="processStartTime != null">
                    and t.process_start_time  = #{processStartTime}
                </if>
                <if test="processEndTime != null">
                    and t.process_end_time  = #{processEndTime}
                </if>
                <if test="processDuration != null and processDuration != ''">
                    and t.process_duration  = #{processDuration}
                </if>
                <if test="reporter4 != null and reporter4 != ''">
                    and t.reporter4  like concat('%', #{reporter4} ,'%')
                </if>
                <if test="rdTaskNumber != null and rdTaskNumber != ''">
                    and t.rd_task_number  like concat('%', #{rdTaskNumber} ,'%')
                </if>
                <if test="wipaia != null and wipaia != ''">
                    and t.wipaia  like concat('%', #{wipaia} ,'%')
                </if>
                <if test="whetherOutsourcedProcess6 != null and whetherOutsourcedProcess6 != ''">
                    and t.whether_outsourced_process6  like concat('%', #{whetherOutsourcedProcess6} ,'%')
                </if>
                <if test="sharedEquipmentWorkOrder != null and sharedEquipmentWorkOrder != ''">
                    and t.shared_equipment_work_order  like concat('%', #{sharedEquipmentWorkOrder} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="sampleTotalCount != null and sampleTotalCount != ''">
                    and t.sample_total_count  = #{sampleTotalCount}
                </if>
                <if test="deviceSerialNumber != null and deviceSerialNumber != ''">
                    and t.device_serial_number  like concat('%', #{deviceSerialNumber} ,'%')
                </if>
                <if test="deviceName != null and deviceName != ''">
                    and t.device_name  like concat('%', #{deviceName} ,'%')
                </if>
                <if test="fixedAssetCoding != null and fixedAssetCoding != ''">
                    and t.fixed_asset_coding  like concat('%', #{fixedAssetCoding} ,'%')
                </if>
                <if test="durationOfTheSameProcess != null and durationOfTheSameProcess != ''">
                    and t.duration_of_the_same_process  like concat('%', #{durationOfTheSameProcess} ,'%')
                </if>
                <if test="equipmentEndTime != null">
                    and t.equipment_end_time  = #{equipmentEndTime}
                </if>
                <if test="equipmentEndTime27 != null">
                    and t.equipment_end_time27  = #{equipmentEndTime27}
                </if>
                <if test="equipmentRunningTimeh != null and equipmentRunningTimeh != ''">
                    and t.equipment_running_timeh  = #{equipmentRunningTimeh}
                </if>
                <if test="equipmentEnergyConsumption != null and equipmentEnergyConsumption != ''">
                    and t.equipment_energy_consumption  = #{equipmentEnergyConsumption}
                </if>
                <if test="equipmentPowerConsumption != null and equipmentPowerConsumption != ''">
                    and t.equipment_power_consumption  = #{equipmentPowerConsumption}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>

        order by  t.codex_torch_create_datetime desc
	</select>

    <select id="selectWorkReportTableList" parameterType="com.huatek.frame.modules.business.service.dto.WorkReportTableDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.WorkReportTableVO">
		select
		<include refid="Base_Column_List" />
			from work_report_table t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="contractNumber != null and contractNumber != ''">
                    and t.contract_number  like concat('%', #{contractNumber} ,'%')
                </if>
                <if test="inspectionQuantity2 != null and inspectionQuantity2 != ''">
                    and t.inspection_quantity2  = #{inspectionQuantity2}
                </if>
                <if test="standardProcessName != null and standardProcessName != ''">
                    and t.standard_process_name  like concat('%', #{standardProcessName} ,'%')
                </if>
                <if test="customerTestName != null and customerTestName != ''">
                    and t.customer_test_name  like concat('%', #{customerTestName} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="batchNumber != null and batchNumber != ''">
                    and t.batch_number  like concat('%', #{batchNumber} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and t.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  like concat('%', #{testMethodology} ,'%')
                </if>
                <if test="processStartTime != null">
                    and t.process_start_time  = #{processStartTime}
                </if>
                <if test="processEndTime != null">
                    and t.process_end_time  = #{processEndTime}
                </if>
                <if test="processDuration != null and processDuration != ''">
                    and t.process_duration  = #{processDuration}
                </if>
                <if test="reporter4 != null and reporter4 != ''">
                    and t.reporter4  like concat('%', #{reporter4} ,'%')
                </if>
                <if test="rdTaskNumber != null and rdTaskNumber != ''">
                    and t.rd_task_number  like concat('%', #{rdTaskNumber} ,'%')
                </if>
                <if test="wipaia != null and wipaia != ''">
                    and t.wipaia  like concat('%', #{wipaia} ,'%')
                </if>
                <if test="whetherOutsourcedProcess6 != null and whetherOutsourcedProcess6 != ''">
                    and t.whether_outsourced_process6  like concat('%', #{whetherOutsourcedProcess6} ,'%')
                </if>
                <if test="sharedEquipmentWorkOrder != null and sharedEquipmentWorkOrder != ''">
                    and t.shared_equipment_work_order  like concat('%', #{sharedEquipmentWorkOrder} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="sampleTotalCount != null and sampleTotalCount != ''">
                    and t.sample_total_count  = #{sampleTotalCount}
                </if>
                <if test="deviceSerialNumber != null and deviceSerialNumber != ''">
                    and t.device_serial_number  like concat('%', #{deviceSerialNumber} ,'%')
                </if>
                <if test="deviceName != null and deviceName != ''">
                    and t.device_name  like concat('%', #{deviceName} ,'%')
                </if>
                <if test="fixedAssetCoding != null and fixedAssetCoding != ''">
                    and t.fixed_asset_coding  like concat('%', #{fixedAssetCoding} ,'%')
                </if>
                <if test="durationOfTheSameProcess != null and durationOfTheSameProcess != ''">
                    and t.duration_of_the_same_process  like concat('%', #{durationOfTheSameProcess} ,'%')
                </if>
                <if test="equipmentEndTime != null">
                    and t.equipment_end_time  = #{equipmentEndTime}
                </if>
                <if test="equipmentEndTime27 != null">
                    and t.equipment_end_time27  = #{equipmentEndTime27}
                </if>
                <if test="equipmentRunningTimeh != null and equipmentRunningTimeh != ''">
                    and t.equipment_running_timeh  = #{equipmentRunningTimeh}
                </if>
                <if test="equipmentEnergyConsumption != null and equipmentEnergyConsumption != ''">
                    and t.equipment_energy_consumption  = #{equipmentEnergyConsumption}
                </if>
                <if test="equipmentPowerConsumption != null and equipmentPowerConsumption != ''">
                    and t.equipment_power_consumption  = #{equipmentPowerConsumption}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectWorkReportTableListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.WorkReportTableVO">
		select
		<include refid="Base_Column_List" />
			from work_report_table t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>