package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 材料库DTO 实体类
* <AUTHOR>
* @date 2025-08-08
**/
@Data
@ApiModel("材料库DTO实体类")
public class MaterialsLibraryDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 物料代码
     **/
    @ApiModelProperty("物料代码")
    private String materialCode;
    
    /**
	 * 物料名称
     **/
    @ApiModelProperty("物料名称")
    private String materialName;
    
    /**
	 * 规格型号
     **/
    @ApiModelProperty("规格型号")
    private String specificationModel;
    
    /**
	 * 库存数量
     **/
    @ApiModelProperty("库存数量")
    private Long inventoryQuantity;
    
    /**
	 * 采购在订量
     **/
    @ApiModelProperty("采购在订量")
    private Long quantityOnOrderForPurchase;
    
    /**
	 * 可用库存量
     **/
    @ApiModelProperty("可用库存量")
    private Long availableStockQuantity;
    
    /**
	 * 基本单位名称
     **/
    @ApiModelProperty("基本单位名称")
    private String basicUnitName;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;

}