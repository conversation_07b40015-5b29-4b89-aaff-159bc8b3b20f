package com.huatek.frame.modules.business.service;

import com.huatek.frame.modules.business.domain.ProductDetails;
import com.huatek.frame.modules.business.service.dto.ProductDetailsDTO;
import com.huatek.frame.modules.business.domain.vo.ProductDetailsVO;
import com.huatek.frame.common.response.TorchResponse;
import java.util.List;


/**
* @description 产品明细Service
* <AUTHOR>
* @date 2025-08-20
**/
public interface ProductDetailsService {
    
    /**
	 * 分页查找查找 产品明细
	 * 
	 * @param dto 产品明细dto实体对象
	 * @return 
	 */
	TorchResponse<List<ProductDetailsVO>> findProductDetailsPage(ProductDetailsDTO dto);

    /**
	 * 添加 \修改 产品明细
	 * 
	 * @param productDetailsDto 产品明细dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(ProductDetailsDTO productDetailsDto);
	
	/**
	 * 通过id查找产品明细
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<ProductDetailsVO> findProductDetails(String id);
	
	/**
	 * 删除 产品明细
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 产品明细
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<ProductDetailsVO>> getOptionsList(String id);




    /**
     * 根据条件查询产品明细列表
     *
     * @param dto 产品明细信息
     * @return 产品明细集合信息
     */
    List<ProductDetailsVO> selectProductDetailsList(ProductDetailsDTO dto);

    /**
     * 导入产品明细数据
     *
     * @param productDetailsList 产品明细数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importProductDetails(List<ProductDetailsVO> productDetailsList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取产品明细数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectProductDetailsListByIds(List<String> ids);



}