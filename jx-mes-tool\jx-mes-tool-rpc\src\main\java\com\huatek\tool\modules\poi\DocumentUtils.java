package com.huatek.tool.modules.poi;

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.web.multipart.MultipartFile;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class DocumentUtils {

	public static MultipartFile convertToMultipartFile(XWPFDocument document, String fileName) throws IOException {
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		document.write(outputStream);
		byte[] content = outputStream.toByteArray();
		outputStream.close();

		return new CustomMultipartFile(content, fileName);
	}

	// 自定义MultipartFile实现
	private static class CustomMultipartFile implements MultipartFile {
		private final byte[] content;
		private final String name;

		public CustomMultipartFile(byte[] content, String name) {
			this.content = content;
			this.name = name;
		}

		@Override
		public String getName() {
			return "file";
		}

		@Override
		public String getOriginalFilename() {
			return name;
		}

		@Override
		public String getContentType() {
			return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
		}

		@Override
		public boolean isEmpty() {
			return content == null || content.length == 0;
		}

		@Override
		public long getSize() {
			return content.length;
		}

		@Override
		public byte[] getBytes() throws IOException {
			return content;
		}

		@Override
		public InputStream getInputStream() throws IOException {
			return new java.io.ByteArrayInputStream(content);
		}

		@Override
		public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
			try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
				fos.write(content);
			}
		}
	}
}

