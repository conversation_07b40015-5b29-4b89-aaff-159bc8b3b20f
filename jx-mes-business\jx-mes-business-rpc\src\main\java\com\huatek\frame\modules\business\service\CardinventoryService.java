package com.huatek.frame.modules.business.service;


import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.CardinventoryVO;
import com.huatek.frame.modules.business.service.dto.CardinventoryDTO;

import java.util.List;


/**
* @description 板卡存放Service
* <AUTHOR>
* @date 2025-08-04
**/
public interface CardinventoryService {
    
    /**
	 * 分页查找查找 板卡存放
	 * 
	 * @param dto 板卡存放dto实体对象
	 * @return 
	 */
	TorchResponse<List<CardinventoryVO>> findCardinventoryPage(CardinventoryDTO dto);

    /**
	 * 添加 \修改 板卡存放
	 * 
	 * @param cardinventoryDto 板卡存放dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(CardinventoryDTO cardinventoryDto);
	
	/**
	 * 通过id查找板卡存放
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<CardinventoryVO> findCardinventory(String id);
	
	/**
	 * 删除 板卡存放
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 板卡存放
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<CardinventoryVO>> getOptionsList(String id);




    /**
     * 根据条件查询板卡存放列表
     *
     * @param dto 板卡存放信息
     * @return 板卡存放集合信息
     */
    List<CardinventoryVO> selectCardinventoryList(CardinventoryDTO dto);

    /**
     * 导入板卡存放数据
     *
     * @param cardinventoryList 板卡存放数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importCardinventory(List<CardinventoryVO> cardinventoryList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取板卡存放数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectCardinventoryListByIds(List<String> ids);


}