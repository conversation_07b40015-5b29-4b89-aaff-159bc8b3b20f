package com.huatek.frame.modules.business.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * 新增/修改监制验收订单请求实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SawOrderAddOrUpdateDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private String orderId;

    /**
     * 产品列表id
     */
    @ApiModelProperty("产品列表id")
    private String productListId;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;

    /**
     * 验收类型
     **/
    @ApiModelProperty("验收类型")
    private String acceptanceType;

    /**
     * 负责人
     **/
    @ApiModelProperty("负责人")
    private String responsiblePerson;

    /**
     * 状态
     **/
    @ApiModelProperty("状态")
    private String status;

    /**
     * 完成日期
     **/
    @ApiModelProperty("完成日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completionDate;

    /**
     * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;

    /**
     * 上传附件
     **/
    @ApiModelProperty("上传附件")
    private String attachment;


    /**
     * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
     * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;

    /**
     * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;

    /**
     * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;

    /**
     * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;

}
