package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 产品明细VO实体类
* <AUTHOR>
* @date 2025-08-20
**/
@Data
@ApiModel("产品明细DTO实体类")
public class ProductDetailsVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workOrderNumber;

    /**
	 * 规格型号
     **/
    @ApiModelProperty("规格型号")
    @Excel(name = "规格型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String specificationModel;

    /**
	 * 品名
     **/
    @ApiModelProperty("品名")
    @Excel(name = "品名",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String nameOfProduct;

    /**
	 * 质量等级
     **/
    @ApiModelProperty("质量等级")
    @Excel(name = "质量等级",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String qualityGrade;

    /**
	 * 生产批次
     **/
    @ApiModelProperty("生产批次")
    @Excel(name = "生产批次",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productionBatch;

    /**
	 * 质保依据
     **/
    @ApiModelProperty("质保依据")
    @Excel(name = "质保依据",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String qualityAssuranceBasis;

    /**
	 * 合同数量(只)
     **/
    @ApiModelProperty("合同数量(只)")
    @Excel(name = "合同数量(只)",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long quantityOfContractUnits;

    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String status;

    /**
	 * 完成日期
     **/
    @ApiModelProperty("完成日期")
    @Excel(name = "完成日期",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completionDate;

    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String comment;

    /**
	 * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}