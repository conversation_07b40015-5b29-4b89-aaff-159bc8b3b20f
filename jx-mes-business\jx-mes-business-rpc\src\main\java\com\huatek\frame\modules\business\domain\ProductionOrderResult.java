package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
* @description 工单检验结论
* <AUTHOR>
* @date 2025-07-30
**/
@Setter
@Getter
@TableName("production_order_result")
public class ProductionOrderResult implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 工单
     **/
    @TableField(value = "work_order"
    )
    private String workOrder;

    
    /**
	 * 检验结论
     **/
    @TableField(value = "inspection_conclusion"
    )
    private String inspectionConclusion;

    /**
     * 其他说明备注
     **/
    @TableField(value = "other_explanation_remark"
    )
    private String othereExplanationRemark;

    /**
     * 不合格数量
     **/
    @TableField(value = "unqualified_quantity"
    )
    private Integer unqualifiedQuantity;
    /**
	 * 检验结果
     **/
    @TableField(value = "inspection_result"
    )
    private String inspectionResult;

    
    /**
	 * 检验形式
     **/
    @TableField(value = "inspection_from"
    )
    private String inspectionFrom;

    
    /**
	 * 检验规范
     **/
    @TableField(value = "test_specifications"
    )
    private String testSpecifications;

    

    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    

    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}