<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ece3a5c4-1e66-4c67-89e5-01661dba50b2" name="Changes" comment="fix:bug 生产任务数据权限">
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/mapper/SysGroupMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/mapper/SysGroupMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/service/impl/SysGroupServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/service/impl/SysGroupServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/service/mapping/SysGroupMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/service/mapping/SysGroupMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-rpc/src/main/java/com/huatek/frame/modules/system/service/SysGroupService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-rpc/src/main/java/com/huatek/frame/modules/system/service/SysGroupService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/mapping/ProductionTaskMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/mapping/ProductionTaskMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/service/dto/ProductionTaskDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/service/dto/ProductionTaskDTO.java" afterDir="false" />
    </list>
    <list id="640866c8-be23-4972-8da4-c676321340e3" name="ignore-on-commit" comment="">
      <change afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target" afterDir="true" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/jarRepositories.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-admin/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-admin/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/core/model/JobInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/core/model/JobInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/service/XxlJobForeignService.class" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/service/XxlJobForeignService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/torch-job-rpc-2.4.0.jar" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/torch-job-rpc-2.4.0.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-basic-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-basic-application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-gateway-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-gateway-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/torch-basic-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/torch-basic-application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/conf/RedissonConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/conf/RedissonConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-gateway/doc/torch-gateway-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-gateway/doc/torch-gateway-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-gateway/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-gateway/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-openapi/target/jx-mes-openapi-1.0.0.jar" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-openapi/target/jx-mes-openapi-1.0.0.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-tool/doc/compass-tool-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-tool/doc/compass-tool-application-dev.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../../../../../../../../tools/mavenRep/com/huatek/torch/huatek-torch-common-core/2.1.9/huatek-torch-common-core-2.1.9.jar!/com/huatek/frame/common/context/SecurityContextHolder.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../../../../../../../tools/mavenRep/com/huatek/torch/huatek-torch-common-core/2.1.9/huatek-torch-common-core-2.1.9.jar!/com/huatek/frame/common/utils/SecurityUser.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="C:\tools\mavenRep" />
        <option name="userSettingsFile" value="C:\tools\apache-maven-3.8.4-bin\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zJXIm52poQbdHVZYIVkXliym0G" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.huatek-torch-job [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.huatek-torch-job [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes [validate].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes-business-rpc [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes-business-rpc [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;1.8&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Work/C3S/compass/svn/JunXin/JunXin2025Code/trunk/02Engineering/04Implementation/jx-mes-code/jx-mes-business/jx-mes-business-application/src/main/resources&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.4&quot;,
    &quot;service.view.auto.scroll.to.source&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;editor.preferences.tabs&quot;,
    &quot;应用程序.AwaitingProductionOrderServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.BasicApplication.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.BusinessApplication.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.GatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.JobAdminApplication.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.JobExecutorApplication.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.OutputValueReportServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.ProductionValueExcelExport.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\resources" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\resources\template" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\resources" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business\rest" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business\service\mapping" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\resources\image" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business" />
    </key>
    <key name="ExtractSuperBase.RECENT_KEYS">
      <recent name="com.huatek.frame.modules.system.service.impl" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.huatek.frame.modules.business.domain.vo" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.huatek.frame.modules.business.domain.vo" />
      <recent name="com.huatek.frame.modules.business.service.dto" />
      <recent name="com.huatek.frame.modules.business.conf" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="应用程序.AwaitingProductionOrderServiceImpl">
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AwaitingProductionOrderServiceImpl" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.modules.business.service.impl.AwaitingProductionOrderServiceImpl" />
      <module name="jx-mes-business-application" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.modules.business.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BasicApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.BasicApplication" />
      <module name="jx-mes-basic-application" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BusinessApplication" type="Application" factoryName="Application">
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.BusinessApplication" />
      <module name="jx-mes-business-application" />
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.GatewayApplication" />
      <module name="jx-mes-gateway" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobAdminApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.job.admin.JobAdminApplication" />
      <module name="torch-job-admin" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.job.admin.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobExecutorApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.job.executor.JobExecutorApplication" />
      <module name="torch-job-executor" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.job.executor.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="应用程序.BusinessApplication" />
      <item itemvalue="应用程序.AwaitingProductionOrderServiceImpl" />
      <item itemvalue="应用程序.GatewayApplication" />
      <item itemvalue="应用程序.BasicApplication" />
      <item itemvalue="应用程序.JobAdminApplication" />
      <item itemvalue="应用程序.JobExecutorApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.BasicApplication" />
        <item itemvalue="应用程序.JobExecutorApplication" />
        <item itemvalue="应用程序.JobAdminApplication" />
        <item itemvalue="应用程序.GatewayApplication" />
        <item itemvalue="应用程序.AwaitingProductionOrderServiceImpl" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
          <option name="myCopyRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
          <option name="myCopyRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ece3a5c4-1e66-4c67-89e5-01661dba50b2" name="Changes" comment="" />
      <created>1751450073556</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751450073556</updated>
    </task>
    <task id="LOCAL-00043" summary="产值报表定时任务">
      <option name="closed" value="true" />
      <created>1756372901422</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1756372901422</updated>
    </task>
    <task id="LOCAL-00044" summary="遗留赋值问题">
      <option name="closed" value="true" />
      <created>1756374207550</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1756374207550</updated>
    </task>
    <task id="LOCAL-00045" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756446945445</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1756446945445</updated>
    </task>
    <task id="LOCAL-00046" summary="更新ProductList和ProductManagement的产品分类">
      <option name="closed" value="true" />
      <created>1756447446147</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1756447446147</updated>
    </task>
    <task id="LOCAL-00047" summary="生产任务产品资料 product_information_management || standard_specification">
      <option name="closed" value="true" />
      <created>1756451081635</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1756451081635</updated>
    </task>
    <task id="LOCAL-00048" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756451804266</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1756451804266</updated>
    </task>
    <task id="LOCAL-00049" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756727955970</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1756727955970</updated>
    </task>
    <task id="LOCAL-00050" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756796152955</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1756796152956</updated>
    </task>
    <task id="LOCAL-00051" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756799740333</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1756799740333</updated>
    </task>
    <task id="LOCAL-00052" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756877131323</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1756877131323</updated>
    </task>
    <task id="LOCAL-00053" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756890803114</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1756890803114</updated>
    </task>
    <task id="LOCAL-00054" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756973344622</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1756973344622</updated>
    </task>
    <task id="LOCAL-00055" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756975023424</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1756975023424</updated>
    </task>
    <task id="LOCAL-00056" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756987881530</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1756987881530</updated>
    </task>
    <task id="LOCAL-00057" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757039341093</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1757039341093</updated>
    </task>
    <task id="LOCAL-00058" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757050842249</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1757050842249</updated>
    </task>
    <task id="LOCAL-00059" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757055047182</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1757055047182</updated>
    </task>
    <task id="LOCAL-00060" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757055122604</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1757055122604</updated>
    </task>
    <task id="LOCAL-00061" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757060030654</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1757060030655</updated>
    </task>
    <task id="LOCAL-00062" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757063056374</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1757063056374</updated>
    </task>
    <task id="LOCAL-00063" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757063702089</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1757063702089</updated>
    </task>
    <task id="LOCAL-00064" summary="设置产品资料">
      <option name="closed" value="true" />
      <created>1757065420140</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1757065420140</updated>
    </task>
    <task id="LOCAL-00065" summary="设置产品资料">
      <option name="closed" value="true" />
      <created>1757065843142</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1757065843142</updated>
    </task>
    <task id="LOCAL-00066" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757299111895</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1757299111895</updated>
    </task>
    <task id="LOCAL-00067" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757299595531</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1757299595531</updated>
    </task>
    <task id="LOCAL-00068" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757300472005</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1757300472005</updated>
    </task>
    <task id="LOCAL-00069" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757311492798</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1757311492798</updated>
    </task>
    <task id="LOCAL-00070" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757312367397</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1757312367397</updated>
    </task>
    <task id="LOCAL-00071" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757312778584</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1757312778584</updated>
    </task>
    <task id="LOCAL-00072" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757313396671</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1757313396671</updated>
    </task>
    <task id="LOCAL-00073" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757315293589</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1757315293590</updated>
    </task>
    <task id="LOCAL-00074" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757319209218</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1757319209218</updated>
    </task>
    <task id="LOCAL-00075" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757321297000</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1757321297000</updated>
    </task>
    <task id="LOCAL-00076" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757321959187</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1757321959187</updated>
    </task>
    <task id="LOCAL-00077" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757324293958</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1757324293958</updated>
    </task>
    <task id="LOCAL-00078" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757324603223</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1757324603223</updated>
    </task>
    <task id="LOCAL-00079" summary="fix:bug 依据下发的订单数据中“产品型号+生产厂家”两个字段， 在产品资料库中进行匹配：">
      <option name="closed" value="true" />
      <created>1757385848563</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1757385848563</updated>
    </task>
    <task id="LOCAL-00080" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757388464442</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1757388464443</updated>
    </task>
    <task id="LOCAL-00081" summary="fix:bug 能力资产导入">
      <option name="closed" value="true" />
      <created>1757401326240</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1757401326241</updated>
    </task>
    <task id="LOCAL-00082" summary="fix:bug  7030">
      <option name="closed" value="true" />
      <created>1757411365132</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1757411365132</updated>
    </task>
    <task id="LOCAL-00083" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757412103783</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1757412103784</updated>
    </task>
    <task id="LOCAL-00084" summary="fix:bug 7062">
      <option name="closed" value="true" />
      <created>1757417207555</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1757417207555</updated>
    </task>
    <task id="LOCAL-00085" summary="fix:bug 未开始状态暂停后恢复，状态未恢复为未开始，而是进行中">
      <option name="closed" value="true" />
      <created>1757418148277</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1757418148277</updated>
    </task>
    <task id="LOCAL-00086" summary="fix:bug 7015">
      <option name="closed" value="true" />
      <created>1757419066778</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1757419066778</updated>
    </task>
    <task id="LOCAL-00087" summary="fix:bug 提交的时候完成时间不能为空">
      <option name="closed" value="true" />
      <created>1757419325653</created>
      <option name="number" value="00087" />
      <option name="presentableId" value="LOCAL-00087" />
      <option name="project" value="LOCAL" />
      <updated>1757419325653</updated>
    </task>
    <task id="LOCAL-00088" summary="fix:bug 增加前置工单工序的执行顺序">
      <option name="closed" value="true" />
      <created>1757421147115</created>
      <option name="number" value="00088" />
      <option name="presentableId" value="LOCAL-00088" />
      <option name="project" value="LOCAL" />
      <updated>1757421147115</updated>
    </task>
    <task id="LOCAL-00089" summary="fix:bug 增加前置工单工序的执行顺序">
      <option name="closed" value="true" />
      <created>1757421232024</created>
      <option name="number" value="00089" />
      <option name="presentableId" value="LOCAL-00089" />
      <option name="project" value="LOCAL" />
      <updated>1757421232024</updated>
    </task>
    <task id="LOCAL-00090" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1757470874829</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1757470874829</updated>
    </task>
    <task id="LOCAL-00091" summary="fix:bug 生产任务数据权限">
      <option name="closed" value="true" />
      <created>1757488794320</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1757488794321</updated>
    </task>
    <option name="localTasksCounter" value="92" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="异常反馈增加工单编号" />
    <MESSAGE value="修改bug" />
    <MESSAGE value="增加对账日期" />
    <MESSAGE value="核算" />
    <MESSAGE value="发消息" />
    <MESSAGE value="生成核算数据" />
    <MESSAGE value="获取技术能力 去掉注释" />
    <MESSAGE value="自适应表头" />
    <MESSAGE value="产值报表定时任务" />
    <MESSAGE value="遗留赋值问题" />
    <MESSAGE value="更新ProductList和ProductManagement的产品分类" />
    <MESSAGE value="生产任务产品资料 product_information_management || standard_specification" />
    <MESSAGE value="设置产品资料" />
    <MESSAGE value="fix:bug 能力评审权限" />
    <MESSAGE value="fix:bug 依据下发的订单数据中“产品型号+生产厂家”两个字段， 在产品资料库中进行匹配：" />
    <MESSAGE value="fix:bug 能力资产导入" />
    <MESSAGE value="fix:bug  生产任务中第一个工序完成后，不需要更新生产工单状态为完成。当前工单的所有任务都完成时才更新生产工单的状态为完成。" />
    <MESSAGE value="fix:bug  7030" />
    <MESSAGE value="fix:bug 7062" />
    <MESSAGE value="fix:bug 未开始状态暂停后恢复，状态未恢复为未开始，而是进行中" />
    <MESSAGE value="fix:bug 7015" />
    <MESSAGE value="fix:bug 提交的时候完成时间不能为空" />
    <MESSAGE value="fix:bug 增加前置工单工序的执行顺序" />
    <MESSAGE value="fix:bug" />
    <MESSAGE value="fix:bug 生产任务数据权限" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:bug 生产任务数据权限" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/huatek-torch-job/torch-job-core/src/main/java/com/huatek/job/core/executor/XxlJobExecutor.java</url>
          <line>67</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/java/com/huatek/job/executor/service/jobhandler/SampleXxlJob.java</url>
          <line>52</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/common/utils/ProductionValueExcelExport.java</url>
          <line>104</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/AwaitingProductionOrderServiceImpl.java</url>
          <line>2513</line>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/service/impl/SysUserServiceImpl.java</url>
          <line>606</line>
          <option name="timeStamp" value="94" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductListServiceImpl.java</url>
          <line>840</line>
          <properties>
            <option name="lambda-ordinal" value="1" />
          </properties>
          <option name="timeStamp" value="97" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>1642</line>
          <option name="timeStamp" value="104" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>1657</line>
          <option name="timeStamp" value="105" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>302</line>
          <option name="timeStamp" value="106" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>1376</line>
          <option name="timeStamp" value="109" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="QueryWrapper wrapper = new QueryWrapper();&#10;&#9;&#9;&#9;wrapper.eq(&quot;work_order&quot;,productionOrderDTO.getId());&#10;&#9;&#9;&#9;CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(wrapper);&#10;&#9;&#9;&#9;CustomerExperimentProjectDTO customerExperimentProjectDTO = new CustomerExperimentProjectDTO();&#10;&#9;&#9;&#9;customerExperimentProjectDTO.setCodexTorchMasterFormId(scheme.getId());&#10;&#9;&#9;&#9;wrapper.eq(&quot;CODEX_TORCH_MASTER_FORM_ID&quot;,scheme.getId());&#10;&#9;&#9;&#9;List&lt;CustomerExperimentProjectVO&gt; customerEperimentProjects= customerExperimentProjectMapper.selectCustomerExperimentProjectList(customerExperimentProjectDTO);&#10;&#9;&#9;&#9;" />
        <watch expression="该记录已加入能力资产，不能进行完成操作" />
        <watch expression="   /**&#10;//&#9; * 能力编号&#10;//     **/&#10;//    @ApiModelProperty(&quot;能力编号&quot;)&#10;//    @Excel(name = &quot;能力编号&quot;,&#10;//        cellType = Excel.ColumnType.STRING,&#10;//        type = Excel.Type.ALL)&#10;//    private String capabilityNumber;" />
      </configuration>
    </watches-manager>
  </component>
</project>