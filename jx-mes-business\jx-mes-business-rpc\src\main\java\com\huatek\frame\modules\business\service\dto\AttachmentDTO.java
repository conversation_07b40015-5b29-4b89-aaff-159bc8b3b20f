package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.business.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 附件表DTO
* <AUTHOR>
* @date 2025-08-18
**/
@Setter
@Getter
@ApiModel(value = "设备原始数据DTO", description = "设备原始数据DTO")
public class AttachmentDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 工单编码
     **/
    @ApiModelProperty("工单编码")
    private String workOrderCode;
    
    /**
	 * 工序编码
     **/
    @ApiModelProperty("工序编码")
    private String processCode;
    
    /**
	 * 原始文件名称
     **/
    @ApiModelProperty("原始文件名称")
    private String originalFileName;
    
    /**
	 * 服务器文件名称
     **/
    @ApiModelProperty("服务器文件名称")
    private String serverFileName;
    
    /**
	 * 文件存储路径
     **/
    @ApiModelProperty("文件存储路径")
    private String filePath;
    
    /**
	 * 文件大小(字节)
     **/
    @ApiModelProperty("文件大小(字节)")
    private Long fileSize;
    
    /**
	 * 文件类型
     **/
    @ApiModelProperty("文件类型")
    private String fileType;
    
    /**
	 * 文件后缀
     **/
    @ApiModelProperty("文件后缀")
    private String suffix;
    
    /**
	 * 文件描述
     **/
    @ApiModelProperty("文件描述")
    private String description;
    
    /**
	 * 业务类型
     **/
    @ApiModelProperty("业务类型")
    private String businessType;
    
    /**
	 * 上传时间
     **/
    @ApiModelProperty("上传时间")
    private Timestamp uploadTime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp updateTime;

    /**
     * 页码
     */
    @ApiModelProperty("当前页码")
    private Integer page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty("每页显示大小")
    private Integer limit;
}
