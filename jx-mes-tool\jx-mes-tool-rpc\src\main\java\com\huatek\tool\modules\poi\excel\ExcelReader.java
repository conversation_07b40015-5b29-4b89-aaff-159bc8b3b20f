package com.huatek.tool.modules.poi.excel;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.alibaba.fastjson.JSONObject;

public class ExcelReader {

	public static void main(String[] args) {
        String path = "D:/file/excel/器件属性-AI测试用.xlsx";
        List<List<TableCell>> datas = readExcel(path, 4);
        System.out.println(JSONObject.toJSONString(datas));
	}
	
	/**
	 * 读取excel
	 * @param filePath     文件路径
	 * @param maxCellNum   数据有效列数
	 * @return
	 */
	public static JSONObject readExcelJSON(String filePath, int maxCellNum) {
		List<List<TableCell>> ds = readExcel(new File(filePath), maxCellNum);
		return JSONObject.parseObject(JSONObject.toJSONString(ds));
	}
	/**
	 * 读取excel
	 * @param filePath     文件路径
	 * @param maxCellNum   数据有效列数
	 * @return
	 */
	public static List<List<TableCell>> readExcel(String filePath, int maxCellNum) {
		return readExcel(new File(filePath), maxCellNum);
	}
	
	public static List<List<TableCell>> readExcel(File file, int maxCellNum) {
		try {
			FileInputStream fis = new FileInputStream(file);
			String fname = file.getName();
			String extString = fname.substring(fname.lastIndexOf("."));
			return readExcel(fis, extString, maxCellNum);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 读取excel
	 * @param is          excel文件流
	 * @param extString   文件后缀
	 * @param maxCellNum  数据有效列数
	 * @return
	 */
	public static List<List<TableCell>> readExcel(InputStream is, String extString, int maxCellNum) {
		Workbook wb = readWorkbook(is, extString);
		Sheet sheet = wb.getSheetAt(0);
		int rownum = sheet.getPhysicalNumberOfRows();
		int maxRowNum = rownum;
		for(int r = rownum - 1; r >= 0; r--) {
			Row row = sheet.getRow(r);
			if(null != row) {
				maxRowNum = r;
				break;
			}
		}
		return readExcel(sheet, maxRowNum, maxCellNum);
	}
	
	private static List<List<TableCell>> readExcel(Sheet sheet, int maxRowNum, int maxCellNum) {
		List<List<TableCell>> res = new ArrayList<List<TableCell>>();
		for(int r = 0; r <= maxRowNum; r++) {
			Row row = sheet.getRow(r);
			if(null == row) continue;//空白行跳过不处理
			List<TableCell> cells = new ArrayList<TableCell>();
			int colnum = row.getLastCellNum();
			for(int c = 0; c < colnum; c++) {
				if(c >= maxCellNum) continue;
				TableCell cell = new TableCell();
				String value = getCellFormatValue(row.getCell(c));
//				if(StringUtils.isBlank(value)) continue;
				String s = isMergedRow(sheet, r, c);
				if("0".equals(s)) {
					cell.setValue(value);
				} else if(!"1".equals(s)) {
					String[] ss = s.split("-");
					cell.setValue(value);
					cell.setCellSpan(Integer.parseInt(ss[0]));
					cell.setRowSpan(Integer.parseInt(ss[1]));
				}
				cells.add(cell);
			}
			res.add(cells);
		}
		return res;
	}

	/**
	 * 读取excel
	 * @param filePath
	 * @return
	 */
	private static Workbook readWorkbook(String filePath) {
		return readWorkbook(new File(filePath));
	}
	
	private static Workbook readWorkbook(File file) {
		try {
			FileInputStream fis = new FileInputStream(file);
			String fname = file.getName();
			String extString = fname.substring(fname.lastIndexOf("."));
			return readWorkbook(fis, extString);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		}
		return null;
	}
	private static Workbook readWorkbook(InputStream is, String extString) {
		Workbook wb = null;
		try {
			if (".xls".equals(extString)) {
				wb = new HSSFWorkbook(is);
			} else if (".xlsx".equals(extString)) {
				wb = new XSSFWorkbook(is);
			} else {
				wb = null;
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return wb;
	}
	
	/**
	 * 读取单元格内容
	 * @param cell
	 * @return
	 */
	private static String getCellFormatValue(Cell cell) {
		String cellValue = null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if (cell != null) {
			// 判断cell类型
			switch (cell.getCellType()) {
			case NUMERIC: {
				DecimalFormat df = new DecimalFormat("0");
				cellValue = df.format(cell.getNumericCellValue());
				String temp = cellValue.toString();
				if(temp.endsWith("0")) {
					cellValue = temp.replaceAll("\\.0+$", "");
				} 
				break;
			}
			case FORMULA: {
				// 判断cell是否为日期格式
				if (DateUtil.isCellDateFormatted(cell)) {
					cellValue = sdf.format(cell.getDateCellValue());
				} else {
					// 数字
					cellValue = String.valueOf(cell.getNumericCellValue());
				}
				break;
			}
			case STRING: {
				cellValue = cell.getRichStringCellValue().getString();
				break;
			}
			default:
				cellValue = "";
			}
		} else {
			cellValue = "";
		}
		return cellValue;
	}
	
	/**
	 * 解析合并单元格
	 * @param sheet
	 * @param row
	 * @param column
	 * @return 0:普通单元格、1:被合并单元格、c-r:合并行的列数-行数
	 */
	private static String isMergedRow(Sheet sheet, int row, int column) {
		int sheetMergeCount = sheet.getNumMergedRegions();
		for(int i = 0; i < sheetMergeCount; i++) {
			CellRangeAddress range = sheet.getMergedRegion(i);
			int firstColumn = range.getFirstColumn();
			int lastColumn = range.getLastColumn();
			int firstRow = range.getFirstRow();
			int lastRow = range.getLastRow();
			if(firstColumn == column && firstRow == row) {
				return (lastColumn - firstColumn + 1) + "-" + (lastRow - firstRow + 1);
			} else if(firstColumn < column && column <= lastColumn && firstRow < row && row <= lastRow) {
				return "1";
            } else if(firstColumn == column && firstRow < row && row <= lastRow) {
            	return "1";
            } else if(firstColumn < column && column <= lastColumn && firstRow == row) {
            	return "1";
            }
		}
		return "0";//普通单元格
	}
}
