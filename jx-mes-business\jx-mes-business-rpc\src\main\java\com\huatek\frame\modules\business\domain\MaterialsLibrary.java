package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 材料库
* <AUTHOR>
* @date 2025-08-08
**/
@Setter
@Getter
@TableName("materials_library")
public class MaterialsLibrary implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 物料代码
     **/
    @TableField(value = "material_code"
    )
    private String materialCode;

    
    /**
	 * 物料名称
     **/
    @TableField(value = "material_name"
    )
    private String materialName;

    
    /**
	 * 规格型号
     **/
    @TableField(value = "specification_model"
    )
    private String specificationModel;

    
    /**
	 * 库存数量
     **/
    @TableField(value = "inventory_quantity"
    )
    private Long inventoryQuantity;

    
    /**
	 * 采购在订量
     **/
    @TableField(value = "quantity_on_order_for_purchase"
    )
    private Long quantityOnOrderForPurchase;

    
    /**
	 * 可用库存量
     **/
    @TableField(value = "available_stock_quantity"
    )
    private Long availableStockQuantity;

    
    /**
	 * 基本单位名称
     **/
    @TableField(value = "basic_unit_name"
    )
    private String basicUnitName;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}