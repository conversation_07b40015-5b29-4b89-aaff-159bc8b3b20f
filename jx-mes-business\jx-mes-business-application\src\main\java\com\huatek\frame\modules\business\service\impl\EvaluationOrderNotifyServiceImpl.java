package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.EvaluationOrderNotify;
import com.huatek.frame.modules.business.domain.OtherContacts;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderNotifyVO;
import com.huatek.frame.modules.business.domain.vo.OtherContactsVO;
import com.huatek.frame.modules.business.mapper.EvaluationOrderNotifyMapper;
import com.huatek.frame.modules.business.service.EvaluationOrderNotifyService;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderNotifyDTO;
import com.huatek.frame.modules.constant.DicConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

/**
 * 测评订单消息通知服务实现层
 */
@Service
@RequiredArgsConstructor
public class EvaluationOrderNotifyServiceImpl extends ServiceImpl<EvaluationOrderNotifyMapper, EvaluationOrderNotify> implements EvaluationOrderNotifyService {

    private final EvaluationOrderNotifyMapper evaluationOrderNotifyMapper;

    @Override
    public TorchResponse<List<EvaluationOrderNotifyVO>> findAll(EvaluationOrderNotifyDTO requestParam) {
        List<EvaluationOrderNotifyVO> evaluationOrderNotifyVOS = evaluationOrderNotifyMapper.findAll(requestParam);
        evaluationOrderNotifyVOS.forEach(item -> {
            String recipients = item.getRecipients();
            String[] recipientsList = recipients == null ? new String[0] : recipients.split(",");
            item.setRecipientsList(recipientsList);
        });
        TorchResponse response = new TorchResponse();
        response.getData().setData(evaluationOrderNotifyVOS);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse saveOrUpdateNotify(List<EvaluationOrderNotifyDTO> requestParam) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        requestParam.forEach(item -> {
            if (HuatekTools.isEmpty(item.getDeleted())) {
                item.setDeleted(Constant.DEFAULT_NO);
            }
            String recipients = String.join(",", item.getRecipientsList());
            String id = item.getId();
            EvaluationOrderNotify entity = new EvaluationOrderNotify();
            BeanUtils.copyProperties(requestParam, entity);
            entity.setCreateTime(new Timestamp(System.currentTimeMillis()));
            entity.setRecipients(recipients);
            entity.setCreator(currentUser);
            if (HuatekTools.isEmpty(id)) {
                evaluationOrderNotifyMapper.insert(entity);
            } else {
                evaluationOrderNotifyMapper.updateById(entity);
            }
        });
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;

    }
}
