package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.modules.system.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* @description 标识卡
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("标识卡 VO实体类")
public class ProductionOrderIDCardVO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;


    //生产工单编号
    @ApiModelProperty("编号")
    private String workOrderNumber;

    //订单基础信息的委托单位名称
    @ApiModelProperty("送筛单位")
    private String entrustedUnit;


    //产品型号
    @ApiModelProperty("器件型号")
    private String productModel;

    @ApiModelProperty("生产批次")
    private String productionBatch;

    //最后一个工序的合格数
    @ApiModelProperty("合格数量")
    private Integer qualifiedQuantity;

    //production_order_result中的unqualified_quantity
    @ApiModelProperty("失效数量")
    private Integer failureQuantity;


}