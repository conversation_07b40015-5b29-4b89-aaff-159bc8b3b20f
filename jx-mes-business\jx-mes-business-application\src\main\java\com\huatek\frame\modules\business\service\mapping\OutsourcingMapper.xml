<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.OutsourcingMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.order_id as orderId,
        t.process_id as processId,
        t.outsourcing_number as outsourcingNumber,
<!--		t.outsourcing_department as outsourcingDepartment,-->
		t.outsourcing_process as outsourcingProcess,
		t.quantity as quantity,
		t.outsourcing_reason as outsourcingReason,
		t.outsourcing_manufacturer as outsourcingManufacturer,
		t.estimated_price as estimatedPrice,
		t.expected_end_time as expectedEndTime,
		t.actual_price as actualPrice,
		t.status as status,
		t.entire_or_process as entireOrProcess,
        t.comment as comment,
        t.attachment as attachment,
		t.application_time as applicationTime,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_applicant as codexTorchApplicant,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>

    <sql id="Product_Order_Info">
        p.work_order_number as workOrderNumber,
        p.order_number as orderNumber,
        p.product as productId
    </sql>

    <sql id="Product_Info">
        pl.evaluation_order_id as evaluationOrderId,
        pl.standard_specification_id as standardSpecificationId,
        pl.product_model as productModel,
        pl.manufacturer as manufacturer,
<!--        pl.product_category as productCategory,-->
        pl.product_name as productName,
        pl.production_batch as productionBatch,
        pl.test_type as testType
    </sql>

    <select id="selectOutsourcingApplicationPage" parameterType="com.huatek.frame.modules.business.service.dto.OutsourcingPageDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
		select
		<include refid="Base_Column_List" />, <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>, ss.specification_number as specificationNumber, c.entrusted_unit as entrustedUnit, pt.test_basis as testBasis
			, pt.process_name2 as outsourcingProcess, g.group_name as outsourcingDepartment, pc.category_name as productCategory
        from outsourcing t
            LEFT JOIN production_order p ON t.order_id = p.id
            LEFT JOIN product_list pl ON p.product = pl.id
            LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
            LEFT JOIN standard_specification ss ON pl.standard_specification_id = ss.id
            LEFT JOIN customer_information_management c ON c.id = e.customer_id
            LEFT JOIN production_task pt ON pt.id = t.process_id
            LEFT JOIN sys_group g ON g.id = t.outsourcing_department
            LEFT JOIN product_category pc ON pc.id = pl.product_category
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and p.work_order_number like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pl.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="outsourcingNumber != null and outsourcingNumber != ''">
                    and t.outsourcing_number like concat ('%', #{outsourcingNumber} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and p.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and c.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="outsourcingDepartment != null and outsourcingDepartment != ''">
                    and t.outsourcing_department  like concat('%', #{outsourcingDepartment} ,'%')
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="testType != null and testType != ''">
                    and pl.test_type = #{testType}
                </if>
                <if test="applicationTime != null">
                    and t.application_time  = #{applicationTime}
                </if>
                <if test="productModel != null and productModel != ''">
                    and pl.product_model like concat ('%', #{productModel}, '%')
                </if>
            </where>
        ORDER BY t.CODEX_TORCH_CREATE_DATETIME DESC
	</select>

    <select id="selectOutsourcingApplicationList" parameterType="com.huatek.frame.modules.business.service.dto.OutsourcingDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
        select
        <include refid="Base_Column_List" />, <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>, ss.specification_number as specificationNumber, c.entrusted_unit as entrustedUnit, pt.test_basis as testBasis
        , pt.process_name2 as outsourcingProcess
        from outsourcing t
        LEFT JOIN production_order p ON t.order_id = p.id
        LEFT JOIN product_list pl ON p.product = pl.id
        LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
        LEFT JOIN standard_specification ss ON pl.standard_specification_id = ss.id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        LEFT JOIN production_task pt ON pt.id = t.process_id
        <where>
            and t.codex_torch_deleted = '0'
            <if test="workOrderNumber != null and workOrderNumber != ''">
                and p.work_order_number like concat('%', #{workOrderNumber} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pl.product_category  like concat('%', #{productCategory} ,'%')
            </if>
            <if test="outsourcingNumber != null and outsourcingNumber != ''">
                and t.outsourcing_number like concat ('%', #{outsourcingNumber} ,'%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and p.order_number  like concat('%', #{orderNumber} ,'%')
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and c.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="outsourcingDepartment != null and outsourcingDepartment != ''">
                and t.outsourcing_department  like concat('%', #{outsourcingDepartment} ,'%')
            </if>
            <if test="status != null and status != ''">
                and t.status  = #{status}
            </if>
            <if test="testType != null and testType != ''">
                and pl.test_type = #{testType}
            </if>
            <if test="applicationTime != null">
                and t.application_time  = #{applicationTime}
            </if>
            <if test="productModel != null and productModel != ''">
                and pl.product_model like concat ('%', #{productModel}, '%')
            </if>
        </where>
        ORDER BY t.CODEX_TORCH_CREATE_DATETIME DESC
	</select>

    <select id="selectOutsourcingApplicationListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
		select
		<include refid="Base_Column_List" />
			from outsourcing t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
        ORDER BY t.CODEX_TORCH_CREATE_DATETIME DESC
	</select>
    <select id="findPendingOutsourcings" resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
        select
        <include refid="Base_Column_List" />, <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>, ss.specification_number as specificationNumber, c.entrusted_unit as entrustedUnit, pt.test_basis as testBasis
        , pt.process_name2 as outsourcingProcess, g.group_name as outsourcingDepartment, pc.category_name as productCategory
        from outsourcing t
        LEFT JOIN production_order p ON t.order_id = p.id
        LEFT JOIN product_list pl ON p.product = pl.id
        LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
        LEFT JOIN standard_specification ss ON pl.standard_specification_id = ss.id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        LEFT JOIN production_task pt ON pt.id = t.process_id
        LEFT JOIN sys_group g ON g.id = t.outsourcing_department
        LEFT JOIN product_category pc ON pc.id = pl.product_category
        <where>
            and t.codex_torch_deleted = '0'
            and t.status = '1'
            <if test="workOrderNumber != null and workOrderNumber != ''">
                and p.work_order_number like concat('%', #{workOrderNumber} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pl.product_category  like concat('%', #{productCategory} ,'%')
            </if>
            <if test="outsourcingNumber != null and outsourcingNumber != ''">
                and t.outsourcing_number like concat ('%', #{outsourcingNumber} ,'%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and p.order_number  like concat('%', #{orderNumber} ,'%')
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and c.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="outsourcingDepartment != null and outsourcingDepartment != ''">
                and t.outsourcing_department  like concat('%', #{outsourcingDepartment} ,'%')
            </if>
            <if test="testType != null and testType != ''">
                and pl.test_type = #{testType}
            </if>
            <if test="applicationTime != null">
                and t.application_time  = #{applicationTime}
            </if>
            <if test="productModel != null and productModel != ''">
                and pl.product_model like concat ('%', #{productModel}, '%')
            </if>
        </where>
        ORDER BY t.CODEX_TORCH_CREATE_DATETIME DESC
    </select>
    <select id="findOutsourcingHistorys"
            resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
        select
        <include refid="Base_Column_List" />, <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>, ss.specification_number as specificationNumber, c.entrusted_unit as entrustedUnit, pt.test_basis as testBasis
        , pt.process_name2 as outsourcingProcess, g.group_name as outsourcingDepartment, pc.category_name as productCategory
        from outsourcing t
        LEFT JOIN production_order p ON t.order_id = p.id
        LEFT JOIN product_list pl ON p.product = pl.id
        LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
        LEFT JOIN standard_specification ss ON pl.standard_specification_id = ss.id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        LEFT JOIN production_task pt ON pt.id = t.process_id
        LEFT JOIN sys_group g ON g.id = t.outsourcing_department
        LEFT JOIN product_category pc ON pc.id = pl.product_category
        <where>
            and t.codex_torch_deleted = "0"
            and (t.status = "2" or t.status = "3" or t.status = "4")
            <if test="workOrderNumber != null and workOrderNumber != ''">
                and p.work_order_number like concat('%', #{workOrderNumber} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pl.product_category  like concat('%', #{productCategory} ,'%')
            </if>
            <if test="outsourcingNumber != null and outsourcingNumber != ''">
                and t.outsourcing_number like concat ('%', #{outsourcingNumber} ,'%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and p.order_number  like concat('%', #{orderNumber} ,'%')
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and c.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="outsourcingDepartment != null and outsourcingDepartment != ''">
                and t.outsourcing_department  like concat('%', #{outsourcingDepartment} ,'%')
            </if>
            <if test="testType != null and testType != ''">
                and pl.test_type = #{testType}
            </if>
            <if test="applicationTime != null">
                and t.application_time  = #{applicationTime}
            </if>
            <if test="productModel != null and productModel != ''">
                and pl.product_model like concat ('%', #{productModel}, '%')
            </if>
        </where>
        ORDER BY t.CODEX_TORCH_CREATE_DATETIME DESC
    </select>
    <select id="findOutsourcingDetails" resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
        select
        <include refid="Base_Column_List" />, <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>, ss.specification_number as specificationNumber,c.entrusted_unit as entrustedUnit
        from outsourcing t
        LEFT JOIN production_order p ON t.order_id = p.id
        LEFT JOIN product_list pl ON p.product = pl.id
        LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
        LEFT JOIN standard_specification ss ON pl.standard_specification_id = ss.id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        where t.id = #{id}
    </select>
</mapper>