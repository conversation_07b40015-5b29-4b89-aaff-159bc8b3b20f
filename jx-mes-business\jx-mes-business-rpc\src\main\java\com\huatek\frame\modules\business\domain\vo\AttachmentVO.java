package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 设备原始数据VO
* <AUTHOR>
* @date 2025-08-18
**/
@Setter
@Getter
@ApiModel(value = "设备原始数据VO", description = "设备原始数据VO")
public class AttachmentVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 工单编码
     **/
    @ApiModelProperty("工单编码")
    @Excel(name = "工单编码",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workOrderCode;

    /**
	 * 工序编码
     **/
    @ApiModelProperty("工序编码")
    @Excel(name = "工序编码",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String processCode;

    /**
	 * 原始文件名称
     **/
    @ApiModelProperty("原始文件名称")
    @Excel(name = "原始文件名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String originalFileName;

    /**
	 * 服务器文件名称
     **/
    @ApiModelProperty("服务器文件名称")
    @Excel(name = "服务器文件名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String serverFileName;

    /**
	 * 文件存储路径
     **/
    @ApiModelProperty("文件存储路径")
    @Excel(name = "文件存储路径",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String filePath;

    /**
	 * 文件大小(字节)
     **/
    @ApiModelProperty("文件大小(字节)")
    @Excel(name = "文件大小(字节)",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long fileSize;

    /**
	 * 文件类型
     **/
    @ApiModelProperty("文件类型")
    @Excel(name = "文件类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String fileType;

    /**
	 * 文件后缀
     **/
    @ApiModelProperty("文件后缀")
    @Excel(name = "文件后缀",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String suffix;

    /**
	 * 文件描述
     **/
    @ApiModelProperty("文件描述")
    @Excel(name = "文件描述",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String description;

    /**
	 * 业务类型
     **/
    @ApiModelProperty("业务类型")
    @Excel(name = "业务类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String businessType;

    /**
	 * 上传时间
     **/
    @ApiModelProperty("上传时间")
    @Excel(name = "上传时间",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private Timestamp uploadTime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @Excel(name = "更新时间",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private Timestamp updateTime;
}
