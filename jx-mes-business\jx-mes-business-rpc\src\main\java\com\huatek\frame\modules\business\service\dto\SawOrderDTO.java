package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 监制验收工单DTO 实体类
* <AUTHOR>
* @date 2025-08-20
**/
@Data
@ApiModel("监制验收工单DTO实体类")
public class SawOrderDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;
    
    /**
	 * 订单类型
     **/
    @ApiModelProperty("订单类型")
    private String orderType;
    
    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    private String entrustedUnit;
    
    /**
	 * 要求完成日期
     **/
    @ApiModelProperty("要求完成日期")
    private String deadline7;
    
    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    
    /**
	 * 客户经理
     **/
    @ApiModelProperty("客户经理")
    private String customerManager;
    
    /**
	 * 工程代码
     **/
    @ApiModelProperty("工程代码")
    private String engineeringCode;
    
    /**
	 * 紧急程度
     **/
    @ApiModelProperty("紧急程度")
    private String urgencyLevel;
    
    /**
	 * 验收通知单
     **/
    @ApiModelProperty("验收通知单")
    private String acceptanceNotice;
    
    /**
	 * 订单合同号
     **/
    @ApiModelProperty("订单合同号")
    private String orderContractNumber;
    
    /**
	 * 订单备注
     **/
    @ApiModelProperty("订单备注")
    private String orderRemarks;
    
    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;
    
    /**
	 * 验收类型
     **/
    @ApiModelProperty("验收类型")
    private String acceptanceType;
    
    /**
	 * 负责人
     **/
    @ApiModelProperty("负责人")
    private String responsiblePerson;
    
    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    private String status;
    
    /**
	 * 完成日期
     **/
    @ApiModelProperty("完成日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completionDate;
    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;
    
    /**
	 * 上传附件
     **/
    @ApiModelProperty("上传附件")
    private String uploadAttachment;
    
    /**
	 * 子表明细项ID
     **/
    @ApiModelProperty("子表明细项ID")
    private String codexTorchDetailItemIds;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


    /**
     * 子表明细项
     */
    @ApiModelProperty("子表明细项")
    private ProductDetailsDTO[] detailFormItems;

	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;


}