package com.huatek.frame.modules.business.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * 测评订单消息请求实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationOrderNotifyDTO {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;



    /**
     * 测评订单id
     */
    @ApiModelProperty("测评订单id")
    private String evaluationOrderId;

    /**
     * 产品列表id
     */
    @ApiModelProperty("产品列表id")
    private String productListId;

    /**
     * 变更通知消息
     */
    @ApiModelProperty("变更通知消息")
    private String message;

    /**
     * 接收人id
     */
    @ApiModelProperty("接收人id，逗号拼接")
    private String recipients;

    /**
     * 接收人id数组
     */
    @ApiModelProperty("接收人id数组")
    private String[] recipientsList;

    /**
     * 消息发送人
     */
    @ApiModelProperty("消息发送人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 删除标识 (0-未删除，1-已删除)
     */
    @ApiModelProperty("删除标识")
    private String deleted;

    /**
     * 请求页码
     */
    @ApiModelProperty("请求页码")
    private Integer page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty("每页显示数量")
    private Integer limit;
}
