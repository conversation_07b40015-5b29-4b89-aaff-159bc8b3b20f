package com.huatek.frame.modules.business.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.Map;

/**
 * 产值计算明细导出VO
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
public class ProductionValueCalculationDetailExportVO {

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderNumber;


    /**
     * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productName;

    /**
     * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productModel;


    /**
     * 批次号
     **/
    @ApiModelProperty("批次号")
    @Excel(name = "生产批次",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String batchNumber;


    /**
     * 产品数量
     **/
    @ApiModelProperty("产品数量")
    @Excel(name = "产品数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer productQuantity;


    /**
     * 结算单位
     **/
    @ApiModelProperty("结算单位")
    @Excel(name = "结算单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String settlementUnit;


    /**
     * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String entrustedUnit;


    /**
     * 合格数
     **/
    @ApiModelProperty("合格数")
    @Excel(name = "合格数",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer numberOfQualifiedProductions;

    /**
     * 不合格数
     **/
    @ApiModelProperty("不合格数")
    @Excel(name = "不合格数",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer numNonQualProd;

    /**
     * 不合格工序
     **/
    @ApiModelProperty("不合格工序")
    @Excel(name = "不合格工序",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String nonQualityProcess;
    /**
     * 收费标准名称
     **/
    @ApiModelProperty("收费标准名称")
    @Excel(name = "收费标准",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String chargingStandardName;


    /**
     * 客户核算价格
     **/
    @ApiModelProperty("客户核算价格")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "合计费用",
            cellType = Excel.ColumnType.NUMERIC,
            type = Excel.Type.ALL)
    private BigDecimal customerAccountingPrice;
    /**
     * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testType;

    /**
     * 委托日期
     **/
    @ApiModelProperty("委托日期")
    @Excel(name = "委托日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfEntrustment;


    /**
     * 实际完成时间
     */
    @ApiModelProperty("实际完成时间")
    @Excel(name = "完成日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp completionTime;

    /**
     * 订单送检编号
     **/
    @ApiModelProperty("订单送检编号")
    @Excel(name = "订单送检编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String orderInspectionNumber;

    /**
     * 工单送检编号
     **/
    @ApiModelProperty("工单送检编号")
    @Excel(name = "工单送检编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderInspectionNumber1;



    /**
     * 发货日期
     */
    @ApiModelProperty("发货时间")
    @Excel(name = "发货时间",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date shippingDate;

    /**
     * 快递接收人
     */
    @ApiModelProperty("快递接收人")
    @Excel(name = "快递接收人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String recipientCustomer;
    /**
     * 客户价格分类
     **/
    @ApiModelProperty("客户价格分类")
    @Excel(name = "价格分类",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String customerPriceClassification;

    /** 动态表头和值的映射 */
    private Map<String, BigDecimal> dynamicColumns;
}
