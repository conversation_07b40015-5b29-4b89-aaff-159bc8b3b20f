package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO;
import com.huatek.frame.modules.business.domain.vo.CapabilityReviewVO;
import com.huatek.frame.modules.business.domain.vo.CapabilityVerificationVO;
import com.huatek.frame.modules.business.domain.vo.ProductListVO;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.service.*;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.SysUser;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.service.SysGroupService;
import com.huatek.frame.modules.system.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.CollectionUtils;


/**
 * 能力评审 ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "capabilityReview")
//@RefreshScope
@Slf4j
public  class CapabilityReviewServiceImpl extends ServiceImpl<CapabilityReviewMapper, CapabilityReview> implements CapabilityReviewService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";


    @Autowired
    private CapabilityReviewMapper capabilityReviewMapper;

    @Autowired
    private CapabilityDevelopmentService capabilityDevelopmentService;

    @Autowired
    private CapabilityVerificationService capabilityVerificationService;

    @Autowired
    private EvaluationOrderReviewService evaluationOrderReviewService;
    @Autowired
    private ProductListMapper productListMapper;
    @Autowired
    private ProductManagementMapper productManagementMapper;

    @Autowired
    private AbnormalfeedbackService abnormalfeedbackService;

    @Autowired
    private EvaluationOrderReviewMapper evaluationOrderReviewMapper;

    @Autowired
    private MessageManagementService messageManagementService;
    @DubboReference
    private SysUserService sysUserService;

    @Autowired
    private CapabilityAssetMapper capabilityAssetMapper;

    @DubboReference
    private SysGroupService sysGroupService;


    @Autowired
    protected Validator validator;

    private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    public CapabilityReviewServiceImpl() {

    }

    @Override
    //@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
    public TorchResponse<List<CapabilityReviewVO>> findCapabilityReviewPage(CapabilityReviewDTO dto) {

        roleQuery(dto);

        TorchResponse<List<CapabilityReviewVO>> response = new TorchResponse<List<CapabilityReviewVO>>();
        response.setStatus(200);
        PageHelper.startPage(dto.getPage(), dto.getLimit());
        Page<CapabilityReviewVO> capabilityReviews = capabilityReviewMapper.selectCapabilityReviewPage(dto);
        response.getData().setData(capabilityReviews);
        response.getData().setCount(capabilityReviews.getTotal());
        return response;
    }

    private void roleQuery(CapabilityReviewDTO dto) {
        //SysUser sysUser = sysUserService.selectById(SecurityContextHolder.getCurrentUserId());
        List<String> roles = sysUserService.selectCurrentUserRoles(SecurityContextHolder.getCurrentUserId());
        if(!BusinessConstant.ADMIN.equals(SecurityContextHolder.getCurrentUserName())){
            if(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_ZIXUN.equals(dto.getType())){
                if(!roles.contains(BusinessConstant.ROLE_NLPS)){
                    throw new ServiceException("您没有权限查看咨询类能力评审");
                }
            }else{
                SysGroup sysGroup = sysGroupService.selectById(SecurityContextHolder.getCurrentUserGroupId());

                String codexTorchGroupId="";
                String[] groupIds= sysGroup.getAncestors().split(",");
                if(groupIds.length>2){
                    codexTorchGroupId=groupIds[2];
                }else{
                    codexTorchGroupId=SecurityContextHolder.getCurrentUserGroupId();
                }

                dto.setCodexTorchGroupId(codexTorchGroupId);
            }
        }
    }

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse batchSave(List<CapabilityReviewDTO> capabilityReviewDtoList) {

        List<CapabilityReview> entityList = new ArrayList<>();
        capabilityReviewDtoList.forEach(item -> {
            CapabilityReview entity = new CapabilityReview();
            BeanUtils.copyProperties(item, entity);
            entity.setId(HuatekTools.getUuid());
            entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
            entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
            entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
            entity.setCodexTorchDeleted(Constant.DEFAULT_NO);
            entityList.add(entity);
        });
        //批量插入
        capabilityReviewMapper.batchInsert(entityList);


        TorchResponse response = new TorchResponse();
        response.getData().setData(entityList);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }
    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveOrUpdate(CapabilityReviewDTO capabilityReviewDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(capabilityReviewDto.getCodexTorchDeleted())) {
            capabilityReviewDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        //咨询还是订单: type DicConstant.TechnicalManagement.CAPABILITY_REVIEW_DINGDAN


        String id = capabilityReviewDto.getId();
        CapabilityReview entity = new CapabilityReview();
        BeanUtils.copyProperties(capabilityReviewDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        if (HuatekTools.isEmpty(id)) {
            capabilityReviewMapper.insert(entity);
        } else {
            capabilityReviewMapper.updateById(entity);
        }

        TorchResponse response = new TorchResponse();
        CapabilityReviewVO vo = new CapabilityReviewVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    //@Cacheable(key = "#p0")
    public TorchResponse<CapabilityReviewVO> findCapabilityReview(String id) {
        CapabilityReviewVO vo = new CapabilityReviewVO();
        if (!HuatekTools.isEmpty(id)) {
            CapabilityReview entity = capabilityReviewMapper.selectById(id);
            if (HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
            }
            BeanUtils.copyProperties(entity, vo);
        }
        TorchResponse<CapabilityReviewVO> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(vo);
        return response;
    }

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse delete(String[] ids) {
        List<CapabilityReview> capabilityReviewList = capabilityReviewMapper.selectBatchIds(Arrays.asList(ids));
        for (CapabilityReview capabilityReview : capabilityReviewList) {
            capabilityReview.setCodexTorchDeleted(Constant.DEFAULT_YES);
            capabilityReviewMapper.updateById(capabilityReview);
        }
        //capabilityReviewMapper.deleteBatchIds(Arrays.asList(ids));
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    public TorchResponse getOptionsList(String id) {
        if (selectOptionsFuncMap.size() == 0) {
        }

        //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
        PageHelper.startPage(1, 1000);
        Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

        TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
        response.getData().setData(selectOptionsVOs);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(selectOptionsVOs.getTotal());
        return response;
    }


    @Override
    @ExcelExportConversion(tableName = "capability_review", convertorFields = "inspectionType,type,reviewResult,feedbackProcessing")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<CapabilityReviewVO> selectCapabilityReviewList(CapabilityReviewDTO dto) {

        roleQuery(dto);
        return capabilityReviewMapper.selectCapabilityReviewList(dto);
    }

    /**
     * 导入能力评审数据
     *
     * @param capabilityReviewList 能力评审数据列表
     * @param unionColumns         作为确认数据唯一性的字段集合
     * @param isUpdateSupport      是否更新支持，如果已存在，则进行更新数据
     * @param operName             操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "capability_review", convertorFields = "inspectionType,type,reviewResult,feedbackProcessing")
    public TorchResponse importCapabilityReview(List<CapabilityReviewVO> capabilityReviewList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(capabilityReviewList) || capabilityReviewList.size() == 0) {
            throw new ServiceException("导入能力评审数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CapabilityReviewVO vo : capabilityReviewList) {
            try {
                CapabilityReview capabilityReview = new CapabilityReview();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum++;
                    continue;
                }
                BeanUtils.copyProperties(vo, capabilityReview);
                QueryWrapper<CapabilityReview> wrapper = new QueryWrapper();
                CapabilityReview oldCapabilityReview = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn : unionColumns) {
                        try {
                            Field field = CapabilityReviewVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<CapabilityReview> oldCapabilityReviewList = capabilityReviewMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldCapabilityReviewList) && oldCapabilityReviewList.size() > 1) {
                        capabilityReviewMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldCapabilityReviewList) && oldCapabilityReviewList.size() == 1) {
                        oldCapabilityReview = oldCapabilityReviewList.get(0);
                    }
                }
                if (StringUtils.isNull(oldCapabilityReview)) {
                    BeanValidators.validateWithException(validator, vo);
                    capabilityReviewMapper.insert(capabilityReview);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、委托单位 " + vo.getEntrustedUnit() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldCapabilityReview, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    capabilityReviewMapper.updateById(oldCapabilityReview);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、委托单位 " + vo.getEntrustedUnit() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、委托单位 " + vo.getEntrustedUnit() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、委托单位 " + vo.getEntrustedUnit() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(CapabilityReviewVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (failureRecord > 0) {
            failureNum++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectCapabilityReviewListByIds(List<String> ids) {
        List<CapabilityReviewVO> capabilityReviewList = capabilityReviewMapper.selectCapabilityReviewListByIds(ids);

        TorchResponse<List<CapabilityReviewVO>> response = new TorchResponse<List<CapabilityReviewVO>>();
        response.getData().setData(capabilityReviewList);
        response.setStatus(200);
        response.getData().setCount((long) capabilityReviewList.size());
        return response;
    }

    /**
     * 能力反馈
     *
     * @param dto 能力反馈DTO实体对象
     * @return
     * @throws Exception
     */
    @Override
    @SuppressWarnings("rawtypes")
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse updateFeedback(CapabilityReviewFeedBackDTO dto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        if (CollectionUtils.isEmpty(dto.getIds())) {
            response.setMessage("请选择需要更新的记录");
            return response;
        }

        List<CapabilityReview> capabilityReviewList = capabilityReviewMapper.selectBatchIds(dto.getIds());
        if (capabilityReviewList.isEmpty()) {
            response.setMessage("未查询到记录");
            return response;
        }
        for (CapabilityReview capabilityReview : capabilityReviewList) {
            if (!DicConstant.TechnicalManagement.CAPABILITY_REVIEW_ZIXUN.equals(capabilityReview.getType()) || !HuatekTools.isEmpty(capabilityReview.getFeedbackProcessing())) {
                throw new ServiceException("只能对咨询类订单且未反馈过的记录进行操作");
            }

            capabilityReview.setFeedbackProcessing(dto.getFeedbackProcessing());
            capabilityReview.setReviewerOfFeedback(currentUser);
            capabilityReview.setReviewTimeOfFeedback(currentTime);
            capabilityReview.setComment(dto.getComment());
            capabilityReview.setCodexTorchUpdateDatetime(currentTime);

            capabilityReviewMapper.updateById(capabilityReview);

            updateCapabilityVerificationFeedBack(dto, capabilityReview.getCapabilityVerificationId());
        }


        response.setMessage("反馈成功");
        return response;
    }

    /**
     * 能力反馈更新能力核验
     *
     * @param dto
     * @param capabilityVerificationId
     */
    private void updateCapabilityVerificationFeedBack(CapabilityReviewFeedBackDTO dto, String capabilityVerificationId) {
        if (!HuatekTools.isEmpty(capabilityVerificationId)) {
            String currentUser = SecurityContextHolder.getCurrentUserName();
            TorchResponse<CapabilityVerificationVO> capabilityVerification = capabilityVerificationService.findCapabilityVerification(capabilityVerificationId);
            CapabilityVerificationVO capabilityVerificationVO = capabilityVerification.getData().getData();
            CapabilityVerificationDTO capabilityVerificationDTO = new CapabilityVerificationDTO();
            capabilityVerificationDTO.setId(capabilityVerificationVO.getId());
            capabilityVerificationDTO.setCapabilityFeedback(dto.getFeedbackProcessing());
            capabilityVerificationDTO.setFeedbacker(currentUser);
            capabilityVerificationDTO.setStatus(DicConstant.SalesOrder.CAPABILITY_VERIFICATION_STATUS_CONFIRMED);
            capabilityVerificationDTO.setComment(dto.getComment());
            capabilityVerificationService.saveOrUpdate(capabilityVerificationDTO);
        }
    }

    /**
     * 能力评审结果
     *
     * @param dto 能力评审结果DTO实体对象
     * @return
     * @throws Exception
     */
    @Override
    @SuppressWarnings("rawtypes")
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse updateReviewResult(CapabilityReviewResultDTO dto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());

        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new ServiceException("请选择需要更新的记录");
        }

        List<CapabilityReview> capabilityReviewList = capabilityReviewMapper.selectBatchIds(dto.getIds());
        for (CapabilityReview capabilityReview : capabilityReviewList) {
            if (!DicConstant.TechnicalManagement.CAPABILITY_REVIEW_DINGDAN.equals(capabilityReview.getType()) || !HuatekTools.isEmpty(capabilityReview.getFeedbackProcessing())) {
                throw new ServiceException("只能对类型为订单且未评审的记录进行操作");
            }
            if(HuatekTools.isEmpty(capabilityReview.getProductCategory())){
                throw new ServiceException("未设置产品分类不能进行操作");
            }

            capabilityReview.setReviewResult(dto.getReviewResult());
            capabilityReview.setFeedbackProcessing(dto.getFeedbackProcessing());

            capabilityReview.setReviewerOfFeedback(currentUser);
            capabilityReview.setReviewTimeOfFeedback(currentTime);

            capabilityReview.setComment(dto.getComment());
            capabilityReview.setCodexTorchUpdateDatetime(currentTime);

            capabilityReviewMapper.updateById(capabilityReview);

            updateEvaluationOrderReview(capabilityReview);

            //不可做 消息通知市场和调度
            if(DicConstant.TechnicalManagement.FEEDBACKPROCESSING_BUKESHAI.equals(capabilityReview.getFeedbackProcessing())) {

                sendMessageToMarketAndDispatch(capabilityReview.getProductName());

            }

        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("评审成功");
        return response;
    }

    /**
     * 发送消息通知市场和调度角色
     *
     * @param productName 产品名称
     */
    private void sendMessageToMarketAndDispatch(String productName) {
        try {
            List<String> userIds = sysUserService.selectUserIdsByRole(BusinessConstant.ROLE_SHICHANGJIESUAN);
            List<String> diaodus = sysUserService.selectUserIdsByRole(BusinessConstant.ROLE_DIAODU);
            userIds.addAll(diaodus);
            if (CollectionUtils.isEmpty(userIds)) {
                return;
            }
            // 发送消息
            MessageManagementDTO messageDto = new MessageManagementDTO();
            messageDto.setMessageContent("该产品试验能力不具备，不可做！产品名称：" + String.join(", ", productName));
            messageDto.setSendTime(new Timestamp(System.currentTimeMillis()));
            for (String userId : userIds) {
                messageDto.setUserId(userId);
                messageManagementService.saveOrUpdate(messageDto);
            }
            log.info("该产品试验能力不具备，不可做，产品名称：{}", String.join(", ", productName));
        } catch (Exception e) {
            log.error("该产品试验能力不具备，不可做 发送失败", e);
        }
    }
    /**
     * 更新测评订单评审结果
     *
     * @param capabilityReview
     */
    private void updateEvaluationOrderReview(CapabilityReview capabilityReview) {
        if (!HuatekTools.isEmpty(capabilityReview.getProductListId())) {
            String currentUser = SecurityContextHolder.getCurrentUserName();

            List<CapabilityReview> capabilityReviewList = capabilityReviewMapper.
                    selectList(new QueryWrapper<CapabilityReview>().eq("product_list_id", capabilityReview.getProductListId()));

            if (capabilityReviewList.stream().anyMatch(item -> HuatekTools.isEmpty(item.getReviewResult()))) {
                return;
            }
            // EvaluationOrderReviewDTO evaluationOrderReviewDTO = new EvaluationOrderReviewDTO();
            // if (capabilityReviewList.stream().allMatch(item -> DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES.equals(item.getReviewResult()))) {
            //     evaluationOrderReviewDTO.setReviewResult(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES);
            // } else {
            //     evaluationOrderReviewDTO.setReviewResult(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_NO);
            // }
            // evaluationOrderReviewDTO.setReviewComment(capabilityReview.getComment());
            // evaluationOrderReviewDTO.setProductListId(capabilityReview.getProductListId());
            // evaluationOrderReviewDTO.setReviewer(currentUser);
            // evaluationOrderReviewDTO.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
            // evaluationOrderReviewDTO.setCodexTorchUpdater(currentUser);
            // evaluationOrderReviewService.createEvalueationOrderReview(evaluationOrderReviewDTO);
            //根据productListId更新 evaluationOrderReview  reviewResult  reviewComment  reviewer  使用updatewrapper
            UpdateWrapper<EvaluationOrderReview> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("product_list_id", capabilityReview.getProductListId());
            if (capabilityReviewList.stream().allMatch(item -> DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES.equals(item.getReviewResult()))) {
                updateWrapper.set("review_result", DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES);
            } else {
                updateWrapper.set("review_result", DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_NO);
            }
            updateWrapper.set("review_comment", capabilityReview.getComment());
            updateWrapper.set("reviewer", currentUser);
            updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
            updateWrapper.set("codex_torch_updater", currentUser);
            evaluationOrderReviewMapper.update(null, updateWrapper);

        }
    }

    /**
     * 根据productlistid判断是否所有结果都为通过
     *
     * @param productListId
     * @return
     */
    @Override
    public boolean isReviewOkByPrioductListId(String productListId) {
        List<CapabilityReview> capabilityReviewList = capabilityReviewMapper.
                selectList(new QueryWrapper<CapabilityReview>().eq("product_list_id", productListId));
        if (capabilityReviewList.stream().allMatch(item -> DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES.equals(item.getReviewResult()))) {
            return true;
        }
        return false;
    }

    /**
     * 设置分类
     *
     * @param dto 能力设置分类DTO实体对象
     * @return
     */
    @Override
    @SuppressWarnings("rawtypes")
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse updateCategory(CapabilityReviewSetCategoryDTO dto) {
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());

        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new ServiceException("请选择需要更新的记录");
        }

        if (HuatekTools.isEmpty(dto.getProductCategory())) {
            throw new ServiceException("产品分类不能为空");
        }

        List<CapabilityReview> capabilityReviewList = capabilityReviewMapper.selectBatchIds(dto.getIds());
        for (CapabilityReview capabilityReview : capabilityReviewList) {
            capabilityReview.setProductCategory(dto.getProductCategory());
            capabilityReview.setCodexTorchUpdateDatetime(currentTime);
            capabilityReviewMapper.updateById(capabilityReview);
            updateProductionOrderProductCategory(capabilityReview.getProductListId(), dto.getProductCategory());

        }


        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("设置产品分类成功");
        return response;
    }

    /**
     * 更新ProductList和ProductManagement的产品分类
     *
     * @param productListId
     * @param productCategory
     */
    private void updateProductionOrderProductCategory(String productListId, String productCategory) {
        if (!HuatekTools.isEmpty(productListId)) {


            //1.查询productlist2.更新productCategory
            ProductList productList = productListMapper.selectById(productListId);
            if (productList == null) {
                return;
            }
            UpdateWrapper<ProductList> up = new UpdateWrapper<>();
            up.set("product_category", productCategory);
            up.eq("id", productListId);
            productListMapper.update(null, up);

            if (HuatekTools.isEmpty(productList.getProductId())) {
                return;
            }
            //2.更新productManagement
            UpdateWrapper<ProductManagement> pm = new UpdateWrapper<>();
            pm.set("product_category", productCategory);
            pm.eq("id", productList.getProductId());
            productManagementMapper.update(null, pm);

        }
    }

    /**
     * 转开发
     *
     * @param ids
     * @return
     */
    @Override
    @SuppressWarnings("rawtypes")
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse toDevelopment(List<String> ids) {
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        for (String id : ids) {
            // 根据id查询能力评审数据
            CapabilityReview entity = capabilityReviewMapper.selectById(id);
            if (entity == null) {
                response.setMessage("未找到能力评审数据,id:" + id);
                return response;
            }
            //只有待转开发的才能转开发
            if (!DicConstant.TechnicalManagement.FEEDBACKPROCESSING_DAIZHUANKAIFA.equals(entity.getFeedbackProcessing())) {
                response.setMessage("只有待转开发的才能转开发");
                return response;
            }



            CapabilityDevelopmentAddDTO developmentAddDTO = new CapabilityDevelopmentAddDTO();
            BeanUtils.copyProperties(entity, developmentAddDTO);

            developmentAddDTO.setId("");
            capabilityDevelopmentService.saveOrUpdateCapabilityDevelopment(developmentAddDTO);


            entity.setFeedbackProcessing(DicConstant.TechnicalManagement.FEEDBACKPROCESSING_YIZHUANKAIFA);
            capabilityReviewMapper.updateById(entity);
        }

        response.setMessage("转开发成功");

        return response;
    }


    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse abnormalfeedback(AbnormalfeedbackDTO abnormalfeedbackDto) {

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);


        abnormalfeedbackDto.setSourceModule(BusinessConstant.CAPABILITY_REVIEW_MENU_NAME);
        CapabilityReview entity = capabilityReviewMapper.selectById(abnormalfeedbackDto.getSourceId());

        TorchResponse<AbnormalfeedbackVO> torchResponse = abnormalfeedbackService.saveOrUpdate(abnormalfeedbackDto);
        AbnormalfeedbackVO vo = torchResponse.getData().getData();


        if (HuatekTools.isEmpty(entity.getAssocExceptionFeedbackNum())) {
            entity.setAssocExceptionFeedbackNum(vo.getAbnormalNumber());
        } else {
            entity.setAssocExceptionFeedbackNum(entity.getAssocExceptionFeedbackNum() + "," + vo.getAbnormalNumber());
        }

        capabilityReviewMapper.updateById(entity);

        return response;
    }

    @Override
    public TorchResponse updateProductInformation(CapabilityReviewProductInformationDto dto) {

        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new ServiceException("请选择需要设置产品资料的记录");
        }
        if (HuatekTools.isEmpty(dto.getProductInformation())) {
            throw new ServiceException("产品资料不能为空");
        }
        if (HuatekTools.isEmpty(dto.getProductInformationNumber())) {
            throw new ServiceException("产品资料编号不能为空");
        }

        UpdateWrapper<CapabilityReview> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("product_information1", dto.getProductInformation());
        updateWrapper.set("product_information1_number", dto.getProductInformationNumber());
        updateWrapper.in("id", dto.getIds());
        updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
        updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
        capabilityReviewMapper.update(null, updateWrapper);

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("设置产品资料成功");
        return response;
    }
}
