package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description 核算标准明细VO实体类
* <AUTHOR>
* @date 2025-08-22
**/
@Data
@ApiModel("核算标准明细DTO实体类")
public class AccountingStandardDetailsVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productCategory;

    /**
	 * 试验项目
     **/
    @ApiModelProperty("试验项目")
    @Excel(name = "试验项目",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String experimentProject;

    /**
	 * 数值
     **/
    @ApiModelProperty("数值")
    @Excel(name = "数值",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private BigDecimal quantity;

    /**
	 * 单位
     **/
    @ApiModelProperty("单位")
    @Excel(name = "单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String unit;

    /**
	 * 收费形式
     **/
    @ApiModelProperty("收费形式")
    @Excel(name = "收费形式",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String chargingMethod;

    /**
	 * 最低数量要求
     **/
    @ApiModelProperty("最低数量要求")
    @Excel(name = "最低数量要求",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Integer minimumQuantityRequirement;

    /**
	 * 基础价格
     **/
    @ApiModelProperty("基础价格")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "基础价格",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal basePrice;

    /**
	 * 收费标准编号
     **/
    @ApiModelProperty("收费标准编号")
    @Excel(name = "收费标准编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String chargeStandardNumber;

    /**
	 * 低于要求数量计算倍数
     **/
    @ApiModelProperty("低于要求数量计算倍数")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "低于要求数量计算倍数",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal cfbrq;

    /**
	 * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}