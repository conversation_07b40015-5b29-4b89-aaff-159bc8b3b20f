package com.huatek.frame.modules.business.domain;


import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 板卡存放
* <AUTHOR>
* @date 2025-08-04
**/
@Setter
@Getter
@TableName("cardinventory")
public class Cardinventory implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * DUT板名称
     **/
    @TableField(value = "dut_board_name"
    )
    private String dutBoardName;

    
    /**
	 * DUT板编号
     **/
    @TableField(value = "dut_board_number"
    )
    private String dutBoardNumber;

    
    /**
	 * DUT板类型
     **/
    @TableField(value = "dut_board_type"
    )
    private String dutBoardType;

    
    /**
	 * 每板能力
     **/
    @TableField(value = "board_capacity"
    )
    private String boardCapacity;

    
    /**
	 * 存放位置
     **/
    @TableField(value = "storage_location"
    )
    private String storageLocation;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}