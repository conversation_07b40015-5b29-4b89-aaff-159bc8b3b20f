management:
    health:
        mail:
            enabled: false
    server:
        servlet:
            context-path: /actuator
mybatis:
    mapper-locations: classpath:/mybatis-mapper/*Mapper.xml
server:
    port: 8883
    servlet:
        context-path: /job
spring:
    datasource:
        driver-class-name: com.mysql.cj.jdbc.Driver
        hikari:
            auto-commit: true
            connection-test-query: SELECT 1
            connection-timeout: 10000
            idle-timeout: 30000
            max-lifetime: 900000
            maximum-pool-size: 30
            minimum-idle: 10
            pool-name: HikariCP
        type: com.zaxxer.hikari.HikariDataSource
        url: ********************************************************************************************************************************
        username: root
        password: 123456
    freemarker:
        charset: UTF-8
        request-context-attribute: request
        settings:
            number_format: 0.##########
        suffix: .ftl
        templateLoaderPath: classpath:/templates/
    mail:
        from: <EMAIL>
        host: smtp.qq.com
        password: xxx
        port: 25
        properties:
            mail:
                smtp:
                    auth: true
                    socketFactory:
                        class: javax.net.ssl.SSLSocketFactory
                    starttls:
                        enable: true
                        required: true
        username: <EMAIL>
    mvc:
        servlet:
            load-on-startup: 0
        static-path-pattern: /static/**
    resources:
        static-locations: classpath:/static/
xxl:
    job:
        accessToken: ''
        i18n: zh_CN
        logretentiondays: 30
        triggerpool:
            fast:
                max: 200
            slow:
                max: 100