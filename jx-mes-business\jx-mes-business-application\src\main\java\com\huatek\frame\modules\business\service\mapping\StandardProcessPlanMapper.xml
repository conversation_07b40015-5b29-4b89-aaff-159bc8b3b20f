<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.StandardProcessPlanMapper">
	<sql id="Base_Column_List">
		t.id as id,
<!--		t.test_type as testType,-->
		t.process_scheme_name as processSchemeName,
<!--		t.entrusted_unit as entrustedUnit,-->
<!--		t.product_model as productModel,-->
<!--		t.product_category as productCategory,-->
<!--		t.standard_specification_number as standardSpecificationNumber,-->
<!--		t.standard_specification_name as standardSpecificationName,-->
		t.manufacturer as manufacturer,
		t.quality_grade as qualityGrade,
		t.package_form as packageForm,
		t.pda as pda,
		t.test_package as testPackage,
		t.status as status,
		t.`comment` as `comment`,
        t.report_comment as reportComment,
<!--		t.department as department,-->
		t.CODEX_TORCH_DETAIL_ITEM_IDS as codexTorchDetailItemIds,
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectStandardProcessPlanPage" parameterType="com.huatek.frame.modules.business.service.dto.StandardProcessPlanDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.StandardProcessPlanVO">
		select
		<include refid="Base_Column_List" />,(select sdd.dname  from sys_dictionnaire_detail sdd left join sys_dictionnaire sd on sdd.pid = sd.id
        where sd.code='test_data_dictionary_testType' and sdd.dcode=t.test_type) as testType,m.product_model ,cim.entrusted_unit,sg.group_name as department,
        pc.category_name  as productCategory,m.product_name as productName,ss.controlled_number as standardSpecificationNumber,
        ss.specification_name as standardSpecificationName
        from standard_process_plan t
        left join product_management m on t.product_model =m.id
        left join standard_specification ss on t.standard_specification_number =ss.id
        left join customer_information_management cim on t.entrusted_unit =cim.id
        left join sys_group sg on t.department  = sg.id
        left join product_category pc on t.product_category = pc.id
        <where>
                and 1=1
                <if test="testType != null and testType != ''">
                    and t.test_type  like concat('%', #{testType} ,'%')
                </if>
                <if test="processSchemeName != null and processSchemeName != ''">
                    and t.process_scheme_name  like concat('%', #{processSchemeName} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and (t.product_model  like concat('%', #{productModel} ,'%') or m.product_model  like concat('%', #{productModel} ,'%'))
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pc.category_name  like concat('%', #{productCategory} ,'%')
                </if>
<!--                <if test="productName != null and productName != ''">-->
<!--                    and m.product_name  like concat('%', #{productName} ,'%')-->
<!--                </if>-->
                <if test="standardSpecificationNumber != null and standardSpecificationNumber != ''">
                    and t.standard_specification_number  like concat('%', #{standardSpecificationNumber} ,'%')
                </if>
                <if test="standardSpecificationName != null and standardSpecificationName != ''">
                    and ss.specification_name  like concat('%', #{standardSpecificationName} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="qualityGrade != null and qualityGrade != ''">
                    and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
                </if>
                <if test="packageForm != null and packageForm != ''">
                    and t.package_form  like concat('%', #{packageForm} ,'%')
                </if>
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="testPackage != null and testPackage != ''">
                    and t.test_package REGEXP #{testPackage}
                </if>
                <if test="status != null and status != ''">
                    and t.status REGEXP #{status}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="department != null and department != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{department} ,'%')
                </if>
                <if test="codexTorchDetailItemIds != null and codexTorchDetailItemIds != ''">
                    and t.CODEX_TORCH_DETAIL_ITEM_IDS  like concat('%', #{codexTorchDetailItemIds} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
        order by t.CODEX_TORCH_CREATE_DATETIME desc
	</select>

    <select id="selectStandardProcessPlanList" parameterType="com.huatek.frame.modules.business.service.dto.StandardProcessPlanDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.StandardProcessPlanVO">
		select
		<include refid="Base_Column_List" />,(select sdd.dname  from sys_dictionnaire_detail sdd left join sys_dictionnaire sd on sdd.pid = sd.id
        where sd.code='test_data_dictionary_testType' and sdd.dcode=t.test_type) as testType,m.product_model ,cim.entrusted_unit,sg.group_name as department,
        pc.category_name  as productCategory,m.product_name as productName,ss.controlled_number as standardSpecificationNumber,
        ss.specification_name as standardSpecificationName
        from standard_process_plan t
        left join product_management m on t.product_model =m.id
        left join standard_specification ss on t.standard_specification_number =ss.id
        left join customer_information_management cim on t.entrusted_unit =cim.id
        left join sys_group sg on t.CODEX_TORCH_GROUP_ID  = sg.id
        left join product_category pc on t.product_category = pc.id
            <where>
                and 1=1
                <if test="testType != null and testType != ''">
                    and t.test_type  like concat('%', #{testType} ,'%')
                </if>
                <if test="processSchemeName != null and processSchemeName != ''">
                    and t.process_scheme_name  like concat('%', #{processSchemeName} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pc.category_name  like concat('%', #{productCategory} ,'%')
                </if>
<!--                <if test="productName != null and productName != ''">-->
<!--                    and m.product_name  like concat('%', #{productName} ,'%')-->
<!--                </if>-->
                <if test="standardSpecificationNumber != null and standardSpecificationNumber != ''">
                    and t.standard_specification_number  like concat('%', #{standardSpecificationNumber} ,'%')
                </if>
                <if test="standardSpecificationName != null and standardSpecificationName != ''">
                    and ss.specification_name  like concat('%', #{standardSpecificationName} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="qualityGrade != null and qualityGrade != ''">
                    and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
                </if>
                <if test="packageForm != null and packageForm != ''">
                    and t.package_form  like concat('%', #{packageForm} ,'%')
                </if>
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="testPackage != null and testPackage != ''">
                    and t.test_package REGEXP #{testPackage}
                </if>
                <if test="status != null and status != ''">
                    and t.status REGEXP #{status}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="department != null and department != ''">
                    and t.department  like concat('%', #{department} ,'%')
                </if>
                <if test="codexTorchDetailItemIds != null and codexTorchDetailItemIds != ''">
                    and t.CODEX_TORCH_DETAIL_ITEM_IDS  like concat('%', #{codexTorchDetailItemIds} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectStandardProcessPlanListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.StandardProcessPlanVO">
		select
		<include refid="Base_Column_List" />,(select sdd.dname  from sys_dictionnaire_detail sdd left join sys_dictionnaire sd on sdd.pid = sd.id
        where sd.code='test_data_dictionary_testType' and sdd.dcode=t.test_type) as testType,m.product_model ,cim.entrusted_unit,sg.group_name as department,
        pc.category_name  as productCategory,m.product_name as productName,ss.controlled_number as standardSpecificationNumber,
        ss.specification_name as standardSpecificationName
        from standard_process_plan t
        left join product_management m on t.product_model =m.id
        left join standard_specification ss on t.standard_specification_number =ss.id
        left join customer_information_management cim on t.entrusted_unit =cim.id
        left join sys_group sg on t.CODEX_TORCH_GROUP_ID  = sg.id
        left join product_category pc on m.product_category = pc.id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>

	<select id="selectDataLinkageByEntrustedUnit" resultType="java.util.Map">
        select
        t.settlement_unit as settlementUnit
        from customer_information_management t
        WHERE t.customer_id0 = #{entrustedUnit}
    </select>
    <select id="selectOptionsByEntrustedUnit" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.entrusted_unit label,
        t.id value
        from customer_information_management t
        WHERE t.CODEX_TORCH_DELETED = '0'
    </select>
    <select id="selectOptionsByProductModel" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.product_model label,
        t.id value
        from product_management t
        WHERE t.CODEX_TORCH_DELETED = '0'
    </select>
    <select id="selectOptionsByProductCategory" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.category_name  label,
        t.id value
        from product_category t
        WHERE t.CODEX_TORCH_DELETED = '0'
    </select>
    <select id="selectOptionsByDepartment" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.group_name label,
        t.id value
        from sys_group t
        WHERE t.group_code != ''
        and t.group_parent_id in ( select sg.id from sys_group sg  where sg.group_parent_id ='0')
    </select>



    <select id="selectDataLinkageByProductModel" parameterType="String"
            resultType="java.util.Map">
        select
            t.product_name as productName,
            t.product_category as productCategory,
            t.manufacturer as manufacturer
        from product_management t
        WHERE t.id = #{productModel}
    </select>
    <select id="selectOptionsByStandardSpecificationNumber" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.specification_number label,
        t.id value
        from standard_specification t
        WHERE t.CODEX_TORCH_DELETED = '0'
    </select>
    <select id="selectDataLinkageByStandardSpecificationNumber" parameterType="String"
            resultType="java.util.Map">
        select
        t.specification_name as standardSpecificationName
        from standard_specification t
        WHERE t.id = #{specificationNumber}
    </select>


</mapper>