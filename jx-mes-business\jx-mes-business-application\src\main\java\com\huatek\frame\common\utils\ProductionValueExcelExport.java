package com.huatek.frame.common.utils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationDetailExportVO;

import java.io.File;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 示例：动态列导出 Excel
 */
public class ProductionValueExcelExport {

    public static void main(String[] args) throws Exception {

        String json = "[{\"workOrderNumber\":\"S202508008\",\"productName\":\"测试1\",\"productModel\":\"测试1\",\"batchNumber\":\"测试1\",\"productQuantity\":5,\"settlementUnit\":\"西安华讯2\",\"entrustedUnit\":\"1948218882877730817\",\"numberOfQualifiedProductions\":null,\"numNonQualProd\":null,\"nonQualityProcess\":null,\"chargingStandardName\":\"收费标准1\",\"customerAccountingPrice\":\"0\",\"testType\":\"14\",\"dateOfEntrustment\":\"2025-08-20\",\"completionTime\":null,\"orderInspectionNumber\":\"测试1\",\"workOrderInspectionNumber1\":\"测试1\",\"shippingDate\":null,\"recipientCustomer\":null,\"customerPriceClassification\":null,\"dynamicColumns\":{\"测试工序2\":2.0,\"测试工序********\":19.0,\"测试工序5454\":54.0}},{\"workOrderNumber\":\"S202508009\",\"productName\":\"测试1\",\"productModel\":\"测试1\",\"batchNumber\":\"测试1\",\"productQuantity\":5,\"settlementUnit\":\"西安华讯2\",\"entrustedUnit\":\"1948218882877730817\",\"numberOfQualifiedProductions\":null,\"numNonQualProd\":null,\"nonQualityProcess\":null,\"chargingStandardName\":\"收费标准1\",\"customerAccountingPrice\":\"0\",\"testType\":\"14\",\"dateOfEntrustment\":\"2025-08-20\",\"completionTime\":null,\"orderInspectionNumber\":\"测试1\",\"workOrderInspectionNumber1\":\"测试1\",\"shippingDate\":null,\"recipientCustomer\":null,\"customerPriceClassification\":null,\"dynamicColumns\":{\"测试工序2\":22.0,\"测试工序88\":88.0,\"测试工序99\":99.0}}]";

        ObjectMapper mapper = new ObjectMapper();
        List<ProductionValueCalculationDetailExportVO> list = Arrays.asList(
                mapper.readValue(json, ProductionValueCalculationDetailExportVO[].class));

        // 收集所有动态列名，保证表头不重复
        Set<String> dynamicHeaderSet = new LinkedHashSet<>();
        for (ProductionValueCalculationDetailExportVO vo : list) {
            if (vo.getDynamicColumns() != null) {
                dynamicHeaderSet.addAll(vo.getDynamicColumns().keySet());
            }
        }

        // 生成 Excel 数据
        List<List<String>> excelData = new ArrayList<>();
        for (ProductionValueCalculationDetailExportVO vo : list) {
            List<String> row = new ArrayList<>();
            // 主字段
            row.add(vo.getWorkOrderNumber() != null ? vo.getWorkOrderNumber() : "");
            row.add(vo.getProductName() != null ? vo.getProductName() : "");
            row.add(vo.getProductModel() != null ? vo.getProductModel() : "");
            row.add(vo.getBatchNumber() != null ? vo.getBatchNumber() : "");
            row.add(vo.getProductQuantity() != null ? vo.getProductQuantity().toString() : "0");
            row.add(vo.getSettlementUnit() != null ? vo.getSettlementUnit() : "");
            row.add(vo.getEntrustedUnit() != null ? vo.getEntrustedUnit() : "");
            row.add(vo.getNumberOfQualifiedProductions() != null ? vo.getNumberOfQualifiedProductions().toString() : "0");
            row.add(vo.getNumNonQualProd() != null ? vo.getNumNonQualProd().toString() : "0");
            row.add(vo.getNonQualityProcess() != null ? vo.getNonQualityProcess() : "");
            row.add(vo.getChargingStandardName() != null ? vo.getChargingStandardName() : "");
            row.add(vo.getCustomerAccountingPrice() != null ? String.valueOf(vo.getCustomerAccountingPrice()) : "0");
            row.add(vo.getTestType() != null ? vo.getTestType() : "");
            row.add(vo.getDateOfEntrustment() != null ? vo.getDateOfEntrustment().toString() : "");
            row.add(vo.getCompletionTime() != null ? vo.getCompletionTime().toString() : "");
            row.add(vo.getOrderInspectionNumber() != null ? vo.getOrderInspectionNumber() : "");
            row.add(vo.getWorkOrderInspectionNumber1() != null ? vo.getWorkOrderInspectionNumber1() : "");
            row.add(vo.getShippingDate() != null ? vo.getShippingDate().toString() : "");
            row.add(vo.getRecipientCustomer() != null ? vo.getRecipientCustomer() : "");
            row.add(vo.getCustomerPriceClassification() != null ? vo.getCustomerPriceClassification() : "");

            // 动态列
            for (String key : dynamicHeaderSet) {
                BigDecimal value = vo.getDynamicColumns() != null ? vo.getDynamicColumns().get(key) : null;
                row.add(value != null ? value.toString() : "0");
            }

            excelData.add(row);

            // 打印调试信息
            System.out.println("添加了一行数据：" + row);
        }

        // 表头顺序
        List<String> headers = new ArrayList<>();
        headers.addAll(Arrays.asList("工单编号", "产品名称", "产品型号", "生产批次", "产品数量",
                "结算单位", "委托单位", "合格数", "不合格数", "不合格工序",
                "收费标准", "合计费用", "试验类型", "委托日期", "完成日期",
                "订单送检编号", "工单送检编号", "发货时间", "快递接收人", "价格分类"));
        headers.addAll(dynamicHeaderSet); // 动态列放最后

        // 写 Excel
        try {
            System.out.println("准备写入数据，行数：" + excelData.size());
            // 创建一个文件对象
            File file = new File("产值明细.xlsx");

            // 构建表头
            List<List<String>> headerList = headers.stream()
                    .map(Collections::singletonList)
                    .collect(Collectors.toList());

            // 使用EasyExcel写入数据
            EasyExcel.write(file)
                    .head(headerList)
                    .registerWriteHandler(new com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy())
                    .sheet("产值明细")
                    .doWrite(excelData);

            System.out.println("Excel 导出完成！路径：" + file.getAbsolutePath());
        } catch (Exception e) {
            System.err.println("Excel 导出失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
