2025-08-21 11:18:49,817 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:18:49,817 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:18:50,461 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-gateway&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=DEFAULT, body: {}
2025-08-21 11:18:52,309 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:18:52,757 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-basic-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:18:54,826 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:18:54,826 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:18:55,516 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-business-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:18:55,834 DEBUG gateway [com.alibaba.nacos.client.Worker.longPolling.fixed-127.0.0.1_8848] c.a.n.c.c.i.ConfigHttpClientManager [NacosRestTemplate.java : 476] HTTP method: POST, url: http://127.0.0.1:8848/nacos/v1/cs/configs/listener, body: {Listening-Configs=jx-mes-gateway.ymljx-mesjx-mes-gateway-dev.ymljx-mesa8e919368480df96d9168efca2722bcfjx-mes-gatewayjx-mes, accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.FPy4T01F9NudRA9YQpndGEFmw-DWOd6vnnfeAS7R_TM}
2025-08-21 11:18:55,835 DEBUG gateway [com.alibaba.nacos.client.Worker.longPolling.fixed-127.0.0.1_8848] c.a.n.c.c.i.ConfigHttpClientManager [NacosRestTemplate.java : 494] Execute via interceptors :[com.alibaba.nacos.client.config.impl.ConfigHttpClientManager$LimiterHttpClientRequestInterceptor@1d2b6e39]
2025-08-21 11:18:57,311 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:18:58,983 DEBUG gateway [RibbonApacheHttpClientConfiguration.connectionManagerTimer] o.a.h.i.c.PoolingHttpClientConnectionManager [PoolingHttpClientConnectionManager.java : 448] Closing expired connections
2025-08-21 11:18:59,831 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:18:59,831 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:00,476 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-gateway&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=DEFAULT, body: {}
2025-08-21 11:19:00,624 DEBUG gateway [PollingServerListUpdater-0] com.alibaba.nacos.client.naming [HostReactor.java : 306] failover-mode: false
2025-08-21 11:19:00,624 DEBUG gateway [PollingServerListUpdater-0] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 241] List of Servers for jx-mes-basic-application obtained from Discovery client: [************:8882]
2025-08-21 11:19:00,624 DEBUG gateway [PollingServerListUpdater-0] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 246] Filtered List of Servers for jx-mes-basic-application obtained from Discovery client: [************:8882]
2025-08-21 11:19:00,624 DEBUG gateway [PollingServerListUpdater-0] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 491] LoadBalancer [jx-mes-basic-application]: clearing server list (SET op)
2025-08-21 11:19:00,624 DEBUG gateway [PollingServerListUpdater-0] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 507] LoadBalancer [jx-mes-basic-application]:  addServer [************:8882]
2025-08-21 11:19:00,624 DEBUG gateway [PollingServerListUpdater-0] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 179] Setting server list for zones: {unknown=[************:8882]}
2025-08-21 11:19:00,624 DEBUG gateway [PollingServerListUpdater-0] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 491] LoadBalancer [jx-mes-basic-application_unknown]: clearing server list (SET op)
2025-08-21 11:19:00,624 DEBUG gateway [PollingServerListUpdater-0] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 507] LoadBalancer [jx-mes-basic-application_unknown]:  addServer [************:8882]
2025-08-21 11:19:02,324 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:19:02,761 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-basic-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:04,846 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:19:04,846 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:05,521 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-business-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:07,325 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:19:07,426 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NamingProxy.java : 195] server list provided by user: [127.0.0.1:8848]
2025-08-21 11:19:09,473 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition basic applying {_genkey_0=/basic/**} to Path
2025-08-21 11:19:09,474 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: basic
2025-08-21 11:19:09,475 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition business applying {_genkey_0=/business/**} to Path
2025-08-21 11:19:09,475 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: business
2025-08-21 11:19:09,476 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition job applying {_genkey_0=/job/**} to Path
2025-08-21 11:19:09,477 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: job
2025-08-21 11:19:09,477 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition log applying {_genkey_0=/log/**} to Path
2025-08-21 11:19:09,478 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: log
2025-08-21 11:19:09,478 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition gen applying {_genkey_0=/gen/**} to Path
2025-08-21 11:19:09,479 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: gen
2025-08-21 11:19:09,480 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition sso applying {_genkey_0=/sso/**} to Path
2025-08-21 11:19:09,480 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: sso
2025-08-21 11:19:09,480 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition sgin applying {_genkey_0=/sgin/**} to Path
2025-08-21 11:19:09,481 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: sgin
2025-08-21 11:19:09,850 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:19:09,850 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:10,481 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-gateway&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=DEFAULT, body: {}
2025-08-21 11:19:11,747 DEBUG gateway [RibbonApacheHttpClientConfiguration.connectionManagerTimer] o.a.h.i.c.PoolingHttpClientConnectionManager [PoolingHttpClientConnectionManager.java : 448] Closing expired connections
2025-08-21 11:19:12,328 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:19:12,768 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-basic-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:14,007 DEBUG gateway [PollingServerListUpdater-1] com.alibaba.nacos.client.naming [HostReactor.java : 306] failover-mode: false
2025-08-21 11:19:14,007 DEBUG gateway [PollingServerListUpdater-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 241] List of Servers for jx-mes-business-application obtained from Discovery client: [************:8886]
2025-08-21 11:19:14,007 DEBUG gateway [PollingServerListUpdater-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 246] Filtered List of Servers for jx-mes-business-application obtained from Discovery client: [************:8886]
2025-08-21 11:19:14,007 DEBUG gateway [PollingServerListUpdater-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 491] LoadBalancer [jx-mes-business-application]: clearing server list (SET op)
2025-08-21 11:19:14,007 DEBUG gateway [PollingServerListUpdater-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 507] LoadBalancer [jx-mes-business-application]:  addServer [************:8886]
2025-08-21 11:19:14,007 DEBUG gateway [PollingServerListUpdater-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 179] Setting server list for zones: {unknown=[************:8886]}
2025-08-21 11:19:14,007 DEBUG gateway [PollingServerListUpdater-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 491] LoadBalancer [jx-mes-business-application_unknown]: clearing server list (SET op)
2025-08-21 11:19:14,007 DEBUG gateway [PollingServerListUpdater-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 507] LoadBalancer [jx-mes-business-application_unknown]:  addServer [************:8886]
2025-08-21 11:19:14,871 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:19:14,871 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:15,527 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-business-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:17,329 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:19:19,879 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:19:19,879 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:20,488 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-gateway&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=DEFAULT, body: {}
2025-08-21 11:19:22,331 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:19:22,778 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-basic-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:24,885 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:19:24,885 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:25,348 DEBUG gateway [com.alibaba.nacos.client.Worker.longPolling.fixed-127.0.0.1_8848] c.a.n.c.c.i.ConfigHttpClientManager [NacosRestTemplate.java : 476] HTTP method: POST, url: http://127.0.0.1:8848/nacos/v1/cs/configs/listener, body: {Listening-Configs=jx-mes-gateway.ymljx-mesjx-mes-gateway-dev.ymljx-mesa8e919368480df96d9168efca2722bcfjx-mes-gatewayjx-mes, accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.FPy4T01F9NudRA9YQpndGEFmw-DWOd6vnnfeAS7R_TM}
2025-08-21 11:19:25,349 DEBUG gateway [com.alibaba.nacos.client.Worker.longPolling.fixed-127.0.0.1_8848] c.a.n.c.c.i.ConfigHttpClientManager [NacosRestTemplate.java : 494] Execute via interceptors :[com.alibaba.nacos.client.config.impl.ConfigHttpClientManager$LimiterHttpClientRequestInterceptor@1d2b6e39]
2025-08-21 11:19:25,531 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-business-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:27,336 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:19:28,984 DEBUG gateway [RibbonApacheHttpClientConfiguration.connectionManagerTimer] o.a.h.i.c.PoolingHttpClientConnectionManager [PoolingHttpClientConnectionManager.java : 448] Closing expired connections
2025-08-21 11:19:29,891 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:19:29,891 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:30,493 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-gateway&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=DEFAULT, body: {}
2025-08-21 11:19:30,625 DEBUG gateway [PollingServerListUpdater-0] com.alibaba.nacos.client.naming [HostReactor.java : 306] failover-mode: false
2025-08-21 11:19:30,625 DEBUG gateway [PollingServerListUpdater-0] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 241] List of Servers for jx-mes-basic-application obtained from Discovery client: [************:8882]
2025-08-21 11:19:30,625 DEBUG gateway [PollingServerListUpdater-0] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 246] Filtered List of Servers for jx-mes-basic-application obtained from Discovery client: [************:8882]
2025-08-21 11:19:30,625 DEBUG gateway [PollingServerListUpdater-0] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 491] LoadBalancer [jx-mes-basic-application]: clearing server list (SET op)
2025-08-21 11:19:30,625 DEBUG gateway [PollingServerListUpdater-0] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 507] LoadBalancer [jx-mes-basic-application]:  addServer [************:8882]
2025-08-21 11:19:30,626 DEBUG gateway [PollingServerListUpdater-0] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 179] Setting server list for zones: {unknown=[************:8882]}
2025-08-21 11:19:30,626 DEBUG gateway [PollingServerListUpdater-0] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 491] LoadBalancer [jx-mes-basic-application_unknown]: clearing server list (SET op)
2025-08-21 11:19:30,626 DEBUG gateway [PollingServerListUpdater-0] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 507] LoadBalancer [jx-mes-basic-application_unknown]:  addServer [************:8882]
2025-08-21 11:19:32,338 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:19:32,789 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-basic-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:34,905 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:19:34,905 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:35,536 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-business-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:37,340 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:19:37,427 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NamingProxy.java : 195] server list provided by user: [127.0.0.1:8848]
2025-08-21 11:19:39,485 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition basic applying {_genkey_0=/basic/**} to Path
2025-08-21 11:19:39,487 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: basic
2025-08-21 11:19:39,487 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition business applying {_genkey_0=/business/**} to Path
2025-08-21 11:19:39,488 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: business
2025-08-21 11:19:39,488 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition job applying {_genkey_0=/job/**} to Path
2025-08-21 11:19:39,489 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: job
2025-08-21 11:19:39,490 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition log applying {_genkey_0=/log/**} to Path
2025-08-21 11:19:39,491 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: log
2025-08-21 11:19:39,491 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition gen applying {_genkey_0=/gen/**} to Path
2025-08-21 11:19:39,492 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: gen
2025-08-21 11:19:39,492 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition sso applying {_genkey_0=/sso/**} to Path
2025-08-21 11:19:39,493 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: sso
2025-08-21 11:19:39,494 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 271] RouteDefinition sgin applying {_genkey_0=/sgin/**} to Path
2025-08-21 11:19:39,495 DEBUG gateway [Nacos-Watch-Task-Scheduler-1] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 162] RouteDefinition matched: sgin
2025-08-21 11:19:39,909 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:19:39,909 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:40,497 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-gateway&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=DEFAULT, body: {}
2025-08-21 11:19:41,747 DEBUG gateway [RibbonApacheHttpClientConfiguration.connectionManagerTimer] o.a.h.i.c.PoolingHttpClientConnectionManager [PoolingHttpClientConnectionManager.java : 448] Closing expired connections
2025-08-21 11:19:42,341 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
2025-08-21 11:19:42,795 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-basic-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:44,011 DEBUG gateway [PollingServerListUpdater-1] com.alibaba.nacos.client.naming [HostReactor.java : 306] failover-mode: false
2025-08-21 11:19:44,011 DEBUG gateway [PollingServerListUpdater-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 241] List of Servers for jx-mes-business-application obtained from Discovery client: [************:8886]
2025-08-21 11:19:44,012 DEBUG gateway [PollingServerListUpdater-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 246] Filtered List of Servers for jx-mes-business-application obtained from Discovery client: [************:8886]
2025-08-21 11:19:44,012 DEBUG gateway [PollingServerListUpdater-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 491] LoadBalancer [jx-mes-business-application]: clearing server list (SET op)
2025-08-21 11:19:44,012 DEBUG gateway [PollingServerListUpdater-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 507] LoadBalancer [jx-mes-business-application]:  addServer [************:8886]
2025-08-21 11:19:44,013 DEBUG gateway [PollingServerListUpdater-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 179] Setting server list for zones: {unknown=[************:8886]}
2025-08-21 11:19:44,013 DEBUG gateway [PollingServerListUpdater-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 491] LoadBalancer [jx-mes-business-application_unknown]: clearing server list (SET op)
2025-08-21 11:19:44,013 DEBUG gateway [PollingServerListUpdater-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 507] LoadBalancer [jx-mes-business-application_unknown]:  addServer [************:8886]
2025-08-21 11:19:44,917 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NamingProxy.java : 421] [BEAT] public sending beat to server: BeatInfo{port=8881, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-gateway', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false}
2025-08-21 11:19:44,917 DEBUG gateway [com.alibaba.nacos.naming.beat.sender] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: PUT, url: http://127.0.0.1:8848/nacos/v1/ns/instance/beat?app=unknown&namespaceId=public&port=8881&clusterName=DEFAULT&ip=************&serviceName=jx-mes%40%40jx-mes-gateway&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU, body: {}
2025-08-21 11:19:45,542 DEBUG gateway [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [NacosRestTemplate.java : 476] HTTP method: GET, url: http://127.0.0.1:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=public&clientIP=************&serviceName=jx-mes%40%40jx-mes-business-application&udpPort=54254&accessToken=eyJhbGciOiJIUzI1NiJ9.*******************************************.q1XDJH_hxGqnRGQvcEuiXwdFE8zXh0lIySNUx90W8hU&clusters=, body: {}
2025-08-21 11:19:47,343 DEBUG gateway [com.alibaba.nacos.naming.failover] com.alibaba.nacos.client.naming [FailoverReactor.java : 147] failover switch is not found, failover00-00---000-VIPSRV_FAILOVER_SWITCH-000---00-00
