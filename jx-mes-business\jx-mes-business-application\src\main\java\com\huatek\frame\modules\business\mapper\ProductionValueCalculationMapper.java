package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.ProductionValueCalculation;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationVO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 产值计算mapper
* <AUTHOR>
* @date 2025-08-22
**/
public interface ProductionValueCalculationMapper extends BaseMapper<ProductionValueCalculation> {

     /**
	 * 产值计算分页
	 * @param dto
	 * @return
	 */
	Page<ProductionValueCalculationVO> selectProductionValueCalculationPage(ProductionValueCalculationDTO dto);


    /**
     * 根据条件查询产值计算列表
     *
     * @param dto 产值计算信息
     * @return 产值计算集合信息
     */
    List<ProductionValueCalculationVO> selectProductionValueCalculationList(ProductionValueCalculationDTO dto);

	/**
	 * 根据IDS查询产值计算列表
	 * @param ids
	 * @return
	 */
    List<ProductionValueCalculationVO> selectProductionValueCalculationListByIds(@Param("ids") List<String> ids);


}