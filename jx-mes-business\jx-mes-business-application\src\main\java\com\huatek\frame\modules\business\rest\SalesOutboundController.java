package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.annotation.factory.BeanValidatorFactory;
import com.huatek.frame.common.utils.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.domain.SalesOutbound;
import com.huatek.frame.modules.business.service.SalesOutboundService;
import com.huatek.frame.modules.business.service.dto.SalesOutboundDTO;
import com.huatek.frame.modules.business.domain.vo.SalesOutboundVO;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-06
**/
@Api(tags = "销售出库管理")
@RestController
@RequestMapping("/api/salesOutbound")
public class SalesOutboundController {

	@Autowired
    private SalesOutboundService salesOutboundService;

	/**
	 * 销售出库列表
	 * 
	 * @param dto 销售出库DTO 实体对象
	 * @return
	 */
    @Log("销售出库列表")
    @ApiOperation(value = "销售出库列表查询")
    @PostMapping(value = "/salesOutboundList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("salesOutbound:list")
    public TorchResponse<List<SalesOutboundVO>> query(@RequestBody SalesOutboundDTO dto){
        return salesOutboundService.findSalesOutboundPage(dto);
    }

	/**
	 * 新增/修改销售出库
	 * 
	 * @param salesOutboundDto 销售出库DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改销售出库")
    @ApiOperation(value = "销售出库新增/修改操作")
    @PostMapping(value = "/salesOutbound", produces = { "application/json;charset=utf-8" })
    @TorchPerm("salesOutbound:add#salesOutbound:edit")
    public TorchResponse add(@RequestBody SalesOutboundDTO salesOutboundDto) throws Exception {
		 BeanValidatorFactory.validate(salesOutboundDto);
		return salesOutboundService.saveOrUpdate(salesOutboundDto);
	}

	/**
	 * 查询销售出库详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("销售出库详情")
    @ApiOperation(value = "销售出库详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("salesOutbound:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return salesOutboundService.findSalesOutbound(id);
	}

	/**
	 * 删除销售出库
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除销售出库")
    @ApiOperation(value = "销售出库删除操作")
    @TorchPerm("salesOutbound:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return salesOutboundService.delete(ids);
	}

    @ApiOperation(value = "销售出库联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return salesOutboundService.getOptionsList(id);
	}


    /**
     * 执行销售出库操作
     * @param dto 包含出库商品ID列表等参数
     * @return 出库结果
     */
    @Log("执行销售出库")
    @ApiOperation(value = "执行销售出库")
    @PostMapping(value = "/outbound", produces = { "application/json;charset=utf-8" })
    @TorchPerm("salesOutbound:outbound")
    public TorchResponse<String> outbound(@RequestBody SalesOutboundDTO dto) {
        return salesOutboundService.executeOutbound(dto);
    }


    @Log("销售出库导出")
    @ApiOperation(value = "销售出库导出")
    @TorchPerm("salesOutbound:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody SalesOutboundDTO dto)
    {
        List<SalesOutboundVO> list = salesOutboundService.selectSalesOutboundList(dto);
        ExcelUtil<SalesOutboundVO> util = new ExcelUtil<SalesOutboundVO>(SalesOutboundVO.class);
        util.exportExcel(response, list, "销售出库数据");
    }

    @Log("销售出库导入")
    @ApiOperation(value = "销售出库导入")
    @TorchPerm("salesOutbound:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<SalesOutboundVO> util = new ExcelUtil<SalesOutboundVO>(SalesOutboundVO.class);
        List<SalesOutboundVO> list = util.importExcel(file.getInputStream());
        return salesOutboundService.importSalesOutbound(list, unionColumns, true, "");
    }

    @Log("销售出库导入模板")
    @ApiOperation(value = "销售出库导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<SalesOutboundVO> util = new ExcelUtil<SalesOutboundVO>(SalesOutboundVO.class);
        util.importTemplateExcel(response, "销售出库数据");
    }

    @Log("根据Ids获取销售出库列表")
    @ApiOperation(value = "销售出库 根据Ids批量查询")
    @PostMapping(value = "/salesOutboundList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getSalesOutboundListByIds(@RequestBody List<String> ids) {
        return salesOutboundService.selectSalesOutboundListByIds(ids);
    }


}