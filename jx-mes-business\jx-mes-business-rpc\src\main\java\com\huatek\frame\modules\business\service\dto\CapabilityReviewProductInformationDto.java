package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
* @description 能力评审 设置产品资料 DTO实体类
* <AUTHOR>
* @date 2025-08-06
**/
@Data
@ApiModel("能力评审 设置产品资料 DTO实体类")
public class CapabilityReviewProductInformationDto extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
     * 产品资料
     **/
    @ApiModelProperty("产品资料")
    private String ProductInformation;

    /**
     * 产品资料编号
     **/
    @ApiModelProperty("产品资料编号")
    private String ProductInformationNumber;

    /**
     * 批量操作的ID列表
     **/
    @ApiModelProperty("批量操作的ID列表")
    private List<String> ids;

}