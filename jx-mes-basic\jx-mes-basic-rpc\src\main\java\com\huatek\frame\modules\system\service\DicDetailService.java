package com.huatek.frame.modules.system.service;

import java.util.List;
import java.util.Map;


import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.system.domain.DicDetail;
import com.huatek.frame.modules.system.domain.vo.DicDetailVO;
import com.huatek.frame.modules.system.service.dto.DicDetailDTO;

/**
 * 系统_字典明细 Service
 *
 * <AUTHOR>
 * @date 2018-7-11 14:03:46
 */
public interface DicDetailService {

	/**
	 * 分页查找查找 系统_字典明细
	 *
	 * @param dto 系统_字典明细dto
	 * @return
	 */
	TorchResponse<List<DicDetailVO>> findDicDetailPage(DicDetailDTO dto);

	/**
	 * 添加/修改 系统_字典明细
	 * 
	 * @param detail 系统_字典明细detail
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(DicDetail detail);

	/**
	 * 删除 系统_字典明细
	 * 
	 * @param ids 主键集合
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 根据字典编号获取字典明细
	 * @param dicCode
	 * @return
	 */
	TorchResponse findDicDetail(String dicCode);

    Map<String, String> selectDicDetailByDicCodeAndDetailName(String dicCode, String dName);

	Map<String, String> selectDicDetailByDicCodeAndDetailCode(String dicCode, String dCode);

	List<Map<String, String>>  findDicDetailByDicCode(String dcode);

}
