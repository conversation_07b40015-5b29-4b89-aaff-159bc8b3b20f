package com.huatek.frame.modules.business.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description 开始任务请求DTO实体类
 * <AUTHOR>
 * @date 2025-08-14
 **/
@Data
@ApiModel("开始任务请求DTO实体类")
public class StartTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 生产任务ID列表
     **/
    @ApiModelProperty("生产任务ID列表")
    private List<String> ids;
}
