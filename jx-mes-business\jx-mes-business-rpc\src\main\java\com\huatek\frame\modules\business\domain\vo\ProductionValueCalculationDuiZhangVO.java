package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description 产值计算VO实体类
 * <AUTHOR>
 * @date 2025-08-22
 **/
@Data
@ApiModel("产值计算 对账 VO实体类")
public class ProductionValueCalculationDuiZhangVO {


    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderNumber;


    /**
     * 对账价格
     **/
    @ApiModelProperty("对账价格")
    @Excel(name = "对账价格",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private BigDecimal settlementPrice;

    /**
     * 对账单号
     **/
    @ApiModelProperty("对账单号")
    @Excel(name = "对账单号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String billStatementNumber;
}
