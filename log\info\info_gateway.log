2025-09-10 09:30:46,416 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-10 09:30:46,427 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-10 09:30:46,590 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 09:30:47,953 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 09:31:06,316 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 09:31:06,338 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 09:31:08,972 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 09:31:08,973 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 09:31:08,990 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 09:31:08,990 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 09:31:09,833 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 09:41:47,410 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-09-10 09:41:48,182 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 09:41:56,365 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 09:41:56,637 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 09:41:56,718 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 09:41:59,549 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 09:41:59,553 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 09:41:59,559 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 09:41:59,561 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 09:41:59,561 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 09:42:09,483 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/completeProductionOrder
2025-09-10 09:42:09,494 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-10 09:42:09,640 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/orderNumber
2025-09-10 09:42:10,000 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/entrustedUnit
2025-09-10 09:42:10,100 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/productModel
2025-09-10 09:42:10,214 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-10 09:42:10,261 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 09:42:10,332 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-10 09:42:10,408 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-10 09:42:10,459 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-10 09:42:10,510 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-10 09:42:28,183 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-10 09:42:53,062 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-10 10:06:16,510 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-10 10:07:17,041 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-10 10:07:38,034 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-10 10:07:38,034 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-10 10:07:38,340 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 10:07:41,065 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-09-10 10:07:41,764 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 10:07:44,583 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 10:07:44,701 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 10:07:44,768 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 10:07:46,370 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:07:46,554 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:07:46,570 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:07:46,581 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:07:47,051 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 10:07:51,996 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 10:07:52,109 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 10:07:52,185 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 10:07:53,091 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 10:07:53,661 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:07:53,669 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:07:53,669 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 10:07:53,680 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:07:53,680 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:07:53,704 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 10:08:15,588 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/completeProductionOrder
2025-09-10 10:08:15,604 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-10 10:08:15,641 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/orderNumber
2025-09-10 10:08:15,703 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/entrustedUnit
2025-09-10 10:08:15,756 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/productModel
2025-09-10 10:08:15,806 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-10 10:08:15,868 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 10:08:15,968 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-10 10:08:16,033 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-10 10:08:16,082 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-10 10:08:16,153 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-10 10:08:18,500 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-10 10:08:28,725 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 10:08:28,727 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 10:08:28,729 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysUser
2025-09-10 10:08:28,735 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/users
2025-09-10 10:08:28,847 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 10:08:29,057 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 10:08:33,596 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1958834646877896706
2025-09-10 10:08:42,976 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-09-10 10:09:05,903 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-09-10 10:09:08,214 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menu/1958811375637766145
2025-09-10 10:09:31,083 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/updateRolePermission/1958811375637766145
2025-09-10 10:09:42,324 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-09-10 10:09:42,767 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 10:09:45,966 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 10:09:46,044 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 10:09:46,087 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 10:09:47,273 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:09:47,277 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 10:09:47,286 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:09:47,289 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:09:47,290 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 10:09:51,567 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/completeProductionOrder
2025-09-10 10:09:51,576 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-10 10:09:51,675 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/orderNumber
2025-09-10 10:09:51,757 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/entrustedUnit
2025-09-10 10:09:51,957 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/productModel
2025-09-10 10:09:52,045 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-10 10:09:52,111 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 10:09:52,160 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-10 10:09:52,204 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-10 10:09:52,237 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-10 10:09:52,352 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-10 10:09:54,423 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-10 10:12:32,766 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menu/1958811375637766145
2025-09-10 10:19:54,654 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-09-10 10:20:11,443 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-10 10:20:11,444 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-10 10:20:11,504 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 10:20:11,787 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 10:20:13,492 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 10:20:13,493 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-09-10 10:20:40,968 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/delete
2025-09-10 10:20:43,356 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-09-10 10:29:45,695 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:29:45,697 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:29:45,709 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/Abnormalfeedback
2025-09-10 10:29:45,710 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:29:45,710 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:29:45,738 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalfeedbackList
2025-09-10 10:29:45,824 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:29:45,828 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:29:45,828 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 10:29:45,840 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:29:45,985 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:29:46,333 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:29:46,531 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:29:46,591 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:29:46,638 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:29:46,682 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:29:46,723 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:30:27,697 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:30:27,698 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:30:27,702 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 10:30:27,706 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:30:27,726 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:30:27,737 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:30:27,829 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:30:27,829 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-10 10:30:27,830 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:30:27,830 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-10 10:30:28,036 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-10 10:30:28,385 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-10 10:30:28,532 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 10:30:28,714 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-10 10:30:28,767 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-10 10:30:55,928 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findDepartById/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-10 10:31:18,827 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/abnormalfeedback
2025-09-10 10:33:49,649 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:33:49,650 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:33:49,676 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalfeedbackList
2025-09-10 10:33:49,738 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 10:33:49,738 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:33:49,738 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:33:49,837 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:33:49,875 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:33:49,917 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/Abnormalfeedback
2025-09-10 10:33:50,043 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:33:50,241 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:33:50,444 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:33:50,530 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:33:50,603 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:33:50,655 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:33:50,697 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:33:50,735 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:35:00,760 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:35:00,770 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:35:00,793 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 10:35:00,802 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:35:00,805 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:35:00,822 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:35:00,904 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:35:00,906 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:35:00,919 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-10 10:35:00,930 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-10 10:35:01,065 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-10 10:35:01,355 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-10 10:35:01,478 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 10:35:01,579 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-10 10:35:01,706 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-10 10:35:09,972 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findDepartById/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-10 10:35:18,598 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/abnormalfeedback
2025-09-10 10:35:19,029 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:36:02,297 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:36:02,307 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:36:02,313 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 10:36:02,322 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:36:02,323 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:36:02,334 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalfeedbackList
2025-09-10 10:36:02,418 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:36:02,429 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:36:02,469 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/Abnormalfeedback
2025-09-10 10:36:02,625 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:36:02,848 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:36:03,156 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:36:03,236 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:36:03,294 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:36:03,360 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:36:03,417 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:36:03,460 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:37:42,901 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:37:42,903 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:37:42,906 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 10:37:42,933 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:37:42,950 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:37:42,952 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:37:43,011 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:37:43,039 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:37:43,045 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-10 10:37:43,063 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-10 10:37:43,602 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-10 10:37:43,760 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-10 10:37:43,864 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 10:37:43,932 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-10 10:37:43,990 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-10 10:37:55,315 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-10 10:37:55,328 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:37:55,389 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-10 10:37:55,441 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-10 10:37:55,702 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 10:37:55,762 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-10 10:37:55,804 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-10 10:38:02,299 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/getInfo/S202509853
2025-09-10 10:38:11,714 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findDepartById/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-10 10:38:20,572 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findDepartById/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-10 10:38:35,147 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/abnormalfeedback
2025-09-10 10:38:35,708 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:38:40,657 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:38:40,666 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:38:40,671 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 10:38:40,702 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:38:40,702 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:38:40,724 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalfeedbackList
2025-09-10 10:38:40,821 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:38:40,823 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:38:40,824 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/Abnormalfeedback
2025-09-10 10:38:40,999 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:38:41,063 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:38:41,392 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:38:41,600 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:38:41,667 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:38:41,725 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:38:41,765 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:38:41,959 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:47:20,289 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:47:20,302 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 10:47:20,302 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 10:47:20,302 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 10:47:20,303 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 10:47:20,308 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:47:20,404 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 10:47:20,438 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 10:47:20,460 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-10 10:47:20,461 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-10 10:47:20,748 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-10 10:47:21,083 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-10 10:47:21,162 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 10:47:21,380 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-10 10:47:21,557 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-10 10:47:23,696 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-10 10:47:23,708 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:47:23,778 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-10 10:47:23,824 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-10 10:47:23,863 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 10:47:23,919 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-10 10:47:23,965 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-10 10:47:34,470 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:47:36,279 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:51:08,743 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 10:54:52,506 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EvaluationOrder
2025-09-10 10:54:52,515 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/evaluationOrderList
2025-09-10 10:54:52,657 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_orderType
2025-09-10 10:54:52,824 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-10 10:54:53,475 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-10 10:54:53,555 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/customerInformationManagement/getMarketUsers
2025-09-10 10:54:53,674 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_urgencyLevel
2025-09-10 10:54:53,726 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_status
2025-09-10 10:54:53,793 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 10:54:53,850 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-10 10:55:48,912 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:55:48,913 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EvaluationOrder
2025-09-10 10:55:48,917 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductList
2025-09-10 10:55:48,927 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/detail/1965602610591494146
2025-09-10 10:55:49,099 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_orderType
2025-09-10 10:55:49,104 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/optionsList/standardSpecificationNumber
2025-09-10 10:55:49,192 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-10 10:55:49,531 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-10 10:55:49,807 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-10 10:55:49,810 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-10 10:55:49,877 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 10:55:49,969 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/otherContacts/otherContactsList/1948218882877730817
2025-09-10 10:55:50,001 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_specialAnalysisTestProject
2025-09-10 10:55:50,055 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_groupType
2025-09-10 10:55:50,223 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/customerInformationManagement/getMarketUsers
2025-09-10 10:55:50,268 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_inspectionTestProject
2025-09-10 10:55:50,312 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_urgencyLevel
2025-09-10 10:55:50,345 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_qualityConsistencyTestItems
2025-09-10 10:55:50,428 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportRequirements
2025-09-10 10:55:50,465 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_dpaTestProject
2025-09-10 10:55:50,516 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportFormat
2025-09-10 10:55:50,548 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_status
2025-09-10 10:55:50,622 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataFormat
2025-09-10 10:55:50,831 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqERep
2025-09-10 10:55:50,942 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqsPapereport
2025-09-10 10:55:51,027 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-10 10:55:51,112 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_status
2025-09-10 10:55:51,168 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-10 10:55:54,610 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EvaluationOrder
2025-09-10 10:55:54,620 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/evaluationOrderList
2025-09-10 10:55:54,738 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_orderType
2025-09-10 10:55:54,872 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-10 10:55:55,162 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-10 10:55:55,577 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/customerInformationManagement/getMarketUsers
2025-09-10 10:55:55,706 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_urgencyLevel
2025-09-10 10:55:55,801 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_status
2025-09-10 10:55:55,949 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 10:55:57,475 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-10 10:56:04,432 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 10:56:04,434 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EvaluationOrder
2025-09-10 10:56:04,451 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductList
2025-09-10 10:56:04,452 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/detail/1965286295586648066
2025-09-10 10:56:04,646 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/optionsList/standardSpecificationNumber
2025-09-10 10:56:04,685 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_orderType
2025-09-10 10:56:04,763 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-10 10:56:04,822 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-10 10:56:05,034 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-10 10:56:05,046 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 10:56:05,145 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_specialAnalysisTestProject
2025-09-10 10:56:05,159 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/evaluationOrder/optionsList/entrustedUnit
2025-09-10 10:56:05,299 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_groupType
2025-09-10 10:56:05,786 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/otherContacts/otherContactsList/1948218882877730817
2025-09-10 10:56:05,788 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_inspectionTestProject
2025-09-10 10:56:05,843 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_qualityConsistencyTestItems
2025-09-10 10:56:06,050 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/customerInformationManagement/getMarketUsers
2025-09-10 10:56:06,118 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list1_dpaTestProject
2025-09-10 10:56:06,229 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_status
2025-09-10 10:56:06,278 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_urgencyLevel
2025-09-10 10:56:06,499 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportRequirements
2025-09-10 10:56:06,648 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportFormat
2025-09-10 10:56:06,771 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataFormat
2025-09-10 10:56:06,869 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqERep
2025-09-10 10:56:06,945 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_dataReqsPapereport
2025-09-10 10:56:07,070 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-10 10:56:07,149 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_status
2025-09-10 10:56:07,214 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-10 10:56:13,763 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 10:56:13,763 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-10 10:56:17,637 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-10 10:56:17,648 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductManagement
2025-09-10 10:56:17,658 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/productManagementList
2025-09-10 10:56:17,715 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productManagement/optionsList/productCategoryName
2025-09-10 10:56:31,630 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_status
2025-09-10 10:56:31,634 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_specificationType
2025-09-10 10:56:31,637 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardSpecification/optionsList/entrustedUnit
2025-09-10 10:56:31,639 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/StandardSpecification
2025-09-10 10:56:31,653 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardSpecification/standardSpecificationList
2025-09-10 10:56:31,735 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_specificationType
2025-09-10 10:56:31,793 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_specification_status
2025-09-10 10:56:35,950 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/add
2025-09-10 10:56:36,642 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/productListList
2025-09-10 10:57:14,848 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productList/batchUpdate
2025-09-10 11:11:45,333 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 11:11:45,337 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 11:11:45,361 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 11:11:45,427 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 11:11:45,428 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 11:11:45,433 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 11:11:45,520 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 11:11:45,591 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-10 11:11:45,594 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 11:11:45,606 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-10 11:11:45,899 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-10 11:11:46,038 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-10 11:11:46,123 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 11:11:46,367 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-10 11:11:46,463 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-10 11:12:50,491 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-10 11:12:50,501 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 11:12:50,556 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-10 11:12:50,606 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-10 11:12:50,652 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 11:12:50,714 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-10 11:12:50,774 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-10 11:13:00,038 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/getInfo/S202509853
2025-09-10 11:13:02,007 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findDepartById/1943617311021862912
2025-09-10 11:13:08,553 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findDepartById/1943617311021862912
2025-09-10 11:13:13,901 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/file/uploadFile
2025-09-10 11:13:15,239 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/abnormalfeedback
2025-09-10 11:13:16,574 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-10 11:13:19,526 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 11:13:19,533 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 11:13:19,553 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 11:13:19,556 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 11:13:19,562 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 11:13:19,575 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalfeedbackList
2025-09-10 11:13:19,624 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 11:13:19,658 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/Abnormalfeedback
2025-09-10 11:13:19,658 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 11:13:19,678 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 11:13:19,787 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 11:13:20,046 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 11:13:20,280 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 11:13:20,387 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 11:13:20,495 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 11:13:20,543 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 11:13:20,579 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 11:19:30,415 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 11:19:30,429 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 11:19:30,432 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 11:19:30,433 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 11:19:30,432 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 11:19:30,432 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 11:19:30,481 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 11:19:30,517 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 11:19:30,621 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 11:19:30,779 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 11:19:30,932 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 11:19:30,969 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 11:19:31,018 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 11:19:31,043 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 11:19:31,075 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 11:19:31,574 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 11:19:36,455 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/getInfo/S202509419
2025-09-10 11:19:38,855 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findDepartById/1960651958186586114
2025-09-10 11:19:41,118 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findDepartById/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-10 11:19:50,456 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/file/uploadFile
2025-09-10 11:19:52,708 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/abnormalfeedback
2025-09-10 11:19:54,969 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1965607106566615041
2025-09-10 11:19:55,422 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 11:19:55,431 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 11:19:55,435 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1965607106566615041
2025-09-10 11:19:55,453 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 11:19:55,456 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-10 11:19:55,457 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 11:19:55,513 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 11:19:55,545 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 11:19:55,548 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 11:19:55,559 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 11:19:55,614 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 11:19:55,629 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-10 11:19:55,630 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 11:19:55,630 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 11:19:55,770 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 11:19:55,773 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 11:19:55,844 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1965607106566615041
2025-09-10 11:27:03,871 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 11:27:21,238 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 11:27:25,118 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 13:43:39,107 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-10 13:43:39,115 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-10 13:43:39,120 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 13:43:39,366 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 13:43:43,037 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 13:43:43,066 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 13:43:43,072 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 13:43:43,087 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 13:43:43,090 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 13:43:43,090 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 13:43:43,131 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 13:43:43,185 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 13:43:43,190 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 13:43:43,358 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 13:43:43,427 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 13:43:43,479 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 13:43:43,531 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 13:43:43,590 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 13:43:43,634 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 13:43:43,705 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 13:43:44,351 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:10:13,668 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysGroup
2025-09-10 14:10:13,676 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/groups
2025-09-10 14:30:59,506 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 14:30:59,506 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 14:30:59,506 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 14:30:59,506 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 14:30:59,506 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 14:30:59,506 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 14:31:00,897 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 14:31:00,897 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 14:31:00,953 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 14:31:00,993 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 14:31:02,842 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 14:31:02,889 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 14:31:03,043 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 14:31:03,112 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 14:31:03,165 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 14:31:03,756 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:33:02,246 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:33:05,404 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:33:21,293 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysGroup
2025-09-10 14:33:21,302 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/groups
2025-09-10 14:44:30,094 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 14:44:30,095 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-10 14:44:30,106 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-10 14:44:36,311 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 14:44:42,839 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysGroup
2025-09-10 14:44:42,849 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/groups
2025-09-10 14:44:42,852 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 14:44:52,906 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 14:44:52,917 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/users
2025-09-10 14:44:52,932 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 14:44:52,933 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysUser
2025-09-10 14:44:53,310 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 14:44:53,530 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 14:45:07,517 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-10 14:45:07,517 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-10 14:45:08,096 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 14:45:14,586 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 14:45:15,180 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 14:45:15,539 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 14:45:17,518 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1958834646877896706
2025-09-10 14:45:18,105 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 14:45:18,120 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 14:45:18,120 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 14:45:18,120 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 14:45:18,122 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 14:45:27,618 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/updateUserRole/1958834646877896706
2025-09-10 14:45:33,170 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-09-10 14:45:33,537 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 14:45:38,097 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 14:45:38,304 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 14:45:38,387 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 14:45:41,744 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 14:45:41,810 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 14:45:41,817 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 14:45:41,930 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 14:45:41,948 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 14:45:48,302 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 14:45:48,307 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 14:45:48,308 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 14:45:48,307 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 14:45:48,308 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 14:45:48,308 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 14:45:48,371 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 14:45:48,401 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 14:45:48,486 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 14:45:48,532 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 14:45:48,806 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 14:45:48,850 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 14:45:48,938 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 14:45:48,981 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 14:45:49,032 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 14:45:49,290 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:46:48,915 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:46:53,392 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:47:04,820 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:47:18,187 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:51:13,577 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:51:34,574 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:51:51,243 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 14:51:51,249 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 14:51:51,250 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 14:51:51,265 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 14:51:51,267 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 14:51:51,267 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 14:51:51,296 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 14:51:51,370 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 14:51:51,518 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 14:51:51,608 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 14:51:51,914 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 14:51:51,984 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 14:51:52,071 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 14:51:52,122 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 14:51:52,175 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 14:51:52,866 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:52:42,652 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 14:52:42,664 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/users
2025-09-10 14:52:42,680 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 14:52:42,684 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysUser
2025-09-10 14:52:42,885 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 14:52:43,227 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 14:52:50,106 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 14:52:50,110 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 14:52:50,112 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 14:52:50,119 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 14:52:50,132 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 14:52:50,141 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 14:52:50,287 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 14:52:50,367 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 14:52:50,812 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 14:52:50,905 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 14:52:50,985 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 14:52:51,019 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 14:52:51,078 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 14:52:51,116 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 14:52:51,179 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 14:52:51,885 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:53:05,835 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 14:53:10,152 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:01:44,988 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:02:11,364 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 15:02:11,365 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysUser
2025-09-10 15:02:11,365 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 15:02:11,379 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/users
2025-09-10 15:02:11,550 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 15:02:12,033 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 15:02:22,836 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:02:28,169 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1958834646877896706
2025-09-10 15:02:39,567 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/updateUserRole/1958834646877896706
2025-09-10 15:02:44,443 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-09-10 15:02:44,959 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 15:02:48,679 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 15:02:49,041 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 15:02:49,120 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 15:02:50,727 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:02:50,734 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 15:02:50,741 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:02:50,742 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:02:50,743 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:03:05,874 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 15:03:05,875 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 15:03:05,875 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 15:03:05,876 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 15:03:05,890 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 15:03:05,895 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 15:03:05,945 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 15:03:05,976 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 15:03:06,147 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 15:03:06,285 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 15:03:06,342 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 15:03:06,381 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 15:03:06,452 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 15:03:06,479 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 15:03:06,509 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 15:03:06,769 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:03:30,230 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:03:54,211 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1958834646877896706
2025-09-10 15:04:06,402 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:04:29,758 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-09-10 15:04:30,019 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 15:04:35,156 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 15:04:35,276 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 15:04:35,331 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 15:04:36,596 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:04:36,604 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 15:04:36,605 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:04:36,662 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:04:36,714 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:04:42,680 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 15:04:42,688 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 15:04:42,688 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 15:04:42,688 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 15:04:42,688 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 15:04:42,692 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 15:04:42,774 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 15:04:42,791 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 15:04:43,335 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 15:04:43,596 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 15:04:43,675 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 15:04:43,741 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 15:04:43,807 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 15:04:43,841 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 15:04:43,906 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 15:04:44,955 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:05:26,291 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:11:51,030 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:12:03,900 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:12:24,419 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/updateUserRole/1958834646877896706
2025-09-10 15:12:30,985 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:12:41,506 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:12:57,845 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-09-10 15:12:58,105 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 15:13:02,688 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1958834646877896706
2025-09-10 15:13:11,338 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/users
2025-09-10 15:13:13,863 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1958834646877896706
2025-09-10 15:13:21,611 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/updateUserRole/1958834646877896706
2025-09-10 15:13:26,645 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1960990905957851138
2025-09-10 15:13:42,178 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1943617311017668608
2025-09-10 15:13:57,492 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1960990905957851138
2025-09-10 15:13:59,948 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/detail/1960990905957851138
2025-09-10 15:14:12,794 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/detail/1960990905957851138
2025-09-10 15:14:30,525 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/findRoles/1960990905957851138
2025-09-10 15:14:38,379 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 15:14:38,379 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 15:14:38,388 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 15:14:38,389 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 15:14:38,389 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 15:14:38,407 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 15:14:38,462 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 15:14:38,473 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 15:14:38,627 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 15:14:38,940 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 15:14:39,100 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 15:14:39,178 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 15:14:39,278 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 15:14:39,330 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 15:14:39,386 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 15:14:40,181 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:15:01,281 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:15:05,210 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:15:44,695 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:15:46,526 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:15:47,211 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:15:49,893 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:16:16,095 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-10 15:16:16,105 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-10 15:16:16,155 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 15:16:16,316 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 15:16:22,596 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/users
2025-09-10 15:16:22,597 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 15:16:22,637 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 15:16:22,638 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 15:16:22,638 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 15:16:22,652 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 15:16:22,744 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 15:16:22,744 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 15:16:22,745 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 15:16:22,771 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 15:16:22,794 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 15:16:22,804 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 15:16:22,913 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysUser
2025-09-10 15:16:22,929 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 15:16:22,979 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 15:16:23,064 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 15:16:23,133 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 15:16:23,154 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 15:16:23,452 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 15:16:23,533 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 15:16:23,540 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 15:16:23,593 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 15:16:24,259 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:16:31,440 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/detail/1960990905957851138
2025-09-10 15:16:44,297 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:16:46,447 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:17:02,168 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 15:17:02,169 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 15:17:02,169 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 15:17:02,169 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 15:17:02,169 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 15:17:02,169 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 15:17:02,269 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 15:17:02,270 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 15:17:02,491 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 15:17:02,612 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 15:17:02,687 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 15:17:02,834 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 15:17:02,883 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 15:17:02,924 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 15:17:02,955 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 15:17:03,343 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:17:07,517 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:18:52,766 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 15:18:52,869 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-10 15:18:56,499 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-10 15:18:56,589 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-10 15:18:56,639 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-10 15:18:57,975 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:18:57,989 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:18:57,989 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-10 15:18:57,989 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:18:57,990 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-10 15:19:03,468 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 15:19:03,472 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 15:19:03,474 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 15:19:03,475 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 15:19:03,475 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 15:19:03,475 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 15:19:03,518 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 15:19:03,583 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 15:19:03,668 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 15:19:03,716 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 15:19:03,866 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 15:19:03,908 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 15:19:03,987 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 15:19:04,034 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 15:19:04,075 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 15:19:04,430 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:19:06,655 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:27:11,627 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysUser
2025-09-10 15:27:11,627 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 15:27:11,627 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 15:27:11,635 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/users
2025-09-10 15:27:11,829 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-09-10 15:27:12,193 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-10 15:27:15,858 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/detail/1958834646877896706
2025-09-10 15:57:45,648 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 15:57:45,666 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 15:57:45,666 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 15:57:45,666 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 15:57:45,666 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 15:57:45,666 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 15:57:45,732 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 15:57:45,761 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 15:57:45,882 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 15:57:46,310 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 15:57:46,416 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 15:57:46,466 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 15:57:46,532 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 15:57:46,580 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 15:57:46,621 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 15:57:47,039 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:57:55,936 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1965679523271950338
2025-09-10 15:57:56,972 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:57:56,980 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 15:57:56,980 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 15:57:56,981 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 15:57:56,984 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1965679523271950338
2025-09-10 15:57:56,984 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:57:57,057 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 15:57:57,063 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 15:57:57,069 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 15:57:57,149 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-10 15:57:57,150 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-10 15:57:57,150 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 15:57:57,303 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1965679523271950338
2025-09-10 15:57:57,403 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:57:57,420 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:57:57,468 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:57:57,486 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:58:08,327 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:58:13,700 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 15:58:25,506 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964969263234199553
2025-09-10 15:58:25,979 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:58:25,983 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:58:25,989 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-10 15:58:25,989 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 15:58:25,989 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 15:58:25,990 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964969263234199553
2025-09-10 15:58:26,178 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 15:58:26,181 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 15:58:26,198 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 15:58:26,199 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:58:26,200 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:58:26,213 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964969263234199553
2025-09-10 15:58:26,279 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:58:26,321 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 15:59:24,409 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-09-10 15:59:24,415 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-09-10 15:59:24,429 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-09-10 15:59:24,433 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceType
2025-09-10 15:59:24,450 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 15:59:24,521 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EquipmentInventory
2025-09-10 15:59:24,755 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 15:59:24,876 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 15:59:25,025 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-09-10 15:59:25,232 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-09-10 15:59:25,325 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-10 15:59:25,363 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 15:59:25,395 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 15:59:25,431 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 15:59:25,474 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/trace_information_d0
2025-09-10 15:59:45,282 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1965601284558090242
2025-09-10 15:59:45,382 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-09-10 15:59:45,737 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-10 15:59:45,751 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 15:59:45,751 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 15:59:45,752 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 15:59:45,753 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-10 15:59:45,794 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-10 15:59:45,799 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 15:59:45,800 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-10 15:59:45,825 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 15:59:45,825 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 16:00:03,005 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1965341293158768642
2025-09-10 16:00:03,058 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-09-10 16:00:05,094 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-10 16:00:05,098 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 16:00:05,349 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 16:00:05,355 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 16:00:05,401 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-09-10 16:00:05,902 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-10 16:00:06,110 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 16:00:06,110 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-10 16:00:06,117 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 16:00:06,126 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-10 16:00:06,131 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 16:08:31,037 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 16:08:31,037 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 16:08:31,038 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 16:08:31,040 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 16:08:31,041 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 16:08:31,041 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 16:08:31,318 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 16:08:31,319 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 16:08:31,576 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 16:08:31,766 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 16:08:31,826 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 16:08:31,864 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 16:08:31,934 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 16:08:31,980 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 16:08:32,018 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 16:08:32,342 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 16:08:38,056 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 16:08:39,511 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 16:09:27,233 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964969263234199553
2025-09-10 16:09:27,723 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:09:27,725 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:09:27,726 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-10 16:09:27,728 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 16:09:27,729 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964969263234199553
2025-09-10 16:09:27,731 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 16:09:27,810 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 16:09:27,828 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 16:09:27,831 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 16:09:27,831 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:09:27,844 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:09:27,893 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964969263234199553
2025-09-10 16:09:27,902 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:09:28,056 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:11:39,958 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-09-10 16:11:39,989 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 16:11:39,992 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 16:11:39,994 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceType
2025-09-10 16:11:39,994 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-09-10 16:11:39,994 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-09-10 16:11:40,118 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 16:11:40,123 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EquipmentInventory
2025-09-10 16:11:40,550 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-09-10 16:11:40,800 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-09-10 16:11:41,667 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-10 16:11:43,211 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 16:11:43,293 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 16:11:43,342 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 16:11:43,407 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/trace_information_d0
2025-09-10 16:11:47,200 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1965341293158768642
2025-09-10 16:11:47,273 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-09-10 16:11:47,821 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-10 16:11:47,829 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 16:11:47,829 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-10 16:11:47,832 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 16:11:47,840 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 16:11:47,866 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-09-10 16:11:47,947 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-10 16:11:47,950 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-10 16:11:47,985 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-10 16:11:48,032 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-10 16:11:48,032 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-10 16:13:52,759 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 16:13:52,761 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 16:13:52,763 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-10 16:13:52,765 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 16:13:52,767 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 16:13:52,783 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 16:13:52,828 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-10 16:13:52,851 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-10 16:13:53,027 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-10 16:13:53,193 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-10 16:13:53,327 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-10 16:13:53,389 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-10 16:13:53,443 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-10 16:13:53,485 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-10 16:13:53,518 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-10 16:13:53,884 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 16:13:58,320 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 16:13:59,991 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 16:14:38,330 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 16:14:39,828 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-10 16:14:41,579 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1964969263234199553
2025-09-10 16:14:42,091 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:14:42,096 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:14:42,103 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-10 16:14:42,103 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1964969263234199553
2025-09-10 16:14:42,117 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-10 16:14:42,118 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-10 16:14:42,193 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-10 16:14:42,195 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-10 16:14:42,218 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-10 16:14:42,228 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:14:42,232 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:14:42,302 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1964969263234199553
2025-09-10 16:14:42,362 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-10 16:14:42,362 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
