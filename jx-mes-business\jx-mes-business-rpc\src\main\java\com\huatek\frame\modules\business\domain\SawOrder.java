package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 监制验收工单
* <AUTHOR>
* @date 2025-08-20
**/
@Setter
@Getter
@TableName("saw_order")
public class SawOrder implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private String orderId;

    /**
     * 产品列表id
     */
    @TableField(value = "product_list_id")
    private String productListId;
    
    /**
	 * 工单编号
     **/
    @TableField(value = "work_order_number"
    )
    private String workOrderNumber;

    
    /**
	 * 验收类型
     **/
    @TableField(value = "acceptance_type"
    )
    private String acceptanceType;

    
    /**
	 * 负责人
     **/
    @TableField(value = "responsible_person"
    )
    private String responsiblePerson;

    
    /**
	 * 状态
     **/
    @TableField(value = "status"
    )
    private String status;

    
    /**
	 * 完成日期
     **/
    @TableField(value = "completion_date"
    )
    private Date completionDate;

    
    /**
	 * 备注
     **/
    @TableField(value = "`comment`"
    )
    private String comment;

    
    /**
	 * 上传附件
     **/
    @TableField(value = "attachment"
    )
    private String attachment;

    
    /**
	 * 子表明细项ID
     **/
    @TableField(value = "codex_torch_detail_item_ids"
    )
    private String codexTorchDetailItemIds;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}