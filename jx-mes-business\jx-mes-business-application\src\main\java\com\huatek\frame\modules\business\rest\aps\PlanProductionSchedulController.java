package com.huatek.frame.modules.business.rest.aps;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 排产计划
 */

@Api(tags = "排产计划")
@RestController
@RequestMapping("/api/Schedul")
public class PlanProductionSchedulController {

    @Autowired
    private HttpClientUtil httpClientUtil;

    /**
     * 获取生产计划分页信息
     */
    @Log("获取生产计划分页信息")
    @ApiOperation(value = "获取生产计划分页信息")
    @PostMapping(value = "/page", produces = { "application/json;charset=utf-8" })
    public Object GetPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/page");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取生产计划详情信息
     */
    @Log("获取生产计划详情信息")
    @ApiOperation(value = "获取生产计划详情信息")
    @PostMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    public Object Getdetails(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/Schedul/detail/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 按排产编码获取设备甘特图
     */
    @Log("按排产编码获取设备甘特图")
    @ApiOperation(value = "按排产编码获取设备甘特图")
    @PostMapping(value = "/gantt/device/{scheduleCode}", produces = { "application/json;charset=utf-8" })
    public Object GetDeviceGantt(@PathVariable String scheduleCode, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/Schedul/gantt/device/" + scheduleCode);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取产品排产计划甘特图
     */
    @Log("获取产品排产计划甘特图")
    @ApiOperation(value = "获取产品排产计划甘特图")
    @PostMapping(value = "/gantt/productplan/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetGanttByProduct(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/Schedul/gantt/productplan/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取产品订单排产计划甘特图
     */
    @Log("获取产品订单排产计划甘特图")
    @ApiOperation(value = "获取产品订单排产计划甘特图")
    @PostMapping(value = "/gantt/orderplan", produces = { "application/json;charset=utf-8" })
    public Object GetGanttByOrderProductNumber(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/gantt/orderplan");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 按计划编码获取生产计划甘特图
     */
    @Log("获取订单产品已排产计划甘特图")
    @ApiOperation(value = "获取订单产品已排产计划甘特图")
    @PostMapping(value = "/gantt/orderproductplan", produces = { "application/json;charset=utf-8" })
    public Object GetGanttByPlanCodeNumber(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/gantt/orderproductplan");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 按计划编码获取生产计划甘特图
     */
    @Log("按计划编码获取生产计划甘特图")
    @ApiOperation(value = "按计划编码获取生产计划甘特图")
    @PostMapping(value = "/gantt/plan", produces = { "application/json;charset=utf-8" })
    public Object GetGanttByPlanCodeNumber(@RequestParam String planCode, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(planCode);
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/Schedul/gantt/plan");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取物料计划信息
     */
    @Log("获取物料计划信息")
    @ApiOperation(value = "获取物料计划信息")
    @PostMapping(value = "/getmaterials", produces = { "application/json;charset=utf-8" })
    public Object GetMaterials(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/getmaterials");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取人员计划信息
     */
    @Log("获取人员计划信息")
    @ApiOperation(value = "获取人员计划信息")
    @PostMapping(value = "/getpersonnels", produces = { "application/json;charset=utf-8" })
    public Object GetPersonnels(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/getpersonnels");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 新增排产计划
     */
    @Log("新增排产计划")
    @ApiOperation(value = "新增排产计划")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    public Object CreateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/add");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 批量删除排产计划
     */
    @Log("批量删除排产计划")
    @ApiOperation(value = "批量删除排产计划")
    @PostMapping(value = "/batchDel", produces = { "application/json;charset=utf-8" })
    public Object DeleteAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/batchDel");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 重排
     */
    @Log("重排")
    @ApiOperation(value = "重排")
    @PostMapping(value = "/reschedulingproduction", produces = { "application/json;charset=utf-8" })
    public Object RealoadSchedulAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/reschedulingproduction");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     *保存排产
     */
    @Log("保存排产")
    @ApiOperation(value = "保存排产")
    @PostMapping(value = "/saveSchedulingProduction", produces = { "application/json;charset=utf-8" })
    public Object SaveSchedulingProduction(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/saveSchedulingProduction");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 修改排产状态
     */
    @Log("修改排产状态")
    @ApiOperation(value = "修改排产状态")
    @PostMapping(value = "/setScheduleStatus", produces = { "application/json;charset=utf-8" })
    public Object setScheduleStatus(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/setScheduleStatus");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 查询排产工序方案
     */
    @Log("查询排产工序方案")
    @ApiOperation(value = "查询排产工序方案")
    @PostMapping(value = "/stepsolution/{code}", produces = { "application/json;charset=utf-8" })
    public Object GetStepSolution(@PathVariable String code, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/Schedul/stepsolution/" + code);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     *分页查询物料预警
     */
    @Log("分页查询物料预警")
    @ApiOperation(value = "分页查询物料预警")
    @PostMapping(value = "/material", produces = { "application/json;charset=utf-8" })
    public Object GetMaterialPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/material");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取物料表
     */
    @Log("获取物料表")
    @ApiOperation(value = "获取物料表")
    @PostMapping(value = "/materialchart", produces = { "application/json;charset=utf-8" })
    public Object GetChartAsync(@RequestParam String Mcode, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(Mcode);
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/Schedul/materialchart");
        return httpClientUtil.callMes(request, inputParamDto);
    }

}
