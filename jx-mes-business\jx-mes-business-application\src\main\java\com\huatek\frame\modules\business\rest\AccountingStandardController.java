package com.huatek.frame.modules.business.rest;

import cn.hutool.core.lang.UUID;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.vo.AccountingStandardVO;
import com.huatek.frame.modules.business.domain.vo.CcCategorizationCodeVO;
import com.huatek.frame.modules.business.service.AccountingStandardService;
import com.huatek.frame.modules.business.service.CcCategorizationCodeService;
import com.huatek.frame.modules.business.service.dto.AccountingStandardDTO;
import com.huatek.frame.modules.business.service.dto.CcCategorizationCodeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-22
**/
@Api(tags = "核算标准管理")
@RestController
@RequestMapping("/api/accountingStandard")
public class AccountingStandardController {

	@Autowired
    private AccountingStandardService accountingStandardService;
    @Autowired
    private CcCategorizationCodeService ccCategorizationCodeService;

	/**
	 * 核算标准列表
	 * 
	 * @param dto 核算标准DTO 实体对象
	 * @return
	 */
    @Log("核算标准列表")
    @ApiOperation(value = "核算标准列表查询")
    @PostMapping(value = "/accountingStandardList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("accountingStandard:list")
    public TorchResponse<List<AccountingStandardVO>> query(@RequestBody AccountingStandardDTO dto){
        return accountingStandardService.findAccountingStandardPage(dto);
    }

	/**
	 * 新增/修改核算标准
	 * 
	 * @param accountingStandardDto 核算标准DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改核算标准")
    @ApiOperation(value = "核算标准新增/修改操作")
    @PostMapping(value = "/accountingStandard", produces = { "application/json;charset=utf-8" })
    @TorchPerm("accountingStandard:add#accountingStandard:edit")
    public TorchResponse add(@RequestBody AccountingStandardDTO accountingStandardDto) throws Exception {
		// BeanValidatorFactory.validate(accountingStandardDto);
		return accountingStandardService.saveOrUpdate(accountingStandardDto);
	}

	/**
	 * 查询核算标准详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("核算标准详情")
    @ApiOperation(value = "核算标准详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("accountingStandard:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return accountingStandardService.findAccountingStandard(id);
	}

	/**
	 * 删除核算标准
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除核算标准")
    @ApiOperation(value = "核算标准删除操作")
    @TorchPerm("accountingStandard:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return accountingStandardService.delete(ids);
	}

    @ApiOperation(value = "核算标准联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return accountingStandardService.getOptionsList(id);
	}





    @Log("核算标准导出")
    @ApiOperation(value = "核算标准导出")
    @TorchPerm("accountingStandard:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody AccountingStandardDTO dto)
    {
        List<AccountingStandardVO> list = accountingStandardService.selectAccountingStandardList(dto);
        ExcelUtil<AccountingStandardVO> util = new ExcelUtil<AccountingStandardVO>(AccountingStandardVO.class);
        util.exportExcel(response, list, "核算标准数据");
    }

    @Log("核算标准导入")
    @ApiOperation(value = "核算标准导入")
    @TorchPerm("accountingStandard:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<AccountingStandardVO> util = new ExcelUtil<AccountingStandardVO>(AccountingStandardVO.class);
        List<AccountingStandardVO> list = util.importExcel(file.getInputStream());
        return accountingStandardService.importAccountingStandard(list, unionColumns, true, "");
    }

    @Log("核算标准导入模板")
    @ApiOperation(value = "核算标准导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<AccountingStandardVO> util = new ExcelUtil<AccountingStandardVO>(AccountingStandardVO.class);
        util.importTemplateExcel(response, "核算标准数据");
    }

    @Log("根据Ids获取核算标准列表")
    @ApiOperation(value = "核算标准 根据Ids批量查询")
    @PostMapping(value = "/accountingStandardList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getAccountingStandardListByIds(@RequestBody List<String> ids) {
        return accountingStandardService.selectAccountingStandardListByIds(ids);
    }

    /**
     * 核算标准主子表单组合提交
     *
	 * @param accountingStandardDto 核算标准DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Log("核算标准主子表单组合提交")
    @ApiOperation(value = "核算标准主子表单组合提交")
    //@TorchPerm("accountingStandard:masterDetailSubmit")
    @PostMapping(value = "/masterDetailSubmit", produces = {"application/json;charset=utf-8"})
    public TorchResponse submitMasterDetails(@RequestBody AccountingStandardDTO accountingStandardDto) {
        return accountingStandardService.submitMasterDetails(accountingStandardDto);
    }
    @Log("生成id")
    @ApiOperation(value = "生成id")
    @PostMapping(value = "/generateId", produces = {"application/json;charset=utf-8"})
    public TorchResponse generateId(){
        String uuid = HuatekTools.getUuid();
        TorchResponse response = new TorchResponse();
        response.getData().setData(uuid);
        return response;
    }

    /**
     * 客户分类对应关系列表
     *
     * @param dto 客户分类对应关系DTO 实体对象
     * @return
     */
    @Log("客户分类对应关系列表")
    @ApiOperation(value = "客户分类对应关系列表查询")
    @PostMapping(value = "/ccCategorizationCodeList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("accountingStandard:categoriztionSet")
    public TorchResponse<List<CcCategorizationCodeVO>> query(@RequestBody CcCategorizationCodeDTO dto){
        return ccCategorizationCodeService.findCcCategorizationCodeList(dto);
    }

    @Log("批量保存客户分类对应关系")
    @ApiOperation(value = "批量保存客户分类对应关系")
    @PostMapping(value = "/saveCcCategorizationCodeList",produces = { "application/json;charset=utf-8" })
    @TorchPerm("accountingStandard:categoriztionSet")
    public TorchResponse saveCcCategorizationCodeList(@RequestBody List<CcCategorizationCodeDTO> dtos)
    {
        return ccCategorizationCodeService.saveCcCategorizationCodeList(dtos);
    }
}