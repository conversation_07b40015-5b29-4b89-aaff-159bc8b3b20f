<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.SalesOutboundMapper">
    <sql id="Base_Column_List">
        t.work_order_number as workOrderNumber,
        t.order_number as orderNumber,
        t.report_number as reportNumber,
        t.product_name as productName,
        t.product_model as productModel,
        t.batch as batch,
        t.quantity_of_commissioned_items as quantityOfCommissionedItems,
        t.quantity_of_screened_items as quantityOfScreenedItems,
        t.qualified_quantity as qualifiedQuantity,
        t.unqualified_quantity as unqualifiedQuantity,
        t.reason_for_non_quality as reasonForNonQuality,
        t.engineering_code as engineeringCode,
        t.report as report,
        t.inspection_sender as inspectionSender,
        t.recipient_customer as recipient<PERSON>ustomer,
        t.recipient_phone as recipientPhone,
        t.recipient_address as recipientAddress,
        t.express_company as expressCompany,
        t.express_waybill_number as expressWaybillNumber,
        t.shipping_date as shippingDate,
        t.order_inspection_number as orderInspectionNumber,
        t.work_order_inspection_number1 as workOrderInspectionNumber1,
        t.codex_torch_creator_id as codexTorchCreatorId,
        t.codex_torch_updater as codexTorchUpdater,
        t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_create_datetime as codexTorchCreateDatetime,
        t.codex_torch_update_datetime as codexTorchUpdateDatetime,
        t.codex_torch_deleted as codexTorchDeleted
    </sql>

    <sql id="Base_All_Column_List">
        t.id AS id,
        p.entrusted_unit AS entrustedUnit,
        p.work_order_number AS workOrderNumber,
        p.order_number AS orderNumber,
        p.report_number AS reportNumber,
        p.product_name AS productName,
        p.product_model AS productModel,
        p.batch AS batch,
        p.quantity_of_commissioned_items AS quantityOfCommissionedItems,
        p.quantity_of_screened_items AS quantityOfScreenedItems,
        p.qualified_quantity AS qualifiedQuantity,
        p.unqualified_quantity AS unqualifiedQuantity,
        p.reason_for_non_quality AS reasonForNonQuality,
        p.engineering_code AS engineeringCode,
        p.report AS report,
        p.inspection_sender AS inspectionSender,
        t.recipient_customer AS recipientCustomer,
        t.recipient_phone AS recipientPhone,
        t.recipient_address AS recipientAddress,
        t.express_company AS expressCompany,
        t.express_waybill_number AS expressWaybillNumber,
        t.shipping_date AS shippingDate,
        t.codex_torch_creator_id AS codexTorchCreatorId,
        t.codex_torch_updater AS codexTorchUpdater,
        t.codex_torch_group_id AS codexTorchGroupId,
        t.codex_torch_create_datetime AS codexTorchCreateDatetime,
        t.codex_torch_update_datetime AS codexTorchUpdateDatetime,
        t.codex_torch_deleted AS codexTorchDeleted
    </sql>
    <select id="selectSalesOutboundPage" parameterType="com.huatek.frame.modules.business.service.dto.SalesOutboundDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.SalesOutboundVO">
        select
        <include refid="Base_All_Column_List" />
        FROM sales_outbound t
        LEFT JOIN product_inventory p ON t.product_inventory_id = p.id
        <where>
            t.codex_torch_deleted = '0'
            <!-- 产品信息字段条件改为p表 -->
            <if test="entrustedUnit != null and entrustedUnit != ''">
                AND p.entrusted_unit LIKE CONCAT('%', #{entrustedUnit}, '%')
            </if>
            <if test="workOrderNumber != null and workOrderNumber != ''">
                AND p.work_order_number LIKE CONCAT('%', #{workOrderNumber}, '%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                AND p.order_number LIKE CONCAT('%', #{orderNumber}, '%')
            </if>
            <if test="reportNumber != null and reportNumber != ''">
                AND p.report_number LIKE CONCAT('%', #{reportNumber}, '%')
            </if>
            <if test="productName != null and productName != ''">
                AND p.product_name LIKE CONCAT('%', #{productName}, '%')
            </if>
            <if test="productModel != null and productModel != ''">
                AND p.product_model LIKE CONCAT('%', #{productModel}, '%')
            </if>
            <if test="batch != null and batch != ''">
                AND p.batch LIKE CONCAT('%', #{batch}, '%')
            </if>
            <if test="quantityOfCommissionedItems != null and quantityOfCommissionedItems != ''">
                AND p.quantity_of_commissioned_items LIKE CONCAT('%', #{quantityOfCommissionedItems}, '%')
            </if>
            <if test="quantityOfScreenedItems != null and quantityOfScreenedItems != ''">
                AND p.quantity_of_screened_items LIKE CONCAT('%', #{quantityOfScreenedItems}, '%')
            </if>
            <if test="qualifiedQuantity != null and qualifiedQuantity != ''">
                AND p.qualified_quantity LIKE CONCAT('%', #{qualifiedQuantity}, '%')
            </if>
            <if test="unqualifiedQuantity != null and unqualifiedQuantity != ''">
                AND p.unqualified_quantity LIKE CONCAT('%', #{unqualifiedQuantity}, '%')
            </if>
            <if test="reasonForNonQuality != null and reasonForNonQuality != ''">
                AND p.reason_for_non_quality LIKE CONCAT('%', #{reasonForNonQuality}, '%')
            </if>
            <if test="engineeringCode != null and engineeringCode != ''">
                AND p.engineering_code LIKE CONCAT('%', #{engineeringCode}, '%')
            </if>
            <if test="report != null and report != ''">
                AND p.report LIKE CONCAT('%', #{report}, '%')
            </if>
            <if test="inspectionSender != null and inspectionSender != ''">
                AND p.inspection_sender LIKE CONCAT('%', #{inspectionSender}, '%')
            </if>

            <!-- 出库信息字段保持t表 -->
            <if test="recipientCustomer != null and recipientCustomer != ''">
                AND t.recipient_customer LIKE CONCAT('%', #{recipientCustomer}, '%')
            </if>
            <if test="recipientPhone != null and recipientPhone != ''">
                AND t.recipient_phone LIKE CONCAT('%', #{recipientPhone}, '%')
            </if>
            <if test="recipientAddress != null and recipientAddress != ''">
                AND t.recipient_address LIKE CONCAT('%', #{recipientAddress}, '%')
            </if>
            <if test="expressCompany != null and expressCompany != ''">
                AND t.express_company LIKE CONCAT('%', #{expressCompany}, '%')
            </if>
            <if test="expressWaybillNumber != null and expressWaybillNumber != ''">
                AND t.express_waybill_number LIKE CONCAT('%', #{expressWaybillNumber}, '%')
            </if>
            <if test="shippingDate != null and shippingDate!= ''">
                AND t.shipping_date LIKE CONCAT('%', #{shippingDate}, '%')
            </if>

            <!-- 系统字段保持t表 -->
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                AND t.codex_torch_creator_id LIKE CONCAT('%', #{codexTorchCreatorId}, '%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                AND t.codex_torch_updater LIKE CONCAT('%', #{codexTorchUpdater}, '%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                AND t.codex_torch_group_id LIKE CONCAT('%', #{codexTorchGroupId}, '%')
            </if>

            ${params.dataScope}
        </where>
    </select>

    <select id="selectSalesOutboundList" parameterType="com.huatek.frame.modules.business.service.dto.SalesOutboundDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.SalesOutboundVO">
        select
        <include refid="Base_All_Column_List" />
        FROM sales_outbound t
        LEFT JOIN product_inventory p ON t.product_inventory_id = p.id
        <where>
            t.codex_torch_deleted = '0'
            <!-- 产品信息字段条件改为p表 -->
            <if test="entrustedUnit != null and entrustedUnit != ''">
                AND p.entrusted_unit LIKE CONCAT('%', #{entrustedUnit}, '%')
            </if>
            <if test="workOrderNumber != null and workOrderNumber != ''">
                AND p.work_order_number LIKE CONCAT('%', #{workOrderNumber}, '%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                AND p.order_number LIKE CONCAT('%', #{orderNumber}, '%')
            </if>
            <if test="reportNumber != null and reportNumber != ''">
                AND p.report_number LIKE CONCAT('%', #{reportNumber}, '%')
            </if>
            <if test="productName != null and productName != ''">
                AND p.product_name LIKE CONCAT('%', #{productName}, '%')
            </if>
            <if test="productModel != null and productModel != ''">
                AND p.product_model LIKE CONCAT('%', #{productModel}, '%')
            </if>
            <if test="batch != null and batch != ''">
                AND p.batch LIKE CONCAT('%', #{batch}, '%')
            </if>
            <if test="quantityOfCommissionedItems != null and quantityOfCommissionedItems != ''">
                AND p.quantity_of_commissioned_items LIKE CONCAT('%', #{quantityOfCommissionedItems}, '%')
            </if>
            <if test="quantityOfScreenedItems != null and quantityOfScreenedItems != ''">
                AND p.quantity_of_screened_items LIKE CONCAT('%', #{quantityOfScreenedItems}, '%')
            </if>
            <if test="qualifiedQuantity != null and qualifiedQuantity != ''">
                AND p.qualified_quantity LIKE CONCAT('%', #{qualifiedQuantity}, '%')
            </if>
            <if test="unqualifiedQuantity != null and unqualifiedQuantity != ''">
                AND p.unqualified_quantity LIKE CONCAT('%', #{unqualifiedQuantity}, '%')
            </if>
            <if test="reasonForNonQuality != null and reasonForNonQuality != ''">
                AND p.reason_for_non_quality LIKE CONCAT('%', #{reasonForNonQuality}, '%')
            </if>
            <if test="engineeringCode != null and engineeringCode != ''">
                AND p.engineering_code LIKE CONCAT('%', #{engineeringCode}, '%')
            </if>
            <if test="report != null and report != ''">
                AND p.report LIKE CONCAT('%', #{report}, '%')
            </if>
            <if test="inspectionSender != null and inspectionSender != ''">
                AND p.inspection_sender LIKE CONCAT('%', #{inspectionSender}, '%')
            </if>

            <!-- 出库信息字段保持t表 -->
            <if test="recipientCustomer != null and recipientCustomer != ''">
                AND t.recipient_customer LIKE CONCAT('%', #{recipientCustomer}, '%')
            </if>
            <if test="recipientPhone != null and recipientPhone != ''">
                AND t.recipient_phone LIKE CONCAT('%', #{recipientPhone}, '%')
            </if>
            <if test="recipientAddress != null and recipientAddress != ''">
                AND t.recipient_address LIKE CONCAT('%', #{recipientAddress}, '%')
            </if>
            <if test="expressCompany != null and expressCompany != ''">
                AND t.express_company LIKE CONCAT('%', #{expressCompany}, '%')
            </if>
            <if test="expressWaybillNumber != null and expressWaybillNumber != ''">
                AND t.express_waybill_number LIKE CONCAT('%', #{expressWaybillNumber}, '%')
            </if>
            <if test="shippingDate != null and shippingDate!= ''">
                AND t.shipping_date LIKE CONCAT('%', #{shippingDate}, '%')
            </if>

            <!-- 系统字段保持t表 -->
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                AND t.codex_torch_creator_id LIKE CONCAT('%', #{codexTorchCreatorId}, '%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                AND t.codex_torch_updater LIKE CONCAT('%', #{codexTorchUpdater}, '%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                AND t.codex_torch_group_id LIKE CONCAT('%', #{codexTorchGroupId}, '%')
            </if>

            ${params.dataScope}
        </where>
    </select>

    <select id="selectSalesOutboundListByIds"
            resultType="com.huatek.frame.modules.business.domain.vo.SalesOutboundVO">
        select
        <include refid="Base_Column_List" />
        from sales_outbound t
        <where>
            <if test="ids != null and ids.size > 0" >
                t.id in
                <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
                    #{id}                    </foreach>
            </if>
        </where>
    </select>
    <!-- 批量插入销售出库记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sales_outbound (
        id,
        work_order_number,
        recipient_customer,
        recipient_phone,
        recipient_address,
        express_company,
        express_waybill_number,
        shipping_date,
        codex_torch_creator_id,
        codex_torch_updater,
        codex_torch_group_id,
        codex_torch_create_datetime,
        codex_torch_update_datetime,
        codex_torch_deleted,
        product_inventory_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.workOrderNumber},
            #{item.recipientCustomer},
            #{item.recipientPhone},
            #{item.recipientAddress},
            #{item.expressCompany},
            #{item.expressWaybillNumber},
            #{item.shippingDate},
            #{item.codexTorchCreatorId},
            #{item.codexTorchUpdater},
            #{item.codexTorchGroupId},
            #{item.codexTorchCreateDatetime},
            #{item.codexTorchUpdateDatetime},
            #{item.codexTorchDeleted},
            #{item.productInventoryId}
            )
        </foreach>
    </insert>

</mapper>