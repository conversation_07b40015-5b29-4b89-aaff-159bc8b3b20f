package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.CcCategorizationCode;
import com.huatek.frame.modules.business.domain.vo.CcCategorizationCodeVO;
import com.huatek.frame.modules.business.service.dto.CcCategorizationCodeDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 客户分类对应关系mapper
* <AUTHOR>
* @date 2025-08-22
**/
public interface CcCategorizationCodeMapper extends BaseMapper<CcCategorizationCode> {

     /**
	 * 客户分类对应关系分页
	 * @param dto
	 * @return
	 */
	Page<CcCategorizationCodeVO> selectCcCategorizationCodePage(CcCategorizationCodeDTO dto);


    /**
     * 根据条件查询客户分类对应关系列表
     *
     * @param dto 客户分类对应关系信息
     * @return 客户分类对应关系集合信息
     */
    List<CcCategorizationCodeVO> selectCcCategorizationCodeList(CcCategorizationCodeDTO dto);

	/**
	 * 根据IDS查询客户分类对应关系列表
	 * @param ids
	 * @return
	 */
    List<CcCategorizationCodeVO> selectCcCategorizationCodeListByIds(@Param("ids") List<String> ids);


}