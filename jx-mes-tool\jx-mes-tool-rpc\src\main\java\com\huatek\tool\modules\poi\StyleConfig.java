package com.huatek.tool.modules.poi;

import java.io.Serializable;

/**
 * 样式配置
 * @name StyleConfig
 * @description 
 * @version V
 * <AUTHOR>
 * @time 2025年8月22日 下午5:19:20
 */
public class StyleConfig implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer height;//高度
	
	private Integer width; //宽度
	
	private Double fontSize; //字体大小
	
	private String fontFamily; //字体
	
	private String algin; //水平对齐方式
	
	private Integer[] margin; //边距
	
	public StyleConfig() {}

	public StyleConfig(Integer height, Integer width, Double fontSize, String fontFamily, String algin, Integer[] margin) {
		this.height = height;
		this.width = width;
		this.fontSize = fontSize;
		this.fontFamily = fontFamily;
		this.algin = algin;
		this.margin = margin;
	}

	public Integer getHeight() {
		return height;
	}

	public void setHeight(Integer height) {
		this.height = height;
	}

	public Integer getWidth() {
		return width;
	}

	public void setWidth(Integer width) {
		this.width = width;
	}

	public Double getFontSize() {
		return fontSize;
	}

	public void setFontSize(Double fontSize) {
		this.fontSize = fontSize;
	}

	public String getFontFamily() {
		return fontFamily;
	}

	public void setFontFamily(String fontFamily) {
		this.fontFamily = fontFamily;
	}

	public String getAlgin() {
		return algin;
	}

	public void setAlgin(String algin) {
		this.algin = algin;
	}

	public Integer[] getMargin() {
		return margin;
	}

	public void setMargin(Integer[] margin) {
		this.margin = margin;
	}
}
