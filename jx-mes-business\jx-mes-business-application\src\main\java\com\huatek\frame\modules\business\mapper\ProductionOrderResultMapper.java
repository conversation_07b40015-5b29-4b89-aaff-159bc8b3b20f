package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import com.huatek.frame.modules.business.domain.CapabilityAsset;
import com.huatek.frame.modules.business.domain.ProductionOrderResult;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderProcessTestVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderResultVO;
import com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO;
import com.huatek.frame.modules.business.service.dto.CapabilityVerificationCheckDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* 工单检验结果mapper
* <AUTHOR>
* @date 2025-08-04
**/
public interface ProductionOrderResultMapper extends BaseMapper<ProductionOrderResult> {


	/**
	 * 根据工单查询检验结果
	 * @param workOrder
	 * @return
	 */
	ProductionOrderResultVO selectByWorkOrder(@Param("workOrder") String workOrder);

	/**
	 * 根据工单查询所有工序报工数据
	 * @param workOrder
	 * @return
	 */
	List<ProductionOrderProcessTestVO> selectPrcessTestDataByWorkOrder(@Param("workOrder")String workOrder);
}