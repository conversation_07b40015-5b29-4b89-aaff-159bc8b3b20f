package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 文件评审
* <AUTHOR>
* @date 2025-08-20
**/
@Setter
@Getter
@TableName("file_review")
public class FileReview implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 生产工单
     **/
    @TableField(value = "work_order"
    )
    private String workOrder;

    
    /**
	 * 产品id
     **/
    @TableField(value = "product"
    )
    private String product;

    

    /**
	 * 关联异常反馈编号
     **/
    @TableField(value = "assoc_exception_feedback_num"
    )
    private String assocExceptionFeedbackNum;

    
    /**
	 * 变更文件
     **/
    @TableField(value = "change_file"
    )
    private String changeFile;

    
    /**
	 * 评审结果
     **/
    @TableField(value = "review_result"
    )
    private String reviewResult;

    
    /**
	 * 评审备注
     **/
    @TableField(value = "review_remark"
    )
    private String reviewRemark;

    
    /**
	 * 评审人
     **/
    @TableField(value = "reviewer"
    )
    private String reviewer;

    
    /**
	 * 评审时间
     **/
    @TableField(value = "review_time",fill = FieldFill.INSERT
    )
    private Timestamp reviewTime;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}