package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.WorkReportTableVO;
import com.huatek.frame.modules.business.service.dto.WorkReportTableDTO;

import java.util.List;


/**
* @description 工时报表Service
* <AUTHOR>
* @date 2025-08-18
**/
public interface WorkReportTableService {
    
    /**
	 * 分页查找查找 工时报表
	 * 
	 * @param dto 工时报表dto实体对象
	 * @return 
	 */
	TorchResponse<List<WorkReportTableVO>> findWorkReportTablePage(WorkReportTableDTO dto);

    /**
	 * 添加 \修改 工时报表
	 * 
	 * @param workReportTableDto 工时报表dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(WorkReportTableDTO workReportTableDto);
	
	/**
	 * 通过id查找工时报表
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<WorkReportTableVO> findWorkReportTable(String id);
	
	/**
	 * 删除 工时报表
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

    /**
     * 生成工时报表
     * 
     * @return 生成结果
     */
    TorchResponse generateWorkReportTable();

	/**
	 * 查找关联信息 工时报表
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<WorkReportTableVO>> getOptionsList(String id);




    /**
     * 根据条件查询工时报表列表
     *
     * @param dto 工时报表信息
     * @return 工时报表集合信息
     */
    List<WorkReportTableVO> selectWorkReportTableList(WorkReportTableDTO dto);

    /**
     * 导入工时报表数据
     *
     * @param workReportTableList 工时报表数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importWorkReportTable(List<WorkReportTableVO> workReportTableList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取工时报表数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectWorkReportTableListByIds(List<String> ids);


}