package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 产品明细
* <AUTHOR>
* @date 2025-08-20
**/
@Setter
@Getter
@TableName("product_details")
public class ProductDetails implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 工单编号
     **/
    @TableField(value = "work_order_number"
    )
    private String workOrderNumber;

    
    /**
	 * 规格型号
     **/
    @TableField(value = "specification_model"
    )
    private String specificationModel;

    
    /**
	 * 品名
     **/
    @TableField(value = "name_of_product"
    )
    private String nameOfProduct;

    
    /**
	 * 质量等级
     **/
    @TableField(value = "quality_grade"
    )
    private String qualityGrade;

    
    /**
	 * 生产批次
     **/
    @TableField(value = "production_batch"
    )
    private String productionBatch;

    
    /**
	 * 质保依据
     **/
    @TableField(value = "quality_assurance_basis"
    )
    private String qualityAssuranceBasis;

    
    /**
	 * 合同数量(只)
     **/
    @TableField(value = "quantity_of_contract_units"
    )
    private Long quantityOfContractUnits;

    
    /**
	 * 状态
     **/
    @TableField(value = "status"
    )
    private String status;

    
    /**
	 * 完成日期
     **/
    @TableField(value = "completion_date"
    )
    private Date completionDate;

    
    /**
	 * 备注
     **/
    @TableField(value = "`comment`"
    )
    private String comment;

    
    /**
	 * 主表单ID
     **/
    @TableField(value = "codex_torch_master_form_id"
    )
    private String codexTorchMasterFormId;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}