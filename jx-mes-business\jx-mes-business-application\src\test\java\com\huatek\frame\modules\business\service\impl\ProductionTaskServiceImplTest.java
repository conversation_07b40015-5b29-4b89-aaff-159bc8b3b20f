package com.huatek.frame.modules.business.service.impl;

import com.huatek.frame.modules.business.domain.ProdTaskOpHist;
import com.huatek.frame.modules.business.domain.ProductionTask;
import com.huatek.frame.modules.business.mapper.ProdTaskOpHistMapper;
import com.huatek.frame.modules.business.mapper.ProductionTaskMapper;
import com.huatek.frame.modules.business.service.ProdTaskOpHistService;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.common.utils.Constant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 生产任务服务测试类
 * 主要测试状态恢复逻辑
 */
@ExtendWith(MockitoExtension.class)
class ProductionTaskServiceImplTest {

    @Mock
    private ProductionTaskMapper productionTaskMapper;

    @Mock
    private ProdTaskOpHistMapper prodTaskOpHistMapper;

    @Mock
    private ProdTaskOpHistService prodTaskOpHistService;

    @InjectMocks
    private ProductionTaskServiceImpl productionTaskService;

    private ProductionTask testTask;
    private ProdTaskOpHist pauseRecord;

    @BeforeEach
    void setUp() {
        // 创建测试用的生产任务
        testTask = new ProductionTask();
        testTask.setId("test-task-id");
        testTask.setTaskNumber("SCRW-2025-001");
        testTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING); // 暂停状态

        // 创建测试用的暂停操作记录
        pauseRecord = new ProdTaskOpHist();
        pauseRecord.setId("pause-record-id");
        pauseRecord.setTaskNumber("SCRW-2025-001");
        pauseRecord.setOperationType(DicConstant.ProductionOrder.OPERATION_TYPE_PAUSE);
        pauseRecord.setPreviousStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI); // 暂停前是未开始状态
        pauseRecord.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
    }

    @Test
    void testResumeTask_ShouldRestoreToPreviousStatus() {
        // 模拟查询任务
        when(productionTaskMapper.selectById("test-task-id")).thenReturn(testTask);
        
        // 模拟查询最近一次暂停操作记录
        when(prodTaskOpHistMapper.selectOne(any())).thenReturn(pauseRecord);

        // 执行恢复操作
        var response = productionTaskService.resumeTask("test-task-id");

        // 验证任务状态被恢复到暂停前的状态（未开始）
        verify(productionTaskMapper).updateById(argThat(task -> 
            DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI.equals(task.getStatus())
        ));

        // 验证插入了恢复操作记录
        verify(prodTaskOpHistService).saveOrUpdate(any());

        // 验证响应
        assertEquals(Constant.REQUEST_SUCCESS, response.getStatus());
        assertTrue(response.getMessage().contains("未开始"));
    }

    @Test
    void testResumeTask_WithProgressStatus_ShouldRestoreToProgress() {
        // 修改暂停前状态为进行中
        pauseRecord.setPreviousStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG);
        
        when(productionTaskMapper.selectById("test-task-id")).thenReturn(testTask);
        when(prodTaskOpHistMapper.selectOne(any())).thenReturn(pauseRecord);

        var response = productionTaskService.resumeTask("test-task-id");

        // 验证任务状态被恢复到暂停前的状态（进行中）
        verify(productionTaskMapper).updateById(argThat(task -> 
            DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG.equals(task.getStatus())
        ));

        assertTrue(response.getMessage().contains("进行中"));
    }

    @Test
    void testResumeTask_NoPauseRecord_ShouldDefaultToProgress() {
        when(productionTaskMapper.selectById("test-task-id")).thenReturn(testTask);
        when(prodTaskOpHistMapper.selectOne(any())).thenReturn(null); // 没有找到暂停记录

        var response = productionTaskService.resumeTask("test-task-id");

        // 验证任务状态被恢复到默认状态（进行中）
        verify(productionTaskMapper).updateById(argThat(task -> 
            DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG.equals(task.getStatus())
        ));

        assertTrue(response.getMessage().contains("进行中"));
    }

    @Test
    void testResumeTask_NotPausedTask_ShouldThrowException() {
        // 设置任务状态为非暂停状态
        testTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG);
        
        when(productionTaskMapper.selectById("test-task-id")).thenReturn(testTask);

        // 验证抛出异常
        assertThrows(RuntimeException.class, () -> {
            productionTaskService.resumeTask("test-task-id");
        });
    }

    @Test
    void testResumeTask_TaskNotFound_ShouldThrowException() {
        when(productionTaskMapper.selectById("test-task-id")).thenReturn(null);

        assertThrows(RuntimeException.class, () -> {
            productionTaskService.resumeTask("test-task-id");
        });
    }
}
