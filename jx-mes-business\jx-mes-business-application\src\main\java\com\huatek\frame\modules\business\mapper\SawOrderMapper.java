package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;
import com.huatek.frame.modules.business.domain.SawOrder;
import  com.huatek.frame.modules.business.domain.vo.SawOrderVO;
import com.huatek.frame.modules.business.service.dto.SawOrderDTO;
import com.huatek.frame.modules.business.service.dto.SawOrderPageDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 监制验收工单mapper
* <AUTHOR>
* @date 2025-08-20
**/
public interface SawOrderMapper extends BaseMapper<SawOrder> {

     /**
	 * 监制验收工单分页
	 * @param dto
	 * @return
	 */
	Page<SawOrderVO> selectSawOrderPage(SawOrderPageDTO requestParam);

    /**
	 * 外键关联表: sys_user - name
     **/
    @ApiModelProperty("外键 sys_user - name")
	Page<SelectOptionsVO> selectOptionsByResponsiblePerson(String responsiblePerson);

    /**
     * 根据条件查询监制验收工单列表
     *
     * @param dto 监制验收工单信息
     * @return 监制验收工单集合信息
     */
    List<SawOrderVO> selectSawOrderList(SawOrderDTO dto);

	/**
	 * 根据IDS查询监制验收工单列表
	 * @param ids
	 * @return
	 */
    List<SawOrderVO> selectSawOrderListByIds(@Param("ids") List<String> ids);


	/**
	 * 根据id查看监制验收工单详情
	 * @param id 工单id
	 * @return
	 */
	SawOrderVO findSawOrder(String id);
}