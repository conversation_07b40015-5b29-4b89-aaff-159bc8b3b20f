package com.huatek.frame.modules.system.service;

import java.util.List;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.service.dto.SysGroupDTO;

/**
 * 系统_组织 Service
 *
 * <AUTHOR>
 * @date 2018-7-11 14:03:46
 */
public interface SysGroupService {

    /**
     * 分页查找查找 系统_组织
     *
     * @param page 页码
     * @param limit 每页显示数量
     * @param dto 系统_组织dto
     * @return map map
     */
	TorchResponse<List<SysGroupVO>>  findGroupPage(SysGroupDTO dto);


    /**
     * 添加 系统_组织
     * 
     * @param sysGroup 系统_组织group
     * @return map map
     */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(SysGroup sysGroup);

    /**
     *  通过id查找 系统_组织
     *
     * @param id 主键
     * @return map map
     */
	TorchResponse<SysGroupVO> findGroup(String id);
    /**
      * 删除 系统_组织
      * 
      * @param ids 主键集合
      * @return map map
      */
	@SuppressWarnings("rawtypes")
	TorchResponse delete( String[] ids);

	/**
	 * 获取部门树结构数据
	 * @return
	 */
	TorchResponse findGroupsCascade();

	/**
	 * 根据级联LabelName获取查询数据
	 * @param labelName
	 * @return
	 */
	TorchResponse findCascadeDataByLabelName(String labelName);

	/**
	 * 根据部门ID获取用户
	 * @param groupId
	 * @return
	 */
    TorchResponse findUserByGroupId(String groupId);

	/**
	 * 根据编码查询单位
	 * @param groupCode
	 * @return
	 */
	SysGroup findGroupsByCode(String groupCode);

	SysGroup selectById(String id);

	/**
	 * 根据组织ID查询当前和所有上级的group_code
	 * @param groupId
	 * @return
	 */
	List<String> selectGroupCodeByGroupId(String groupId);
}

