<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>jx-mes-tool</artifactId>
    <groupId>com.huatek.jx</groupId>
    <version>1.0.0</version>
  </parent>

  <artifactId>jx-mes-tool-rpc</artifactId>
  <name>jx-mes-tool-rpc</name>
  <packaging>jar</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>

  <dependencies>
<!--    <dependency>-->
<!--      <groupId>com.huatek.torch</groupId>-->
<!--      <artifactId>jx-mes-basic</artifactId>-->
<!--      <version>1.1.0-aigc</version>-->
<!--    </dependency>-->
    <dependency>
      <groupId>com.huatek.torch</groupId>
      <artifactId>torch-log-sdk</artifactId>
      <version>1.0.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi-ooxml</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi-ooxml-schemas</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi-scratchpad</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>io.springfox</groupId>-->
<!--      <artifactId>springfox-swagger2</artifactId>-->
<!--      <version>2.9.2</version> &lt;!&ndash; 建议用最新版本 &ndash;&gt;-->
<!--    </dependency>-->

    <!-- 可选：Swagger UI 界面 -->
<!--    <dependency>-->
<!--      <groupId>io.springfox</groupId>-->
<!--      <artifactId>springfox-swagger-ui</artifactId>-->
<!--      <version>2.9.2</version>-->
<!--    </dependency>-->
    <!--lombok插件 -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>5.2.3</version>
    </dependency>
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>5.2.3</version>
    </dependency>
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-scratchpad</artifactId>
        <version>5.2.3</version>
    </dependency>
            <dependency>
            <groupId>com.documents4j</groupId>
            <artifactId>documents4j-local</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.documents4j</groupId>
            <artifactId>documents4j-transformer-msoffice-word</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>19.0</version>
        </dependency>
  </dependencies>
</project>
