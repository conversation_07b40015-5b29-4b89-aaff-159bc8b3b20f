package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 销售出库DTO 实体类
* <AUTHOR>
* @date 2025-08-06
**/
@Data
@ApiModel("销售出库DTO实体类")
public class SalesOutboundDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;


    @ApiModelProperty("ID")
    private String id;

    // 产品信息相关字段（关联product_inventory表）
    @ApiModelProperty("委托单位")
    private String entrustedUnit;

    @ApiModelProperty("工单号")
    private String workOrderNumber;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("报告编号")
    private String reportNumber;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品型号")
    private String productModel;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("委托数量")
    private String quantityOfCommissionedItems;

    @ApiModelProperty("筛选项数量")
    private String quantityOfScreenedItems;

    @ApiModelProperty("合格数量")
    private String qualifiedQuantity;

    @ApiModelProperty("不合格数量")
    private String unqualifiedQuantity;

    @ApiModelProperty("不合格原因")
    private String reasonForNonQuality;

    @ApiModelProperty("工程代号")
    private String engineeringCode;

    @ApiModelProperty("报告")
    private String report;

    @ApiModelProperty("检验发送者")
    private String inspectionSender;

    // 出库信息相关字段（sales_outbound表）
    @ApiModelProperty("收货客户")
    private String recipientCustomer;

    @ApiModelProperty("收货电话")
    private String recipientPhone;

    @ApiModelProperty("收货地址")
    private String recipientAddress;

    @ApiModelProperty("快递公司")
    private String expressCompany;

    @ApiModelProperty("快递单号")
    private String expressWaybillNumber;

    @ApiModelProperty("发货日期")
    private String shippingDate;

    // 系统字段（sales_outbound表）
    @ApiModelProperty("创建人ID")
    private String codexTorchCreatorId;

    @ApiModelProperty("更新人")
    private String codexTorchUpdater;

    @ApiModelProperty("组ID")
    private String codexTorchGroupId;

    @ApiModelProperty("创建时间")
    private String codexTorchCreateDatetime;

    @ApiModelProperty("更新时间")
    private String codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;

	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;

	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;

}