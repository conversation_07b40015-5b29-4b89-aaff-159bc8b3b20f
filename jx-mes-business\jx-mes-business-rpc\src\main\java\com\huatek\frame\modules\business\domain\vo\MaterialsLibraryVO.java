package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 材料库VO实体类
* <AUTHOR>
* @date 2025-08-08
**/
@Data
@ApiModel("材料库DTO实体类")
public class MaterialsLibraryVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 物料代码
     **/
    @ApiModelProperty("物料代码")
    @Excel(name = "物料代码",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String materialCode;

    /**
	 * 物料名称
     **/
    @ApiModelProperty("物料名称")
    @Excel(name = "物料名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String materialName;

    /**
	 * 规格型号
     **/
    @ApiModelProperty("规格型号")
    @Excel(name = "规格型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String specificationModel;

    /**
	 * 库存数量
     **/
    @ApiModelProperty("库存数量")
    @Excel(name = "库存数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long inventoryQuantity;

    /**
	 * 采购在订量
     **/
    @ApiModelProperty("采购在订量")
    @Excel(name = "采购在订量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long quantityOnOrderForPurchase;

    /**
	 * 可用库存量
     **/
    @ApiModelProperty("可用库存量")
    @Excel(name = "可用库存量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long availableStockQuantity;

    /**
	 * 基本单位名称
     **/
    @ApiModelProperty("基本单位名称")
    @Excel(name = "基本单位名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String basicUnitName;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}