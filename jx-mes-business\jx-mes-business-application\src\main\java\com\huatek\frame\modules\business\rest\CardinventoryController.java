package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.CardinventoryVO;
import com.huatek.frame.modules.business.service.CardinventoryService;
import com.huatek.frame.modules.business.service.dto.CardinventoryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-04
**/
@Api(tags = "板卡存放管理")
@RestController
@RequestMapping("/api/cardinventory")
public class CardinventoryController {

	@Autowired
    private CardinventoryService cardinventoryService;

	/**
	 * 板卡存放列表
	 * 
	 * @param dto 板卡存放DTO 实体对象
	 * @return
	 */
    @Log("板卡存放列表")
    @ApiOperation(value = "板卡存放列表查询")
    @PostMapping(value = "/cardinventoryList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("cardinventory:list")
    public TorchResponse<List<CardinventoryVO>> query(@RequestBody CardinventoryDTO dto){
        return cardinventoryService.findCardinventoryPage(dto);
    }

	/**
	 * 新增/修改板卡存放
	 * 
	 * @param cardinventoryDto 板卡存放DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改板卡存放")
    @ApiOperation(value = "板卡存放新增/修改操作")
    @PostMapping(value = "/cardinventory", produces = { "application/json;charset=utf-8" })
    @TorchPerm("cardinventory:add#cardinventory:edit")
    public TorchResponse add(@RequestBody CardinventoryDTO cardinventoryDto) throws Exception {
		// BeanValidatorFactory.validate(cardinventoryDto);
		return cardinventoryService.saveOrUpdate(cardinventoryDto);
	}

	/**
	 * 查询板卡存放详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("板卡存放详情")
    @ApiOperation(value = "板卡存放详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("cardinventory:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return cardinventoryService.findCardinventory(id);
	}

	/**
	 * 删除板卡存放
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除板卡存放")
    @ApiOperation(value = "板卡存放删除操作")
    @TorchPerm("cardinventory:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return cardinventoryService.delete(ids);
	}

    @ApiOperation(value = "板卡存放联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return cardinventoryService.getOptionsList(id);
	}





    @Log("板卡存放导出")
    @ApiOperation(value = "板卡存放导出")
    @TorchPerm("cardinventory:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody CardinventoryDTO dto)
    {
        List<CardinventoryVO> list = cardinventoryService.selectCardinventoryList(dto);
        ExcelUtil<CardinventoryVO> util = new ExcelUtil<CardinventoryVO>(CardinventoryVO.class);
        util.exportExcel(response, list, "板卡存放数据");
    }

    @Log("板卡存放导入")
    @ApiOperation(value = "板卡存放导入")
    @TorchPerm("cardinventory:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<CardinventoryVO> util = new ExcelUtil<CardinventoryVO>(CardinventoryVO.class);
        List<CardinventoryVO> list = util.importExcel(file.getInputStream());
        return cardinventoryService.importCardinventory(list, unionColumns, true, "");
    }

    @Log("板卡存放导入模板")
    @ApiOperation(value = "板卡存放导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<CardinventoryVO> util = new ExcelUtil<CardinventoryVO>(CardinventoryVO.class);
        util.importTemplateExcel(response, "板卡存放数据");
    }

    @Log("根据Ids获取板卡存放列表")
    @ApiOperation(value = "板卡存放 根据Ids批量查询")
    @PostMapping(value = "/cardinventoryList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getCardinventoryListByIds(@RequestBody List<String> ids) {
        return cardinventoryService.selectCardinventoryListByIds(ids);
    }


}