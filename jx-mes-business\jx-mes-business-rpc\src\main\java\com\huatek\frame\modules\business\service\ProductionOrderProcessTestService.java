package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderResultVO;
import com.huatek.frame.modules.business.service.dto.ProductionOrderProcessTestDTO;
import com.huatek.frame.modules.business.service.dto.ProductionOrderResultDTO;

/**
* @description 工单检验结果Service
* <AUTHOR>
* @date 2025-07-30
**/
public interface ProductionOrderProcessTestService {

    /**
     * 保存工单工序试验数据
     * @param productionOrderProcessTestDTO
     * @return
     */
    TorchResponse saveOrUpdate(ProductionOrderProcessTestDTO productionOrderProcessTestDTO);
}