package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
* @description 产值计算明细
* <AUTHOR>
* @date 2025-08-22
**/
@Setter
@Getter
@TableName("prod_val_calc_details")
public class ProdValCalcDetails implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 产值计算id
     **/
    @TableField(value = "production_value_calculation_id"
    )
    private String productionValueCalculationId;

    
    /**
	 * 试验项目
     **/
    @TableField(value = "experiment_project"
    )
    private String experimentProject;

    
    /**
	 * 数量
     **/
    @TableField(value = "quantity"
    )
    private Integer quantity;

    
    /**
	 * 单价
     **/
    @TableField(value = "unit_price"
    )
    private BigDecimal unitPrice;

    
    /**
	 * 合计
     **/
    @TableField(value = "total"
    )
    private BigDecimal total;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}