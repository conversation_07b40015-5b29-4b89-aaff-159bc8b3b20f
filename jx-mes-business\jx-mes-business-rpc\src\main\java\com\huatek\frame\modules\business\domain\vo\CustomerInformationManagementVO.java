package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 客户信息管理VO实体类
* <AUTHOR>
* @date 2025-07-16
**/
@Data
@ApiModel("客户信息管理VO实体类")
public class CustomerInformationManagementVO implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 客户编号
     **/
    @ApiModelProperty("客户编号")
    private String customerId0;
    
    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String entrustedUnit;

    @ApiModelProperty("简称")
    @Excel(name = "简称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String shortName;

    /**
     * 类型
     **/
    @ApiModelProperty("类型")
    @Excel(name = "类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String type;

    /**
     * 客户优先级
     **/
    @ApiModelProperty("客户优先级")
    @Excel(name = "客户优先级",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String customerPriority;


    /**
     * 结算单位
     **/
    @ApiModelProperty("结算单位")
    @Excel(name = "结算单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String settlementUnit;
    
    /**
	 * 传真
     **/
    @ApiModelProperty("传真")
    @Excel(name = "传真",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String fax;
    
    /**
	 * 客户经理
     **/
    @ApiModelProperty("客户经理")
    @Excel(name = "客户经理",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String customerManager;
    
    /**
	 * 委托单位地址
     **/
    @ApiModelProperty("委托单位地址")
    @Excel(name = "委托单位地址",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String addressOfEntrustedUnit;
    
    /**
	 * 加盖CNAS章
     **/
    @ApiModelProperty("加盖CNAS章")
    @Excel(name = "是否加盖CNAS章",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String sealedWithCnasSeal;
    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String comment;
    
    /**
	 * 子表明细项ID
     **/
    @ApiModelProperty("子表明细项ID")
    private String codexTorchDetailItemIds;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人姓名")
    private String creator;
    /**
	 * 创建人id
     **/
    @ApiModelProperty("创建人id")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
//    @Excel(name = "更新人",
//        cellType = Excel.ColumnType.STRING,
//        type = Excel.Type.ALL)
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}