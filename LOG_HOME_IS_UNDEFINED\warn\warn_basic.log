2025-08-21 11:19:14,765 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:4) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-21 11:19:26,486 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:4) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-21 11:19:44,773 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:4) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-21 11:19:56,492 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:4) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
