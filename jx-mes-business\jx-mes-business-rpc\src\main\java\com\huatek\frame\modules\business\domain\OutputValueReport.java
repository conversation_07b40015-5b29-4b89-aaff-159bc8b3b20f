package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
* @description 产值报表
* <AUTHOR>
* @date 2025-08-28
**/
@Setter
@Getter
@TableName("output_value_report")
public class OutputValueReport implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 结算单位
     **/
    @TableField(value = "settlement_unit"
    )
    private String settlementUnit;

    
    /**
	 * 年度
     * eg.2025
     **/
    @TableField(value = "annual"
    )
    private String annual;

    
    /**
	 * 月份
     * eg.01 02 03 ~12
     **/
    @TableField(value = "`month`"
    )
    private String month;

    
    /**
	 * 内部核算金额
     **/
    @TableField(value = "internal_accounting_amount"
    )
    private BigDecimal internalAccountingAmount;

    
    /**
	 * 客户对账价格
     **/
    @TableField(value = "customer_reconciliation_price"
    )
    private BigDecimal customerReconciliationPrice;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}