package com.huatek.frame.modules.business.service;

import com.huatek.frame.modules.business.domain.OutputValueReport;
import com.huatek.frame.modules.business.service.dto.OutputValueReportDTO;
import com.huatek.frame.modules.business.domain.vo.OutputValueReportVO;
import com.huatek.frame.common.response.TorchResponse;
import java.util.List;


/**
* @description 产值报表Service
* <AUTHOR>
* @date 2025-08-28
**/
public interface OutputValueReportService {
    
    /**
	 * 分页查找查找 产值报表
	 * 
	 * @param dto 产值报表dto实体对象
	 * @return 
	 */
	TorchResponse<List<OutputValueReportVO>> findOutputValueReportPage(OutputValueReportDTO dto);

    /**
	 * 添加 \修改 产值报表
	 * 
	 * @param outputValueReportDto 产值报表dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(OutputValueReportDTO outputValueReportDto);
	
	/**
	 * 通过id查找产值报表
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<OutputValueReportVO> findOutputValueReport(String id);
	
	/**
	 * 删除 产值报表
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

    /**
     * 获取首页看板统计数据
     * @return 统计数据
     */
    TorchResponse getDashboardStats();

	/**
	 * 查找关联信息 产值报表
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<OutputValueReportVO>> getOptionsList(String id);




    /**
     * 根据条件查询产值报表列表
     *
     * @param dto 产值报表信息
     * @return 产值报表集合信息
     */
    List<OutputValueReportVO> selectOutputValueReportList(OutputValueReportDTO dto);

    /**
     * 导入产值报表数据
     *
     * @param outputValueReportList 产值报表数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importOutputValueReport(List<OutputValueReportVO> outputValueReportList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取产值报表数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectOutputValueReportListByIds(List<String> ids);


	TorchResponse generateOutputVal();
}