package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.ProdValCalcDetails;
import com.huatek.frame.modules.business.domain.vo.ProdValCalcDetailsVO;
import com.huatek.frame.modules.business.mapper.ProdValCalcDetailsMapper;
import com.huatek.frame.modules.business.service.ProdValCalcDetailsService;
import com.huatek.frame.modules.business.service.dto.ProdValCalcDetailsDTO;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import java.sql.Date;
import org.springframework.util.CollectionUtils;



/**
 * 产值计算明细 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "prodValCalcDetails")
//@RefreshScope
@Slf4j
public class ProdValCalcDetailsServiceImpl implements ProdValCalcDetailsService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private ProdValCalcDetailsMapper prodValCalcDetailsMapper;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public ProdValCalcDetailsServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ProdValCalcDetailsVO>> findProdValCalcDetailsPage(ProdValCalcDetailsDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ProdValCalcDetailsVO> prodValCalcDetailss = prodValCalcDetailsMapper.selectProdValCalcDetailsPage(dto);
		TorchResponse<List<ProdValCalcDetailsVO>> response = new TorchResponse<List<ProdValCalcDetailsVO>>();
		response.getData().setData(prodValCalcDetailss);
		response.setStatus(200);
		response.getData().setCount(prodValCalcDetailss.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ProdValCalcDetailsDTO prodValCalcDetailsDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(prodValCalcDetailsDto.getCodexTorchDeleted())) {
            prodValCalcDetailsDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = prodValCalcDetailsDto.getId();
		ProdValCalcDetails entity = new ProdValCalcDetails();
        BeanUtils.copyProperties(prodValCalcDetailsDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			prodValCalcDetailsMapper.insert(entity);
		} else {
			prodValCalcDetailsMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        ProdValCalcDetailsVO vo = new ProdValCalcDetailsVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProdValCalcDetailsVO> findProdValCalcDetails(String id) {
		ProdValCalcDetailsVO vo = new ProdValCalcDetailsVO();
		if (!HuatekTools.isEmpty(id)) {
			ProdValCalcDetails entity = prodValCalcDetailsMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<ProdValCalcDetailsVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<ProdValCalcDetails> prodValCalcDetailsList = prodValCalcDetailsMapper.selectBatchIds(Arrays.asList(ids));
        for (ProdValCalcDetails prodValCalcDetails : prodValCalcDetailsList) {
            prodValCalcDetails.setCodexTorchDeleted(Constant.DEFAULT_YES);
            prodValCalcDetailsMapper.updateById(prodValCalcDetails);
        }
		//prodValCalcDetailsMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "prod_val_calc_details", convertorFields = "")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProdValCalcDetailsVO> selectProdValCalcDetailsList(ProdValCalcDetailsDTO dto) {
        return prodValCalcDetailsMapper.selectProdValCalcDetailsList(dto);
    }

    /**
     * 导入产值计算明细数据
     *
     * @param prodValCalcDetailsList 产值计算明细数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "prod_val_calc_details", convertorFields = "")
    public TorchResponse importProdValCalcDetails(List<ProdValCalcDetailsVO> prodValCalcDetailsList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(prodValCalcDetailsList) || prodValCalcDetailsList.size() == 0) {
            throw new ServiceException("导入产值计算明细数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProdValCalcDetailsVO vo : prodValCalcDetailsList) {
            try {
                ProdValCalcDetails prodValCalcDetails = new ProdValCalcDetails();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, prodValCalcDetails);
                QueryWrapper<ProdValCalcDetails> wrapper = new QueryWrapper();
                ProdValCalcDetails oldProdValCalcDetails = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = ProdValCalcDetailsVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<ProdValCalcDetails> oldProdValCalcDetailsList = prodValCalcDetailsMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldProdValCalcDetailsList) && oldProdValCalcDetailsList.size() > 1) {
                        prodValCalcDetailsMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldProdValCalcDetailsList) && oldProdValCalcDetailsList.size() == 1) {
                        oldProdValCalcDetails = oldProdValCalcDetailsList.get(0);
                    }
                }
                if (StringUtils.isNull(oldProdValCalcDetails)) {
                    BeanValidators.validateWithException(validator, vo);
                    prodValCalcDetailsMapper.insert(prodValCalcDetails);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、产值计算id " + vo.getProductionValueCalculationId() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldProdValCalcDetails, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    prodValCalcDetailsMapper.updateById(oldProdValCalcDetails);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、产值计算id " + vo.getProductionValueCalculationId() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、产值计算id " + vo.getProductionValueCalculationId() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、产值计算id " + vo.getProductionValueCalculationId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(ProdValCalcDetailsVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProdValCalcDetailsListByIds(List<String> ids) {
        List<ProdValCalcDetailsVO> prodValCalcDetailsList = prodValCalcDetailsMapper.selectProdValCalcDetailsListByIds(ids);

		TorchResponse<List<ProdValCalcDetailsVO>> response = new TorchResponse<List<ProdValCalcDetailsVO>>();
		response.getData().setData(prodValCalcDetailsList);
		response.setStatus(200);
		response.getData().setCount((long)prodValCalcDetailsList.size());
		return response;
    }
    
    @Override
    public List<ProdValCalcDetailsVO> selectPVCDListByPVCIdJunXin(String productionValueCalculationId) {

        List<ProdValCalcDetailsVO> prodValCalcDetailsList = prodValCalcDetailsMapper.selectPVCDListByPVCIdJunXin(productionValueCalculationId);

		return prodValCalcDetailsList;
    }
    @Override
    public List<ProdValCalcDetailsVO> selectPVCDListByPVCIdCustomer(String productionValueCalculationId) {

        List<ProdValCalcDetailsVO> prodValCalcDetailsList = prodValCalcDetailsMapper.selectPVCDListByPVCIdCustomer(productionValueCalculationId);

        return prodValCalcDetailsList;
    }





}
