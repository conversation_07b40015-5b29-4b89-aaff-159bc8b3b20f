package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description 核算标准明细DTO 实体类
* <AUTHOR>
* @date 2025-08-22
**/
@Data
@ApiModel("核算标准明细DTO实体类")
public class AccountingStandardDetailsDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    private String productCategory;
    
    /**
	 * 试验项目
     **/
    @ApiModelProperty("试验项目")
    private String experimentProject;
    
    /**
	 * 数值
     **/
    @ApiModelProperty("数值")
    private BigDecimal quantity;
    
    /**
	 * 单位
     **/
    @ApiModelProperty("单位")
    private String unit;
    
    /**
	 * 收费形式
     **/
    @ApiModelProperty("收费形式")
    private String chargingMethod;
    
    /**
	 * 最低数量要求
     **/
    @ApiModelProperty("最低数量要求")
    private Integer minimumQuantityRequirement;
    
    /**
	 * 基础价格
     **/
    @ApiModelProperty("基础价格")
    private BigDecimal basePrice;
    
    /**
	 * 收费标准编号
     **/
    @ApiModelProperty("收费标准编号")
    private String chargeStandardNumber;
    
    /**
	 * 低于要求数量计算倍数
     **/
    @ApiModelProperty("低于要求数量计算倍数")
    private BigDecimal cfbrq;
    
    /**
	 * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;


}