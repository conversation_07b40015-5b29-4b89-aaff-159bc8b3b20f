package com.huatek.frame.modules.business.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description 产值计算 合同录入 DTO 实体类
 * <AUTHOR>
 * @date 2025-08-22
 **/
@Data
@ApiModel("产值计算 合同录入 DTO实体类")
public class ProductionValueCalculationContractDTO {
    /**
     * 主键ID列表（批量操作）
     **/
    @ApiModelProperty("主键ID列表")
    private List<String> ids;



    /**
     * 合同编号
     **/
    @ApiModelProperty("合同编号")
    private String contractNumber;
}
