package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.domain.ProductDetails;
import com.huatek.frame.modules.business.service.ProductDetailsService;
import com.huatek.frame.modules.business.service.dto.ProductDetailsDTO;
import com.huatek.frame.modules.business.domain.vo.ProductDetailsVO;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-20
**/
@Api(tags = "产品明细管理")
@RestController
@RequestMapping("/api/productDetails")
public class ProductDetailsController {

	@Autowired
    private ProductDetailsService productDetailsService;

	/**
	 * 产品明细列表
	 * 
	 * @param dto 产品明细DTO 实体对象
	 * @return
	 */
    @Log("产品明细列表")
    @ApiOperation(value = "产品明细列表查询")
    @PostMapping(value = "/productDetailsList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productDetails:list")
    public TorchResponse<List<ProductDetailsVO>> query(@RequestBody ProductDetailsDTO dto){
        return productDetailsService.findProductDetailsPage(dto);
    }

	/**
	 * 新增/修改产品明细
	 * 
	 * @param productDetailsDto 产品明细DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改产品明细")
    @ApiOperation(value = "产品明细新增/修改操作")
    @PostMapping(value = "/productDetails", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productDetails:add#productDetails:edit")
    public TorchResponse add(@RequestBody ProductDetailsDTO productDetailsDto) throws Exception {
		// BeanValidatorFactory.validate(productDetailsDto);
		return productDetailsService.saveOrUpdate(productDetailsDto);
	}

	/**
	 * 查询产品明细详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("产品明细详情")
    @ApiOperation(value = "产品明细详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productDetails:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return productDetailsService.findProductDetails(id);
	}

	/**
	 * 删除产品明细
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除产品明细")
    @ApiOperation(value = "产品明细删除操作")
    @TorchPerm("productDetails:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return productDetailsService.delete(ids);
	}

    @ApiOperation(value = "产品明细联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return productDetailsService.getOptionsList(id);
	}





    @Log("产品明细导出")
    @ApiOperation(value = "产品明细导出")
    @TorchPerm("productDetails:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ProductDetailsDTO dto)
    {
        List<ProductDetailsVO> list = productDetailsService.selectProductDetailsList(dto);
        ExcelUtil<ProductDetailsVO> util = new ExcelUtil<ProductDetailsVO>(ProductDetailsVO.class);
        util.exportExcel(response, list, "产品明细数据");
    }

    @Log("产品明细导入")
    @ApiOperation(value = "产品明细导入")
    @TorchPerm("productDetails:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<ProductDetailsVO> util = new ExcelUtil<ProductDetailsVO>(ProductDetailsVO.class);
        List<ProductDetailsVO> list = util.importExcel(file.getInputStream());
        return productDetailsService.importProductDetails(list, unionColumns, true, "");
    }

    @Log("产品明细导入模板")
    @ApiOperation(value = "产品明细导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<ProductDetailsVO> util = new ExcelUtil<ProductDetailsVO>(ProductDetailsVO.class);
        util.importTemplateExcel(response, "产品明细数据");
    }

    @Log("根据Ids获取产品明细列表")
    @ApiOperation(value = "产品明细 根据Ids批量查询")
    @PostMapping(value = "/productDetailsList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getProductDetailsListByIds(@RequestBody List<String> ids) {
        return productDetailsService.selectProductDetailsListByIds(ids);
    }




}