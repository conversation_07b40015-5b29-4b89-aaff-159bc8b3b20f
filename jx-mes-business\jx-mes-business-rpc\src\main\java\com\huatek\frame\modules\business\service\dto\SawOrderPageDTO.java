package com.huatek.frame.modules.business.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huatek.frame.modules.system.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 监制验收工单分页查询请求DTO
 */
@Data
public class SawOrderPageDTO extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;


    /**
     * 订单类型
     **/
    @ApiModelProperty("订单类型")
    private String orderType;

    /**
     * 委托单位
     **/
    @ApiModelProperty("委托单位")
    private String entrustedUnit;

    /**
     * 要求完成日期
     **/
    @ApiModelProperty("要求完成日期")
    private String deadline;

    /**
     * 紧急程度
     **/
    @ApiModelProperty("紧急程度")
    private String urgencyLevel;

    /**
     * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
     * 完成日期
     **/
    @ApiModelProperty("完成日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completionDate;

    /**
     * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    private String manufacturer;


    /**
     * 订单合同号
     **/
    @ApiModelProperty("订单合同号")
    private String orderContractNumber;


    /**
     * 状态
     **/
    @ApiModelProperty("状态")
    private String status;


    /**
     * 页码
     */
    @ApiModelProperty("当前页码")
    private Integer page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty("每页显示大小")
    private Integer limit;
}
