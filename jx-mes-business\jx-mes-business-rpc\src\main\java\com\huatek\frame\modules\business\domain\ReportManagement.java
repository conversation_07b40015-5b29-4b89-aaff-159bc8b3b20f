package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 报告管理
* <AUTHOR>
* @date 2025-08-15
**/
@Setter
@Getter
@TableName("report_management")
public class ReportManagement implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 报告编号
     **/
    @TableField(value = "report_number"
    )
    private String reportNumber;

    
    /**
	 * 工单编号
     **/
    @TableField(value = "work_order_number"
    )
    private String workOrderNumber;

    /**
	 * 报告编制人
     **/
    @TableField(value = "preparer_of_the_report"
    )
    private String preparerOfTheReport;

    
    /**
	 * 编制时间
     **/
    @TableField(value = "compilation_time"
    )
    private Timestamp compilationTime;

    
    /**
	 * 报告状态
     **/
    @TableField(value = "report_status"
    )
    private String reportStatus;


    /**
	 * 附件
     **/
    @TableField(value = "attachment"
    )
    private String attachment;

    
    /**
	 * 申请人
     **/
    @TableField(value = "codex_torch_applicant"
    )
    private String codexTorchApplicant;

    
    /**
	 * 待审批人
     **/
    @TableField(value = "codex_torch_approver"
    )
    private String codexTorchApprover;

    
    /**
	 * 审批人列表
     **/
    @TableField(value = "codex_torch_approvers"
    )
    private String codexTorchApprovers;

    
    /**
	 * 流程状态
     **/
    @TableField(value = "codex_torch_approval_status"
    )
    private String codexTorchApprovalStatus;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}