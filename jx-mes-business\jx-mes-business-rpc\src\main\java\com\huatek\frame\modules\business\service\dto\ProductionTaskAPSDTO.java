package com.huatek.frame.modules.business.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huatek.frame.modules.system.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 生产任务DTO 实体类
* <AUTHOR>
* @date 2025-08-11
**/
@Data
@ApiModel("生产任务DTO实体类")
public class ProductionTaskAPSDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;


    @ApiModelProperty("stepId")
    private String stepId;

    @ApiModelProperty("工单Id")
    private String workOrderId;


    @ApiModelProperty("工序Id")
    private String processId;

    @ApiModelProperty("显示序号")
    private Integer displayNumber;

    /**
     * 计划开始时间
     **/
    @ApiModelProperty("计划开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp scheduledStartTime;

    /**
     * 计划结束时间
     **/
    @ApiModelProperty("计划结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp scheduledEndTime;



}