package com.huatek.frame.modules.business.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description 产值计算 对账 DTO 实体类
 * <AUTHOR>
 * @date 2025-08-22
 **/
@Data
@ApiModel("产值计算 对账 DTO实体类")
public class ProductionValueCalculationDuiZhangDTO {
    /**
     * 主键ID列表（批量操作）
     **/
    @ApiModelProperty("主键ID列表")
    private List<String> ids;



    /**
     * 对账价格
     **/
    @ApiModelProperty("对账价格")
    private BigDecimal settlementPrice;

    /**
     * 对账单号
     **/
    @ApiModelProperty("对账单号")
    private String billStatementNumber;
}
