package com.huatek.frame.modules.business.rest;

import com.alibaba.excel.EasyExcel;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationDetailExportVO;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationDuiZhangVO;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationVO;
import com.huatek.frame.modules.business.service.ProductionValueCalculationService;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationContractDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDuiZhangDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.response.TorchResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;



/**
* <AUTHOR>
* @date 2025-08-22
**/
@Api(tags = "产值计算管理")
@RestController
@RequestMapping("/api/productionValueCalculation")
public class ProductionValueCalculationController {

	@Autowired
    private ProductionValueCalculationService productionValueCalculationService;

	/**
	 * 产值计算列表
	 *
	 * @param dto 产值计算DTO 实体对象
	 * @return
	 */
    @Log("产值计算列表")
    @ApiOperation(value = "产值计算列表查询")
    @PostMapping(value = "/productionValueCalculationList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:list")
    public TorchResponse<List<ProductionValueCalculationVO>> query(@RequestBody ProductionValueCalculationDTO dto){
        return productionValueCalculationService.findProductionValueCalculationPage(dto);
    }

	/**
	 * 新增/修改产值计算
	 *
	 * @param productionValueCalculationDto 产值计算DTO实体对象
	 * @return
	 * @throws Exception
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改产值计算")
    @ApiOperation(value = "产值计算新增/修改操作")
    @PostMapping(value = "/productionValueCalculation", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:add#productionValueCalculation:edit")
    public TorchResponse add(@RequestBody ProductionValueCalculationDTO productionValueCalculationDto) throws Exception {
		// BeanValidatorFactory.validate(productionValueCalculationDto);
		return productionValueCalculationService.saveOrUpdate(productionValueCalculationDto);
	}
//
//	/**
//	 * 查询产值计算详情
//	 *
//	 * @param id 主键id
//	 * @return
//	 */
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//    @Log("产值计算详情")
//    @ApiOperation(value = "产值计算详情查询")
//    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("productionValueCalculation:detail")
//	public TorchResponse detail(@PathVariable(value = "id") String id) {
//		return productionValueCalculationService.findProductionValueCalculation(id);
//	}

	/**
	 * 删除产值计算
	 *
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除产值计算")
    @ApiOperation(value = "产值计算删除操作")
    @TorchPerm("productionValueCalculation:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return productionValueCalculationService.delete(ids);
	}

    @ApiOperation(value = "产值计算联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return productionValueCalculationService.getOptionsList(id);
	}





    @Log("产值计算导出")
    @ApiOperation(value = "产值计算导出")
    @TorchPerm("productionValueCalculation:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ProductionValueCalculationDTO dto)
    {
        List<ProductionValueCalculationVO> list = productionValueCalculationService.selectProductionValueCalculationList(dto);
        ExcelUtil<ProductionValueCalculationVO> util = new ExcelUtil<ProductionValueCalculationVO>(ProductionValueCalculationVO.class);
        util.exportExcel(response, list, "产值计算数据");
    }


    @Log("产值计算导入")
    @ApiOperation(value = "产值计算导入")
    @TorchPerm("productionValueCalculation:duizhang")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception {
        ExcelUtil<ProductionValueCalculationDuiZhangVO> util = new ExcelUtil<ProductionValueCalculationDuiZhangVO>(ProductionValueCalculationDuiZhangVO.class);
        List<ProductionValueCalculationDuiZhangVO> list = util.importExcel(file.getInputStream());
        return productionValueCalculationService.importProductionValueCalculation(list, unionColumns, true, "");
    }

    @Log("产值计算对账导入模板")
    @ApiOperation(value = "产值计算对账导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<ProductionValueCalculationDuiZhangVO> util = new ExcelUtil<ProductionValueCalculationDuiZhangVO>(ProductionValueCalculationDuiZhangVO.class);
        util.importTemplateExcel(response, "产值计算对账数据");
    }

    @Log("根据Ids获取产值计算列表")
    @ApiOperation(value = "产值计算 根据Ids批量查询")
    @PostMapping(value = "/productionValueCalculationList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getProductionValueCalculationListByIds(@RequestBody List<String> ids) {
        return productionValueCalculationService.selectProductionValueCalculationListByIds(ids);
    }

    /**
     * 产值计算明细
     *
     * @param productionValueCalculationId 主键id
     * @return
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("产值计算明细")
    @ApiOperation(value = "产值计算明细查询")
    @GetMapping(value = "/selectDetailsListByPVCId/{productionValueCalculationId}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:detail")
    public TorchResponse selectDetailsListByPVCId(@PathVariable(value = "productionValueCalculationId") String productionValueCalculationId) {
        return productionValueCalculationService.selectProdValCalcDetailsListByProductionValueCalculationId(productionValueCalculationId);
    }


    @Log("产值计算对账")
    @ApiOperation(value = "产值计算对账")
    @PostMapping(value = "/duizhang", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:duizhang")
    public TorchResponse duiZhang(@RequestBody ProductionValueCalculationDuiZhangDTO productionValueCalculationDuiZhangDTO) {
        return productionValueCalculationService.duiZhang(productionValueCalculationDuiZhangDTO);
    }

    @ApiOperation(value = "更新合同编号")
    @PostMapping(value = "/updateContract", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:contract")
    public TorchResponse updateContract(@RequestBody ProductionValueCalculationContractDTO productionValueCalculationContractDTO) {
        return productionValueCalculationService.updateContract(productionValueCalculationContractDTO);
    }

    /**
     * 生成产值计算
     *
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("生成产值计算")
    @ApiOperation(value = "生成产值计算")
    @PostMapping(value = "/generateCalculation", produces = { "application/json;charset=utf-8" })
    public TorchResponse generateCalculation() throws Exception {
        return productionValueCalculationService.generateCalculation();
    }
    /**
     * 内部价格重新核算
     *
     * @param ids 记录ID列表
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Log("内部价格重新核算")
    @ApiOperation(value = "内部价格重新核算")
    @PostMapping(value = "/recalculateInternalPrice", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:recalculateInternalPrice")
    public TorchResponse recalculateInternalPrice(@RequestBody List<String> ids) {
        return productionValueCalculationService.recalculateInternalPrice(ids);
    }

    /**
     * 客户价格重新核算
     *
     * @param ids 记录ID列表
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Log("客户价格重新核算")
    @ApiOperation(value = "客户价格重新核算")
    @PostMapping(value = "/recalculateCustomerPrice", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:recalculateCustomerPrice")
    public TorchResponse recalculateCustomerPrice(@RequestBody List<String> ids) {
        return productionValueCalculationService.recalculateCustomerPrice(ids);
    }

    /**
     * 查询产值计算明细导出数据
     *
     * @param ids 主键id列表
     * @return
     */
    @Log("查询产值计算明细导出数据")
    @ApiOperation(value = "查询产值计算明细导出数据")
    @PostMapping(value = "/selectDetailExportData", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:export")
    public TorchResponse selectDetailExportData(@RequestBody List<String> ids) {
        return productionValueCalculationService.selectDetailExportData(ids);
    }


    @Log("导出明细")
    @ApiOperation(value = "导出明细")
    @TorchPerm("productionValueCalculation:export")
    @PostMapping("/exportDetails")
    public void exportDetails(HttpServletResponse response, @RequestBody List<String> ids) throws IOException {
        // 获取数据
        TorchResponse<List<ProductionValueCalculationDetailExportVO>> result = productionValueCalculationService.selectDetailExportData(ids);
        List<ProductionValueCalculationDetailExportVO> list = result.getData().getData();

        if (list == null || list.isEmpty()) {
            throw new RuntimeException("没有数据可导出");
        }



        // 收集所有动态列名，保证表头不重复
        Set<String> dynamicHeaderSet = new LinkedHashSet<>();
        for (ProductionValueCalculationDetailExportVO vo : list) {
            if (vo.getDynamicColumns() != null) {
                dynamicHeaderSet.addAll(vo.getDynamicColumns().keySet());
            }
        }

        // 生成 Excel 数据
        List<List<String>> excelData = new ArrayList<>();
        for (ProductionValueCalculationDetailExportVO vo : list) {
            List<String> row = new ArrayList<>();
            // 主字段
            row.add(vo.getWorkOrderNumber() != null ? vo.getWorkOrderNumber() : "");
            row.add(vo.getProductName() != null ? vo.getProductName() : "");
            row.add(vo.getProductModel() != null ? vo.getProductModel() : "");
            row.add(vo.getBatchNumber() != null ? vo.getBatchNumber() : "");
            row.add(vo.getProductQuantity() != null ? vo.getProductQuantity().toString() : "0");
            row.add(vo.getSettlementUnit() != null ? vo.getSettlementUnit() : "");
            row.add(vo.getEntrustedUnit() != null ? vo.getEntrustedUnit() : "");
            row.add(vo.getNumberOfQualifiedProductions() != null ? vo.getNumberOfQualifiedProductions().toString() : "0");
            row.add(vo.getNumNonQualProd() != null ? vo.getNumNonQualProd().toString() : "0");
            row.add(vo.getNonQualityProcess() != null ? vo.getNonQualityProcess() : "");
            row.add(vo.getChargingStandardName() != null ? vo.getChargingStandardName() : "");
            row.add(vo.getCustomerAccountingPrice() != null ? String.valueOf(vo.getCustomerAccountingPrice()) : "0");
            row.add(vo.getTestType() != null ? vo.getTestType() : "");
            row.add(vo.getDateOfEntrustment() != null ? vo.getDateOfEntrustment().toString() : "");
            row.add(vo.getCompletionTime() != null ? vo.getCompletionTime().toString() : "");
            row.add(vo.getOrderInspectionNumber() != null ? vo.getOrderInspectionNumber() : "");
            row.add(vo.getWorkOrderInspectionNumber1() != null ? vo.getWorkOrderInspectionNumber1() : "");
            row.add(vo.getShippingDate() != null ? vo.getShippingDate().toString() : "");
            row.add(vo.getRecipientCustomer() != null ? vo.getRecipientCustomer() : "");
            row.add(vo.getCustomerPriceClassification() != null ? vo.getCustomerPriceClassification() : "");

            // 动态列
            for (String key : dynamicHeaderSet) {
                BigDecimal value = vo.getDynamicColumns() != null ? vo.getDynamicColumns().get(key) : null;
                row.add(value != null ? value.toString() : "0");
            }

            excelData.add(row);

            // 打印调试信息
            System.out.println("添加了一行数据：" + row);
        }

        // 表头顺序
        List<String> headers = new ArrayList<>();
        headers.addAll(Arrays.asList("工单编号", "产品名称", "产品型号", "生产批次", "产品数量",
                "结算单位", "委托单位", "合格数", "不合格数", "不合格工序",
                "收费标准", "合计费用", "试验类型", "委托日期", "完成日期",
                "订单送检编号", "工单送检编号", "发货时间", "快递接收人", "价格分类"));
        headers.addAll(dynamicHeaderSet); // 动态列放最后

        // 写 Excel
        try {
            System.out.println("准备写入数据，行数：" + excelData.size());
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "产值明细_" + System.currentTimeMillis() + ".xlsx";
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + java.net.URLEncoder.encode(fileName, "UTF-8"));

            // 构建表头
            List<List<String>> headerList = headers.stream()
                    .map(Collections::singletonList)
                    .collect(Collectors.toList());

            // 使用EasyExcel写入数据到响应流，并配置自适应宽度
            EasyExcel.write(response.getOutputStream())
                    .head(headerList)
                    .registerWriteHandler(new com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy())
                    .sheet("产值明细")
                    .doWrite(excelData);

            System.out.println("Excel 导出完成！");
        } catch (Exception e) {
            System.err.println("Excel 导出失败：" + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("导出Excel失败：" + e.getMessage());
        }
    }


}