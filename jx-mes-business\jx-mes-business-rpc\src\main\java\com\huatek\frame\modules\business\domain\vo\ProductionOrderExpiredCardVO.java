package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.modules.system.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* @description 标识卡 失效卡
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("标识卡 失效卡 VO实体类")
public class ProductionOrderExpiredCardVO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;


    @ApiModelProperty("编号")
    private String workOrderNumber;

    @ApiModelProperty("送筛单位")
    private String entrustedUnit;


    @ApiModelProperty("器件型号")
    private String productModel;

    @ApiModelProperty("生产批次")
    private String productionBatch;


    //production_order_result中的unqualified_quantity
    @ApiModelProperty("失效数量")
    private Integer failureQuantity;

    //每个工序的失效模式(存的是字典 字典code是production_task_failureMode  需要查询到字典值)的拼接，用逗号分隔
    @ApiModelProperty("备注")
    private String remark;

}