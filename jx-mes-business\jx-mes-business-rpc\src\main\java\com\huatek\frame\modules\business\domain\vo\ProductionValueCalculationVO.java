package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.service.dto.ProdValCalcDetailsDTO;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 产值计算VO实体类
 * @date 2025-08-22
 **/
@Data
@ApiModel("产值计算VO实体类")
public class ProductionValueCalculationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderNumber;


    /**
     * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productName;

    /**
     * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productModel;


    /**
     * 批次号
     **/
    @ApiModelProperty("批次号")
    @Excel(name = "生产批次",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String batchNumber;


    /**
     * 产品数量
     **/
    @ApiModelProperty("产品数量")
    @Excel(name = "产品数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer productQuantity;


    /**
     * 结算单位
     **/
    @ApiModelProperty("结算单位")
    @Excel(name = "结算单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String settlementUnit;


    /**
     * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String entrustedUnit;


    /**
     * 合格数
     **/
    @ApiModelProperty("合格数")
    @Excel(name = "合格数",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer numberOfQualifiedProductions;

    /**
     * 不合格数
     **/
    @ApiModelProperty("不合格数")
    @Excel(name = "不合格数",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer numNonQualProd;

    /**
     * 不合格工序
     **/
    @ApiModelProperty("不合格工序")
    @Excel(name = "不合格工序",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String nonQualityProcess;
    /**
     * 收费标准名称
     **/
    @ApiModelProperty("收费标准名称")
    @Excel(name = "收费标准",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String chargingStandardName;


    /**
     * 客户核算价格
     **/
    @ApiModelProperty("客户核算价格")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "合计费用",
            cellType = Excel.ColumnType.NUMERIC,
            type = Excel.Type.ALL)
    private BigDecimal customerAccountingPrice;
    /**
     * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testType;

    /**
     * 委托日期
     **/
    @ApiModelProperty("委托日期")
    @Excel(name = "委托日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfEntrustment;


    /**
     * 实际完成时间
     */
    @ApiModelProperty("实际完成时间")
    @Excel(name = "完成日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp completionTime;

    /**
     * 订单送检编号
     **/
    @ApiModelProperty("订单送检编号")
    @Excel(name = "订单送检编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String orderInspectionNumber;

    /**
     * 工单送检编号
     **/
    @ApiModelProperty("工单送检编号")
    @Excel(name = "工单送检编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderInspectionNumber1;



    /**
     * 发货日期
     */
    @ApiModelProperty("发货时间")
    @Excel(name = "发货时间",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date shippingDate;

    /**
     * 快递接收人
     */
    @ApiModelProperty("快递接收人")
    @Excel(name = "快递接收人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String recipientCustomer;
    /**
     * 客户价格分类
     **/
    @ApiModelProperty("客户价格分类")
    @Excel(name = "价格分类",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String customerPriceClassification;
    /**
     * 工单类型
     **/
    @ApiModelProperty("工单类型")
//    @Excel(name = "工单类型",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String ticketType;

    /**
     * 状态
     **/
    @ApiModelProperty("状态")
//    @Excel(name = "状态",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String status;

    /**
     * 类型
     **/
    @ApiModelProperty("类型")
//    @Excel(name = "类型",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String type;

    /**
     * 对账日期
     **/
    @ApiModelProperty("对账日期")
//    @Excel(name = "对账日期",
//            cellType = Excel.ColumnType.NUMERIC,
//            dateFormat = "yyyy-MM-dd",
//            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date settlementDate;

    /**
     * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
//    @Excel(name = "生产厂家",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String manufacturer;


    /**
     * 产品分类
     **/
    @ApiModelProperty("产品分类")
//    @Excel(name = "产品分类",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String productCategory;

    /**
     * 产品资料名称
     **/
    @ApiModelProperty("产品资料名称")
//    @Excel(name = "产品资料名称",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String productInformationName;

    /**
     * 产品资料
     **/
    @ApiModelProperty("产品资料")
//    @Excel(name = "产品资料",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String productInformation1;


    /**
     * 内部核算价格
     **/
    @ApiModelProperty("内部核算价格")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
//    @Excel(name = "内部核算价格",
//            cellType = Excel.ColumnType.NUMERIC,
//            type = Excel.Type.ALL)
    private BigDecimal internalAccountingPrice;

    /**
     * 折扣
     **/
    @ApiModelProperty("折扣")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
//    @Excel(name = "折扣",
//            cellType = Excel.ColumnType.NUMERIC,
//            type = Excel.Type.ALL)
    private BigDecimal discount;



    /**
     * 对账价格
     **/
    @ApiModelProperty("对账价格")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
//    @Excel(name = "对账价格",
//            cellType = Excel.ColumnType.NUMERIC,
//            type = Excel.Type.ALL)
    private BigDecimal settlementPrice;

    /**
     * 内部价格分类
     **/
    @ApiModelProperty("内部价格分类")
//    @Excel(name = "内部价格分类",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String internalPriceClassification;



    /**
     * 订单编号
     **/
    @ApiModelProperty("订单编号")
//    @Excel(name = "订单编号",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String orderNumber;



    /**
     * 对账单号
     **/
    @ApiModelProperty("对账单号")
//    @Excel(name = "对账单号",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String billStatementNumber;

    /**
     * 合同编号
     **/
    @ApiModelProperty("合同编号")
//    @Excel(name = "合同编号",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    private String contractNumber;

    /**
     * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
     * 更新人
     **/
    @ApiModelProperty("更新人")
//    @Excel(name = "更新人",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
     * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
     * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
     * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;
}