package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.ProductList;
import com.huatek.frame.modules.business.domain.ProductionOrder;
import com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderInfoVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderVO;
import com.huatek.frame.modules.business.service.dto.ProductionOrderDTO;
import org.apache.ibatis.annotations.Param;
import java.util.Map;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;

/**
* 待制工单mapper
* <AUTHOR>
* @date 2025-07-30
**/
public interface AwaitingProductionOrderMapper extends BaseMapper<ProductionOrder> {

     /**
	 * 待制工单分页
	 * @param dto
	 * @return
	 */
	Page<ProductionOrderVO> selectAwaitingProductionOrderPage(ProductionOrderDTO dto);

    /**
	 * 外键关联表: evaluation_order - order_number
     **/
    @ApiModelProperty("外键 evaluation_order - order_number")
	Page<SelectOptionsVO> selectOptionsByOrderNumber(String orderNumber);
    Map<String,String> selectDataLinkageByOrderNumber(@Param("order_number") String order_number);
    /**
	 * 外键关联表: customer_information_management - entrusted_unit
     **/
    @ApiModelProperty("外键 customer_information_management - entrusted_unit")
	Page<SelectOptionsVO> selectOptionsByEntrustedUnit(String entrustedUnit);
    /**
	 * 外键关联表: product_list1 - product_model
     **/
    @ApiModelProperty("外键 product_list1 - product_model")
	Page<SelectOptionsVO> selectOptionsByProductModel(String productModel);
    Map<String,String> selectDataLinkageByProductModel(@Param("product_model") String product_model);
    /**
	 * 外键关联表: product_list1 - product_model
     **/
    @ApiModelProperty("外键 product_list1 - product_model")
	Page<SelectOptionsVO> selectOptionsByPredecessorWorkOrder(String predecessorWorkOrder);
    /**
	 * 外键关联表: product_list1 - product_model
     **/
    @ApiModelProperty("外键 product_list1 - product_model")
	Page<SelectOptionsVO> selectOptionsByRelatedWorkOrder(String relatedWorkOrder);
    /**
	 * 外键关联表: sys_user - user_code
     **/
    @ApiModelProperty("外键 sys_user - user_code")
	Page<SelectOptionsVO> selectOptionsByResponsiblePerson(String responsiblePerson);

    /**
     * 根据条件查询待制工单列表
     *
     * @param dto 待制工单信息
     * @return 待制工单集合信息
     */
    List<ProductionOrderVO> selectAwaitingProductionOrderList(ProductionOrderDTO dto);

	/**
	 * 根据IDS查询待制工单列表
	 * @param ids
	 * @return
	 */
    List<ProductionOrderVO> selectAwaitingProductionOrderListByIds(@Param("ids") List<String> ids);

	/**
	 * 根据订单和产品型号，批次查询已有工单号
	 *
	 * @param evaluationOrderId
	 * @param productModel
	 * @param prefix
	 * @return
	 */
	ProductionOrder selectProductionOrderByModelAndBatch(@Param("evaluationOrderId") String evaluationOrderId, @Param("productModel")String productModel,@Param("prefix") String prefix);

	/**
	 * 根据id查询工单详情
	 * @param id
	 * @return
	 */
	ProductionOrderVO selectProductionOrderById(@Param("id")String id);

	/**
	 * 查询工型号同批次同生产厂家工单
	 *
	 * @param productlist
	 * @param codexTorchGroupId
	 * @return
	 */
    List<ProductionOrderVO> selectSameModelBatchManufacture(ProductList productlist, String codexTorchGroupId);

	List<ProductionOrderVO> selectSameModelBatchManufacture(@Param("productModel")String productModel,@Param("productionBatch") String productionBatch,@Param("manufacturer")  String manufacturer,
															@Param("codexTorchGroupId") String codexTorchGroupId,@Param("productionOrderId") String productionOrderId);

	/**
	 * 查询当前用角色
	 * @param currentUserId
	 * @return
	 */
    List<String> selectCurrentUserRoles(@Param("currentUserId")String currentUserId);

	Page<SelectOptionsVO> selectOptionsByProcessCode3(String s);

	Page<SelectOptionsVO> selectOptionsByAssoWoPredProc(@Param(value = "orderId") String orderId);

	Page<SelectOptionsVO> selectOptionsByWorkstation(String s);

	Page<SelectOptionsVO> selectOptionsByDeviceType(String s);

	Page<SelectOptionsVO> selectOptionsByProductInformation1(String s);

	Page<SelectOptionsVO> selectOptionsByTestingTeam(String s);

	Page<SelectOptionsVO> selectOptionsByGrouping(String s);

	/**
	 * 根据工单编号获取相关信息
	 * @param orderNumber
	 * @return
	 */
	AbnormalfeedbackVO getInfoByOrderNumber(@Param(value = "workOrderNumber") String orderNumber);

	/**
	 * 根据工单编号查询工单详细信息
	 * @param productionWorkOrderNumber 生产工单编号
	 * @return
	 */
	ProductionOrderInfoVO getDetailByOrderNumber(@Param(value = "productionWorkOrderNumber") String productionWorkOrderNumber);
}