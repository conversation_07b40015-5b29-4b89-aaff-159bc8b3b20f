package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProductionTaskCapabilityVO {
    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 能力编号
     **/
    @ApiModelProperty("能力编号")
    @Excel(name = "能力编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String capabilityNumber;
    /**
     * 操作卡
     **/
    @ApiModelProperty("操作卡")
    @Excel(name = "操作卡",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String operationCard;
}
