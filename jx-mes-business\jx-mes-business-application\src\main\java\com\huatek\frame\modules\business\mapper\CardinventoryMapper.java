package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.Cardinventory;
import com.huatek.frame.modules.business.domain.vo.CardinventoryVO;
import com.huatek.frame.modules.business.service.dto.CardinventoryDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 板卡存放mapper
* <AUTHOR>
* @date 2025-08-04
**/
public interface CardinventoryMapper extends BaseMapper<Cardinventory> {

     /**
	 * 板卡存放分页
	 * @param dto
	 * @return
	 */
	Page<CardinventoryVO> selectCardinventoryPage(CardinventoryDTO dto);


    /**
     * 根据条件查询板卡存放列表
     *
     * @param dto 板卡存放信息
     * @return 板卡存放集合信息
     */
    List<CardinventoryVO> selectCardinventoryList(CardinventoryDTO dto);

	/**
	 * 根据IDS查询板卡存放列表
	 * @param ids
	 * @return
	 */
    List<CardinventoryVO> selectCardinventoryListByIds(@Param("ids") List<String> ids);

}