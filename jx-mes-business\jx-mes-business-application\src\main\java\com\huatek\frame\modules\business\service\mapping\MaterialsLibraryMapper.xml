<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.MaterialsLibraryMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.material_code as materialCode,
		t.material_name as materialName,
		t.specification_model as specificationModel,
		t.inventory_quantity as inventoryQuantity,
		t.quantity_on_order_for_purchase as quantityOnOrderForPurchase,
		t.available_stock_quantity as availableStockQuantity,
		t.basic_unit_name as basicUnitName,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectMaterialsLibraryPage" parameterType="com.huatek.frame.modules.business.service.dto.MaterialsLibraryDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.MaterialsLibraryVO">
		select
		<include refid="Base_Column_List" />
			from materials_library t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="materialCode != null and materialCode != ''">
                    and t.material_code  like concat('%', #{materialCode} ,'%')
                </if>
                <if test="materialName != null and materialName != ''">
                    and t.material_name  like concat('%', #{materialName} ,'%')
                </if>
                <if test="specificationModel != null and specificationModel != ''">
                    and t.specification_model  like concat('%', #{specificationModel} ,'%')
                </if>
                <if test="inventoryQuantity != null and inventoryQuantity != ''">
                    and t.inventory_quantity  = #{inventoryQuantity}
                </if>
                <if test="quantityOnOrderForPurchase != null and quantityOnOrderForPurchase != ''">
                    and t.quantity_on_order_for_purchase  = #{quantityOnOrderForPurchase}
                </if>
                <if test="availableStockQuantity != null and availableStockQuantity != ''">
                    and t.available_stock_quantity  = #{availableStockQuantity}
                </if>
                <if test="basicUnitName != null and basicUnitName != ''">
                    and t.basic_unit_name  like concat('%', #{basicUnitName} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectMaterialsLibraryList" parameterType="com.huatek.frame.modules.business.service.dto.MaterialsLibraryDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.MaterialsLibraryVO">
		select
		<include refid="Base_Column_List" />
			from materials_library t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="materialCode != null and materialCode != ''">
                    and t.material_code  like concat('%', #{materialCode} ,'%')
                </if>
                <if test="materialName != null and materialName != ''">
                    and t.material_name  like concat('%', #{materialName} ,'%')
                </if>
                <if test="specificationModel != null and specificationModel != ''">
                    and t.specification_model  like concat('%', #{specificationModel} ,'%')
                </if>
                <if test="inventoryQuantity != null and inventoryQuantity != ''">
                    and t.inventory_quantity  = #{inventoryQuantity}
                </if>
                <if test="quantityOnOrderForPurchase != null and quantityOnOrderForPurchase != ''">
                    and t.quantity_on_order_for_purchase  = #{quantityOnOrderForPurchase}
                </if>
                <if test="availableStockQuantity != null and availableStockQuantity != ''">
                    and t.available_stock_quantity  = #{availableStockQuantity}
                </if>
                <if test="basicUnitName != null and basicUnitName != ''">
                    and t.basic_unit_name  like concat('%', #{basicUnitName} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectMaterialsLibraryListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.MaterialsLibraryVO">
		select
		<include refid="Base_Column_List" />
			from materials_library t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>