package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.ReportManagementVO;
import com.huatek.frame.modules.business.service.ReportManagementService;
import com.huatek.frame.modules.business.service.dto.ReportManagementDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;

import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-15
**/
@Api(tags = "报告管理管理")
@RestController
@RequestMapping("/api/reportManagement")
public class ReportManagementController {

	@Autowired
    private ReportManagementService reportManagementService;

	/**
	 * 报告管理列表
	 * 
	 * @param dto 报告管理DTO 实体对象
	 * @return
	 */
    @Log("报告管理列表")
    @ApiOperation(value = "报告管理列表查询")
    @PostMapping(value = "/reportManagementList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("reportManagement:list#reportManagementApproval:list")
    public TorchResponse<List<ReportManagementVO>> query(@RequestBody ReportManagementDTO dto){
        return reportManagementService.findReportManagementPage(dto);
    }

	/**
	 * 新增/修改报告管理
	 * 
	 * @param reportManagementDto 报告管理DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改报告管理")
    @ApiOperation(value = "报告管理新增/修改操作")
    @PostMapping(value = "/reportManagement", produces = { "application/json;charset=utf-8" })
    @TorchPerm("reportManagement:add#reportManagement:edit")
    public TorchResponse add(@RequestBody ReportManagementDTO reportManagementDto) throws Exception {
		// BeanValidatorFactory.validate(reportManagementDto);
		return reportManagementService.saveOrUpdate(reportManagementDto);
	}

	/**
	 * 查询报告管理详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("报告管理详情")
    @ApiOperation(value = "报告管理详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("reportManagement:detail#reportManagementApproval:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return reportManagementService.findReportManagement(id);
	}

	/**
	 * 删除报告管理
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除报告管理")
    @ApiOperation(value = "报告管理删除操作")
    @TorchPerm("reportManagement:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return reportManagementService.delete(ids);
	}

	/**
	 * 删除报告管理
	 *
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	@Log("提交报告管理")
	@ApiOperation(value = "提交报告管理")
	@TorchPerm("reportManagement:submit")
	@PostMapping(value = "/submit", produces = { "application/json;charset=utf-8" })
	public TorchResponse submit(@RequestBody String[] ids,@RequestHeader(value = Constant.TOKEN) String token) {
		return reportManagementService.submit(ids,token);
	}


	@ApiOperation(value = "报告管理联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return reportManagementService.getOptionsList(id);
	}

	/**
	 * 报告管理审批
	 *
	 * @param formApprovalDTO 表单审批DTO实体对象
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("rawtypes")
	@Log("报告管理审批")
	@ApiOperation(value = "报告管理审批")
	@PostMapping(value = "/approval", produces = { "application/json;charset=utf-8" })
	@TorchPerm("reportManagement:approval#reportManagementApproval:approval")
	public TorchResponse approve(@RequestBody FormApprovalDTO formApprovalDTO, @RequestHeader(value = Constant.TOKEN) String token) throws Exception {
		// BeanValidatorFactory.validate(formApprovalDTO);
		return reportManagementService.approve(formApprovalDTO, token);
	}

	/**
	 * 提交审批报告管理
	 *
	 * @param reportManagementDto 报告管理DTO实体对象
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("rawtypes")
	@Log("提交审批报告管理")
	@ApiOperation(value = "报告管理提交审批")
	@PostMapping(value = "/apply", produces = { "application/json;charset=utf-8" })
	@TorchPerm("reportManagement:add#reportManagement:edit")
	public TorchResponse apply(@RequestBody ReportManagementDTO reportManagementDto, @RequestHeader(value = Constant.TOKEN) String token) throws Exception {
		// BeanValidatorFactory.validate(reportManagementDto);
		return reportManagementService.apply(reportManagementDto, token);
	}



    @ApiOperation(value = "报告管理 自动填充数据查询")
    @GetMapping(value = "/linkageData/{linkageDataTableName}/{conditionalValue}", produces = { "application/json;charset=utf-8" })
    public TorchResponse getLinkageData(@PathVariable(value = "linkageDataTableName") String linkageDataTableName,
    									@PathVariable(value = "conditionalValue") String conditionalValue) {
		return reportManagementService.getLinkageData(linkageDataTableName, conditionalValue);
	}

    @Log("报告管理导出")
    @ApiOperation(value = "报告管理导出")
    @TorchPerm("reportManagement:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ReportManagementDTO dto)
    {
        List<ReportManagementVO> list = reportManagementService.selectReportManagementList(dto);
        ExcelUtil<ReportManagementVO> util = new ExcelUtil<ReportManagementVO>(ReportManagementVO.class);
        util.exportExcel(response, list, "报告管理数据");
    }

    @Log("报告管理导入")
    @ApiOperation(value = "报告管理导入")
    @TorchPerm("reportManagement:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<ReportManagementVO> util = new ExcelUtil<ReportManagementVO>(ReportManagementVO.class);
        List<ReportManagementVO> list = util.importExcel(file.getInputStream());
        return reportManagementService.importReportManagement(list, unionColumns, true, "");
    }

    @Log("报告管理导入模板")
    @ApiOperation(value = "报告管理导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<ReportManagementVO> util = new ExcelUtil<ReportManagementVO>(ReportManagementVO.class);
        util.importTemplateExcel(response, "报告管理数据");
    }

    @Log("根据Ids获取报告管理列表")
    @ApiOperation(value = "报告管理 根据Ids批量查询")
    @PostMapping(value = "/reportManagementList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getReportManagementListByIds(@RequestBody List<String> ids) {
        return reportManagementService.selectReportManagementListByIds(ids);
    }




}