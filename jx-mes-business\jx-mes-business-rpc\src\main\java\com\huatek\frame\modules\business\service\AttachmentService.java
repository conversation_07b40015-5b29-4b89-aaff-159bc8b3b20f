package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.service.dto.AttachmentDTO;
import com.huatek.frame.modules.business.domain.vo.AttachmentVO;
import java.util.List;

/**
* @description 设备原始数据Service
* <AUTHOR>
* @date 2025-08-18
**/
public interface AttachmentService {
    
    /**
	 * 分页查找查找 附件表
	 * 
	 * @param dto 附件表dto实体对象
	 * @return 
	 */
	TorchResponse<List<AttachmentVO>> findAttachmentPage(AttachmentDTO dto);

    /**
	 * 添加 \修改 附件表
	 * 
	 * @param attachmentDto 附件表dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(AttachmentDTO attachmentDto);
	
	/**
	 * 通过id查找附件表
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<AttachmentVO> findAttachment(String id);
	
	/**
	 * 删除 附件表
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 附件表
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<AttachmentVO>> getOptionsList(String id);

    /**
     * 根据条件查询附件表列表
     *
     * @param dto 附件表信息
     * @return 附件表集合信息
     */
    List<AttachmentVO> selectAttachmentList(AttachmentDTO dto);

    /**
     * 导入附件表数据
     *
     * @param attachmentList 附件表数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importAttachment(List<AttachmentVO> attachmentList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取附件表数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectAttachmentListByIds(List<String> ids);
	
	/**
	 * 根据工单编码查询附件列表
	 * @param workOrderCode 工单编码
	 * @return 附件列表
	 */
	TorchResponse<List<AttachmentVO>> selectAttachmentListByWorkOrderCode(String workOrderCode);
	
	/**
	 * 根据工单编码和工序编码查询附件列表
	 * @param workOrderCode 工单编码
	 * @param processCode 工序编码
	 * @return 附件列表
	 */
	TorchResponse<List<AttachmentVO>> selectAttachmentListByWorkOrderAndProcess(String workOrderCode, String processCode);
}
