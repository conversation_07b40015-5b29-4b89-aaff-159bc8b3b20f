package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.modules.system.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* @description 标识卡
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("标识卡 DPA VO实体类")
public class ProductionOrderIDCardDPAVO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;


    @ApiModelProperty("编号")
    private String workOrderNumber;

    @ApiModelProperty("委托单位")
    private String entrustedUnit;
    //固定为DPA
    @ApiModelProperty("委托类型")
    private String entrustedType;


    @ApiModelProperty("器件型号")
    private String productModel;

    @ApiModelProperty("生产批次")
    private String productionBatch;

    //production_order_result中的inspection_conclusion
    @ApiModelProperty("结论")
    private String conclusion;


}