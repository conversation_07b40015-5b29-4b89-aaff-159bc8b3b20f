package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;
import com.huatek.frame.modules.business.domain.CapabilityVerification;
import  com.huatek.frame.modules.business.domain.vo.CapabilityVerificationVO;
import com.huatek.frame.modules.business.service.dto.CapabilityVerificationDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 能力核验mapper
* <AUTHOR>
* @date 2025-08-05
**/
public interface CapabilityVerificationMapper extends BaseMapper<CapabilityVerification> {

     /**
	 * 能力核验分页
	 * @param dto
	 * @return
	 */
	Page<CapabilityVerificationVO> selectCapabilityVerificationPage(CapabilityVerificationDTO dto);

    /**
	 * 外键关联表: customer_information_management - entrusted_unit
     **/
    @ApiModelProperty("外键 customer_information_management - entrusted_unit")
	Page<SelectOptionsVO> selectOptionsByEntrustedUnit(String entrustedUnit);

    /**
     * 根据条件查询能力核验列表
     *
     * @param dto 能力核验信息
     * @return 能力核验集合信息
     */
    List<CapabilityVerificationVO> selectCapabilityVerificationList(CapabilityVerificationDTO dto);

	/**
	 * 根据IDS查询能力核验列表
	 * @param ids
	 * @return
	 */
    List<CapabilityVerificationVO> selectCapabilityVerificationListByIds(@Param("ids") List<String> ids);

	/**
	 * 批量更新
	 */
	void batchUpdateCapabilityVerification(@Param("param") List<CapabilityVerificationVO> capabilityVerifications);

	/**
	 * 获取单个能力核验详情
	 * @param id 能力核验id
	 * @return
	 */
    CapabilityVerificationVO findCapabilityVerification(@Param("id") String id);
}