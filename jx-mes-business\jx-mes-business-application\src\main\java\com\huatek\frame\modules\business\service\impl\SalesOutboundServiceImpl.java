package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.SalesOutbound;
import com.huatek.frame.modules.business.domain.vo.SalesOutboundVO;
import com.huatek.frame.modules.business.mapper.SalesOutboundMapper;
import com.huatek.frame.modules.business.service.SalesOutboundService;
import com.huatek.frame.modules.business.service.dto.SalesOutboundDTO;
import java.sql.Date;
import org.springframework.util.CollectionUtils;



/**
 * 销售出库 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-06
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "salesOutbound")
//@RefreshScope
@Slf4j
public class SalesOutboundServiceImpl implements SalesOutboundService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private SalesOutboundMapper salesOutboundMapper;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<SalesOutboundVO>> findSalesOutboundPage(SalesOutboundDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<SalesOutboundVO> salesOutbounds = salesOutboundMapper.selectSalesOutboundPage(dto);
		TorchResponse<List<SalesOutboundVO>> response = new TorchResponse<List<SalesOutboundVO>>();
		response.getData().setData(salesOutbounds);
		response.setStatus(200);
		response.getData().setCount(salesOutbounds.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(SalesOutboundDTO salesOutboundDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(salesOutboundDto.getCodexTorchDeleted())) {
            salesOutboundDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = salesOutboundDto.getId();
		SalesOutbound entity = new SalesOutbound();
        BeanUtils.copyProperties(salesOutboundDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			salesOutboundMapper.insert(entity);
		} else {
			salesOutboundMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<SalesOutboundVO> findSalesOutbound(String id) {
		SalesOutboundVO vo = new SalesOutboundVO();
		if (!HuatekTools.isEmpty(id)) {
			SalesOutbound entity = salesOutboundMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<SalesOutboundVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<SalesOutbound> salesOutboundList = salesOutboundMapper.selectBatchIds(Arrays.asList(ids));
        for (SalesOutbound salesOutbound : salesOutboundList) {
            salesOutbound.setCodexTorchDeleted(Constant.DEFAULT_YES);
            salesOutboundMapper.updateById(salesOutbound);
        }
		//salesOutboundMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "sales_outbound", convertorFields = "")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<SalesOutboundVO> selectSalesOutboundList(SalesOutboundDTO dto) {
        return salesOutboundMapper.selectSalesOutboundList(dto);
    }

    /**
     * 导入销售出库数据
     *
     * @param salesOutboundList 销售出库数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "sales_outbound", convertorFields = "")
    public TorchResponse importSalesOutbound(List<SalesOutboundVO> salesOutboundList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(salesOutboundList) || salesOutboundList.size() == 0) {
            throw new ServiceException("导入销售出库数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SalesOutboundVO vo : salesOutboundList) {
            try {
                SalesOutbound salesOutbound = new SalesOutbound();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, salesOutbound);
                QueryWrapper<SalesOutbound> wrapper = new QueryWrapper();
                SalesOutbound oldSalesOutbound = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = SalesOutboundVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<SalesOutbound> oldSalesOutboundList = salesOutboundMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldSalesOutboundList) && oldSalesOutboundList.size() > 1) {
                        salesOutboundMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldSalesOutboundList) && oldSalesOutboundList.size() == 1) {
                        oldSalesOutbound = oldSalesOutboundList.get(0);
                    }
                }
                if (StringUtils.isNull(oldSalesOutbound)) {
                    BeanValidators.validateWithException(validator, vo);
                    salesOutboundMapper.insert(salesOutbound);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、委托单位 " + vo.getEntrustedUnit() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldSalesOutbound, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    salesOutboundMapper.updateById(oldSalesOutbound);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、委托单位 " + vo.getEntrustedUnit() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、委托单位 " + vo.getEntrustedUnit() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、委托单位 " + vo.getEntrustedUnit() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(SalesOutboundVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getWorkOrderNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工单编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getRecipientCustomer())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>客户收件人不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getRecipientPhone())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>收件电话不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getRecipientAddress())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>收件人地址不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getExpressCompany())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>快递公司不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getExpressWaybillNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>快递单号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getShippingDate())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>发货日期不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectSalesOutboundListByIds(List<String> ids) {
        List<SalesOutboundVO> salesOutboundList = salesOutboundMapper.selectSalesOutboundListByIds(ids);

		TorchResponse<List<SalesOutboundVO>> response = new TorchResponse<List<SalesOutboundVO>>();
		response.getData().setData(salesOutboundList);
		response.setStatus(200);
		response.getData().setCount((long)salesOutboundList.size());
		return response;
    }

    @Override
    public TorchResponse<String> executeOutbound(SalesOutboundDTO dto) {
        return saveOrUpdate(dto);
    }

    @Override
    public int insertIntoSalesOutboundBatch(List<SalesOutbound> salesOutboundList) {
        int result = 0;
        if(null!=salesOutboundList&&salesOutboundList.size()>0){
            result = salesOutboundMapper.batchInsert(salesOutboundList);
        }
        return result;
    }


}
