package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.constant.DicConstant;

import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;

import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.ProductInformationManagement;
import com.huatek.frame.modules.business.domain.vo.ProductInformationManagementVO;
import com.huatek.frame.modules.business.mapper.ProductInformationManagementMapper;
import com.huatek.frame.modules.business.service.ProductInformationManagementService;
import com.huatek.frame.modules.business.service.dto.ProductInformationManagementDTO;
import org.springframework.util.CollectionUtils;



/**
 * 产品资料管理 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "productInformationManagement")
//@RefreshScope
@Slf4j
public class ProductInformationManagementServiceImpl implements ProductInformationManagementService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private ProductInformationManagementMapper productInformationManagementMapper;
    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public ProductInformationManagementServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ProductInformationManagementVO>> findProductInformationManagementPage(ProductInformationManagementDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ProductInformationManagementVO> productInformationManagements = productInformationManagementMapper.selectProductInformationManagementPage(dto);
		TorchResponse<List<ProductInformationManagementVO>> response = new TorchResponse<List<ProductInformationManagementVO>>();
		response.getData().setData(productInformationManagements);
		response.setStatus(200);
		response.getData().setCount(productInformationManagements.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ProductInformationManagementDTO productInformationManagementDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(productInformationManagementDto.getCodexTorchDeleted())) {
            productInformationManagementDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = productInformationManagementDto.getId();
		ProductInformationManagement entity = new ProductInformationManagement();
        BeanUtils.copyProperties(productInformationManagementDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
            //查询你编码前准获取编码
            TorchResponse response = codeManagementService.getOrderNumber("CPZL-");
            entity.setNumber(response.getData().getData().toString());
            entity.setVersion("1");
			productInformationManagementMapper.insert(entity);
		} else {
            if(StringUtils.isNotEmpty(productInformationManagementDto.getIsUpgrade()) && productInformationManagementDto.getIsUpgrade().equals(DicConstant.CommonDic.DEFAULT_TRUE)){
                entity.setVersion((Integer.valueOf(productInformationManagementDto.getVersion()+1)).toString());
            }
			productInformationManagementMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        ProductInformationManagementVO vo = new ProductInformationManagementVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductInformationManagementVO> findProductInformationManagement(String id) {
		ProductInformationManagementVO vo = new ProductInformationManagementVO();
		if (!HuatekTools.isEmpty(id)) {
			ProductInformationManagement entity = productInformationManagementMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<ProductInformationManagementVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		productInformationManagementMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "product_information_management", convertorFields = "")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductInformationManagementVO> selectProductInformationManagementList(ProductInformationManagementDTO dto) {
        return productInformationManagementMapper.selectProductInformationManagementList(dto);
    }

    /**
     * 导入产品资料管理数据
     *
     * @param productInformationManagementList 产品资料管理数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "product_information_management", convertorFields = "")
    public TorchResponse importProductInformationManagement(List<ProductInformationManagementVO> productInformationManagementList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(productInformationManagementList) || productInformationManagementList.size() == 0) {
            throw new ServiceException("导入产品资料管理数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProductInformationManagementVO vo : productInformationManagementList) {
            try {
                ProductInformationManagement productInformationManagement = new ProductInformationManagement();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, productInformationManagement);
                QueryWrapper<ProductInformationManagement> wrapper = new QueryWrapper();
                ProductInformationManagement oldProductInformationManagement = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = ProductInformationManagementVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<ProductInformationManagement> oldProductInformationManagementList = productInformationManagementMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldProductInformationManagementList) && oldProductInformationManagementList.size() > 1) {
                        productInformationManagementMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldProductInformationManagementList) && oldProductInformationManagementList.size() == 1) {
                        oldProductInformationManagement = oldProductInformationManagementList.get(0);
                    }
                }
                if (StringUtils.isNull(oldProductInformationManagement)) {
                    BeanValidators.validateWithException(validator, vo);
                    productInformationManagementMapper.insert(productInformationManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、文件编号 " + vo.getFileNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldProductInformationManagement, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    productInformationManagementMapper.updateById(oldProductInformationManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、文件编号 " + vo.getFileNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、文件编号 " + vo.getFileNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、文件编号 " + vo.getFileNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(ProductInformationManagementVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getFileNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>文件编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getFileName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>文件名称不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getProductModel())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>产品型号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getManufacturer())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>生产厂家不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getAttachment())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>附件不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProductInformationManagementListByIds(List<String> ids) {
        List<ProductInformationManagementVO> productInformationManagementList = productInformationManagementMapper.selectProductInformationManagementListByIds(ids);

		TorchResponse<List<ProductInformationManagementVO>> response = new TorchResponse<List<ProductInformationManagementVO>>();
		response.getData().setData(productInformationManagementList);
		response.setStatus(200);
		response.getData().setCount((long)productInformationManagementList.size());
		return response;
    }

    @Override
    public TorchResponse submitUpgrade(ProductInformationManagementDTO productInformationManagementDto) {
        ProductInformationManagement ProductInformationManagement = productInformationManagementMapper.selectById(productInformationManagementDto.getId());
        ProductInformationManagement.setAttachment(productInformationManagementDto.getAttachment());
        ProductInformationManagement.setVersion(String.valueOf(Integer.valueOf(ProductInformationManagement.getVersion())+1));
        productInformationManagementMapper.updateById(ProductInformationManagement);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(200);
        return response;
    }


}
