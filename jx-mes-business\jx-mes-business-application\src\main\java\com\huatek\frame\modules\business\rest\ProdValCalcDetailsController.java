package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.ProdValCalcDetailsVO;
import com.huatek.frame.modules.business.service.ProdValCalcDetailsService;
import com.huatek.frame.modules.business.service.dto.ProdValCalcDetailsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-22
**/
@Api(tags = "产值计算明细管理")
@RestController
@RequestMapping("/api/prodValCalcDetails")
public class ProdValCalcDetailsController {

	@Autowired
    private ProdValCalcDetailsService prodValCalcDetailsService;

	/**
	 * 产值计算明细列表
	 * 
	 * @param dto 产值计算明细DTO 实体对象
	 * @return
	 */
    @Log("产值计算明细列表")
    @ApiOperation(value = "产值计算明细列表查询")
    @PostMapping(value = "/prodValCalcDetailsList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("prodValCalcDetails:list")
    public TorchResponse<List<ProdValCalcDetailsVO>> query(@RequestBody ProdValCalcDetailsDTO dto){
        return prodValCalcDetailsService.findProdValCalcDetailsPage(dto);
    }

	/**
	 * 新增/修改产值计算明细
	 * 
	 * @param prodValCalcDetailsDto 产值计算明细DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改产值计算明细")
    @ApiOperation(value = "产值计算明细新增/修改操作")
    @PostMapping(value = "/prodValCalcDetails", produces = { "application/json;charset=utf-8" })
    @TorchPerm("prodValCalcDetails:add#prodValCalcDetails:edit")
    public TorchResponse add(@RequestBody ProdValCalcDetailsDTO prodValCalcDetailsDto) throws Exception {
		// BeanValidatorFactory.validate(prodValCalcDetailsDto);
		return prodValCalcDetailsService.saveOrUpdate(prodValCalcDetailsDto);
	}

	/**
	 * 查询产值计算明细详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("产值计算明细详情")
    @ApiOperation(value = "产值计算明细详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("prodValCalcDetails:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return prodValCalcDetailsService.findProdValCalcDetails(id);
	}

	/**
	 * 删除产值计算明细
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除产值计算明细")
    @ApiOperation(value = "产值计算明细删除操作")
    @TorchPerm("prodValCalcDetails:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return prodValCalcDetailsService.delete(ids);
	}

    @ApiOperation(value = "产值计算明细联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return prodValCalcDetailsService.getOptionsList(id);
	}





    @Log("产值计算明细导出")
    @ApiOperation(value = "产值计算明细导出")
    @TorchPerm("prodValCalcDetails:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ProdValCalcDetailsDTO dto)
    {
        List<ProdValCalcDetailsVO> list = prodValCalcDetailsService.selectProdValCalcDetailsList(dto);
        ExcelUtil<ProdValCalcDetailsVO> util = new ExcelUtil<ProdValCalcDetailsVO>(ProdValCalcDetailsVO.class);
        util.exportExcel(response, list, "产值计算明细数据");
    }

    @Log("产值计算明细导入")
    @ApiOperation(value = "产值计算明细导入")
    @TorchPerm("prodValCalcDetails:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<ProdValCalcDetailsVO> util = new ExcelUtil<ProdValCalcDetailsVO>(ProdValCalcDetailsVO.class);
        List<ProdValCalcDetailsVO> list = util.importExcel(file.getInputStream());
        return prodValCalcDetailsService.importProdValCalcDetails(list, unionColumns, true, "");
    }

    @Log("产值计算明细导入模板")
    @ApiOperation(value = "产值计算明细导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<ProdValCalcDetailsVO> util = new ExcelUtil<ProdValCalcDetailsVO>(ProdValCalcDetailsVO.class);
        util.importTemplateExcel(response, "产值计算明细数据");
    }

    @Log("根据Ids获取产值计算明细列表")
    @ApiOperation(value = "产值计算明细 根据Ids批量查询")
    @PostMapping(value = "/prodValCalcDetailsList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getProdValCalcDetailsListByIds(@RequestBody List<String> ids) {
        return prodValCalcDetailsService.selectProdValCalcDetailsListByIds(ids);
    }




}