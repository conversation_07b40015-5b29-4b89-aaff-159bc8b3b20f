<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.AwaitingProductionOrderMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.work_order_number as workOrderNumber,
		t.order_number as orderNumber,
		t.test_methodology as testMethodology,
		t.predecessor_work_order as predecessorWorkOrder,
		t.related_work_order as relatedWorkOrder,
		t.pda as pda,
		t.whether_to_include_in_scheduling as whetherToIncludeInScheduling,
		t.estimated_completion_time as estimatedCompletionTime,
		t.quantity as quantity,
		t.attachment as attachment,
		t.work_order_status as workOrderStatus,
        t.report_status as reportStatus,
<!--		t.responsible_person as responsible<PERSON>erson,-->
<!--        t. prepared_by as preparedBy,-->
		t.production_stage as productionStage,
		t.wtstabr as wtstabr,
		t.whether_to_enter_components as whetherToEnterComponents,
		t.whether_to_enter_documents as whetherToEnterDocuments,
		t.completion_time as completionTime,
		t.wmfcnr as wmfcnr,
		t.irretrievable_reason as irretrievableReason,
        t.package_form as packageForm,
        t.source_order as sourceOrder,
        t.non_aging_reason as nonAgingReason,
        t.is_calculation as isCalculation,
        t.calculation_time as calculationTime,
        t.codex_torch_applicant as codexTorchApplicant,
        t.codex_torch_approver as codexTorchApprover,
        t.codex_torch_approvers as codexTorchApprovers,
        t.codex_torch_approval_status as codexTorchApprovalStatus,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectAwaitingProductionOrderPage" parameterType="com.huatek.frame.modules.business.service.dto.ProductionOrderDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
		select distinct
		<include refid="Base_Column_List" />,cim.entrusted_unit as entrustedUnit,pl.product_model as productModel,pl.manufacturer as manufacturer,
            pl.product_name as productName,pl.production_batch as prodctionBatch,pl.production_batch as productionBatch,
        pc.category_name as productCategory,pl.standard_specification_id as standardSpecificationId ,
        (SELECT GROUP_CONCAT(ss.controlled_number)
        FROM standard_specification ss
        WHERE FIND_IN_SET(ss.id, pl.standard_specification_id) > 0) AS standardSpecificationNumber,
            sg.group_name as groupName,
        (SELECT GROUP_CONCAT(ss.attachment)
        FROM standard_specification ss
        WHERE FIND_IN_SET(ss.id, pl.standard_specification_id) > 0) AS standardSpecificationAttachment,
            po_pre.work_order_number as predecessorWorkOrderNumber,po_rel.work_order_number as relatedWorkOrderNumber,
            pl.deadline as deadline,pl.task_level as taskLevel,su.user_name  as responsiblePerson,su1.user_name as preparedBy,pl.test_type as testType
			from production_order t left join evaluation_order eo on t.order_number =eo.order_number
            left join customer_information_management cim on eo.customer_id  = cim.id
            left join product_list pl on t.product  = pl.id
            left join sys_user su on t.responsible_person  = su.id
            left join sys_user su1 on t.prepared_by = su1.id
            left join sys_group sg on t.codex_torch_group_id =sg.id
            LEFT JOIN production_order po_pre ON t.predecessor_work_order = po_pre.id
            LEFT JOIN production_order po_rel ON t.related_work_order = po_rel.id
            left join product_category pc on pl.product_category  = pc.id
        left join standard_specification ss2 on  FIND_IN_SET(ss2.id, pl.standard_specification_id) > 0
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and eo.id  = #{orderNumber}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and cim.id  = #{entrustedUnit}
                </if>

                <if test="productModel != null and productModel != ''">
                    and pl.product_model  = #{productModel}
                </if>
                <if test="productName != null and productName != ''">
                    and pl.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productionBatch != null and productionBatch != ''">
                    and t.production_batch  like concat('%', #{productionBatch} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and pl.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and  pc.category_name  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="taskLevel != null and taskLevel != ''">
                    and pl.task_level  = #{taskLevel}
                </if>
                <if test="responsiblePerson  != null and responsiblePerson != ''">
                    and t.responsible_person  = #{responsiblePerson}
                </if>

                <if test="standardSpecificationNumber != null and standardSpecificationNumber != ''">
                    and (ss2.controlled_number  like concat('%', #{standardSpecificationNumber} ,'%')
                    or ss2.specification_number  like concat('%', #{standardSpecificationNumber} ,'%'))
                </if>

                <if test="testType != null and testType != ''">
                    and pl.test_type  like concat('%', #{testType} ,'%')
                </if>
                <if test="deadline != null">
                    and pl.deadline  = #{deadline}
                </if>
                <if test="deadlineStart != null">
                    and pl.deadline  &gt;= #{deadlineStart}
                </if>
                <if test="deadlineEnd != null">
                    and pl.deadline  &lt;= #{deadlineEnd}
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
                <if test="menuType != null and menuType != '' and menuType =='awaiting'">
                    and t.test_methodology !='1' and t.work_order_status in ('3','0','9','1') and pl.test_type not in ('2','3') and t.test_methodology !='1'
                </if>
                <if test="menuType != null and menuType != '' and menuType =='approve'">
                    and t.work_order_status !='6' and t.source_order is null
                </if>
                <if test="menuType != null and menuType != '' and menuType =='complete'">
                    and t.work_order_status in ('6','7')
                </if>
                <if test="workOrderStatus != null and workOrderStatus != ''">
                    and t.work_order_status  = #{workOrderStatus}
                </if>

                <if test="productionStage != null and productionStage != ''">
                    and t.production_stage  = #{productionStage}
                </if>

                <if test="completionTime != null and completionTime != ''">
                    and DATE(t.completion_time)  = #{completionTime}
                </if>
                <if test="wmfcnr != null and wmfcnr != ''">
                    and t.wmfcnr  = #{wmfcnr}
                </if>
                <if test="irretrievableReason != null and irretrievableReason != ''">
                    and t.irretrievable_reason  = #{irretrievableReason}
                </if>
                <if test="codexTorchApprovalStatus != null and codexTorchApprovalStatus != ''">
                    and (t.codex_torch_approval_status  like concat('%', #{codexTorchApprovalStatus} ,'%')
                    or t.codex_torch_approval_status  REGEXP #{codexTorchApprovalStatus})
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null and codexTorchCreateDatetime != ''">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null and codexTorchCreateDatetime != ''">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                <if test="menuType != null and menuType != '' and menuType =='awaiting'">
                   order by FIELD(t.work_order_status, '3', '0', '9', '1'),t.completion_time asc
                </if>
                <if test="menuType != null and menuType != '' and menuType =='complete'">
                    order by FIELD(t.work_order_status, '7', '6'),t.completion_time asc
                </if>
                ${params.dataScope}
            </where>
<!--        order by t.work_order_status-->
	</select>
     <select id="selectOptionsByOrderNumber" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.order_number label,
        	t.id value
        from evaluation_order t
        WHERE  t.codex_torch_deleted = '0'
     </select>
     <select id="selectDataLinkageByOrderNumber" parameterType="String"
             resultType="java.util.Map">
        select
            t.engineering_code as engineeringCode,
            t.order_remarks as orderRemarks
        from evaluation_order t
        WHERE t.order_number = #{order_number}
            and t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByEntrustedUnit" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.entrusted_unit label,
        	t.id value
        from customer_information_management t
        WHERE t.entrusted_unit != '' and t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByProductModel" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
         select
         distinct
         t.product_model  label,
         t.product_model value
         from product_list t
         WHERE  t.codex_torch_deleted = '0' and t.product_model is not null
     </select>
     <select id="selectDataLinkageByProductModel" parameterType="String"
             resultType="java.util.Map">
        select
            t.product_name as productName,
            t.production_batch as productionBatch,
            t.manufacturer as manufacturer,
            t.inspection_quantity2 as inspectionQuantity2,
            t.sample_total_count as sampleTotalCount,
            t.standard_specification_number as standardSpecificationNumber,
            t.experiment_project as experimentProject,
            t.work_order_inspection_number1 as workOrderInspectionNumber1,
            t.quality_grade as qualityGrade,
            t.test_type as testType,
            t.deadline as deadline
        from product_list t
        WHERE t.product_model = #{product_model}
            and t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByPredecessorWorkOrder" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
             t.work_order_number  label,
             t.id value
         from production_order t
         WHERE  t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByRelatedWorkOrder" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
             t.work_order_number  label,
             t.id value
         from production_order t
         WHERE  t.codex_torch_deleted = '0'
     </select>
     <select id="selectOptionsByResponsiblePerson" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.user_name label,
        	t.id value
        from sys_user t
        WHERE  t.deleted = '0'
     </select>

    <select id="selectAwaitingProductionOrderList" parameterType="com.huatek.frame.modules.business.service.dto.ProductionOrderDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
		select
        <include refid="Base_Column_List" />,cim.entrusted_unit as entrustedUnit,pl.product_model as productModel,pl.manufacturer as manufacturer,
        pl.product_name as productName,pl.production_batch as prodctionBatch,pl.production_batch as productionBatch,
        pc.category_name as productCategory,pl.standard_specification_id as standardSpecificationId ,
        (SELECT GROUP_CONCAT(ss.controlled_number)
        FROM standard_specification ss
        WHERE FIND_IN_SET(ss.id, pl.standard_specification_id) > 0) AS standardSpecificationNumber,
        sg.group_name as groupName,
        (SELECT GROUP_CONCAT(ss.attachment)
        FROM standard_specification ss
        WHERE FIND_IN_SET(ss.id, pl.standard_specification_id) > 0) AS standardSpecificationAttachment,
        po_pre.work_order_number as predecessorWorkOrderNumber,po_rel.work_order_number as relatedWorkOrderNumber,
        pl.deadline as deadline,pl.task_level as taskLevel,su.user_name  as responsiblePerson,su1.user_name as preparedBy,pl.test_type as testType
        from production_order t left join evaluation_order eo on t.order_number =eo.order_number
        left join customer_information_management cim on eo.customer_id  = cim.id
        left join product_list pl on t.product  = pl.id
        left join sys_user su on t.responsible_person  = su.id
        left join sys_user su1 on t.prepared_by = su1.id
        left join sys_group sg on t.codex_torch_group_id =sg.id
        LEFT JOIN production_order po_pre ON t.predecessor_work_order = po_pre.id
        LEFT JOIN production_order po_rel ON t.related_work_order = po_rel.id
        left join product_category pc on pl.product_category  = pc.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and eo.id  = #{orderNumber}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  = #{entrustedUnit}
                </if>
                <if test="engineeringCode != null and engineeringCode != ''">
                    and t.engineering_code  like concat('%', #{engineeringCode} ,'%')
                </if>
                <if test="orderRemarks != null and orderRemarks != ''">
                    and t.order_remarks  = #{orderRemarks}
                </if>
                <if test="reportRequirements != null and reportRequirements != ''">
                    and t.report_requirements  = #{reportRequirements}
                </if>
                <if test="reportFormat != null and reportFormat != ''">
                    and t.report_format REGEXP #{reportFormat}
                </if>
                <if test="dataReqERep != null and dataReqERep != ''">
                    and t.data_req_e_rep  = #{dataReqERep}
                </if>
                <if test="dataReqsPapereport != null and dataReqsPapereport != ''">
                    and t.data_reqs_papereport  = #{dataReqsPapereport}
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  = #{productModel}
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productionBatch != null and productionBatch != ''">
                    and t.production_batch  like concat('%', #{productionBatch} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="inspectionQuantity2 != null and inspectionQuantity2 != ''">
                    and t.inspection_quantity2  = #{inspectionQuantity2}
                </if>
                <if test="sampleTotalCount != null and sampleTotalCount != ''">
                    and t.sample_total_count  = #{sampleTotalCount}
                </if>
                <if test="taskLevel != null and taskLevel != ''">
                    and t.task_level  = #{taskLevel}
                </if>
                <if test="standardSpecificationNumber != null and standardSpecificationNumber != ''">
                    and t.standard_specification_number  like concat('%', #{standardSpecificationNumber} ,'%')
                </if>
                <if test="experimentProject != null and experimentProject != ''">
                    and t.experiment_project  like concat('%', #{experimentProject} ,'%')
                </if>
                <if test="workOrderInspectionNumber1 != null and workOrderInspectionNumber1 != ''">
                    and t.work_order_inspection_number1  like concat('%', #{workOrderInspectionNumber1} ,'%')
                </if>
                <if test="packageForm != null and packageForm != ''">
                    and t.package_form  like concat('%', #{packageForm} ,'%')
                </if>
                <if test="qualityGrade != null and qualityGrade != ''">
                    and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  like concat('%', #{testType} ,'%')
                </if>
                <if test="deadline != null">
                    and t.deadline  = #{deadline}
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
                <if test="predecessorWorkOrder != null and predecessorWorkOrder != ''">
                    and t.predecessor_work_order  = #{predecessorWorkOrder}
                </if>
                <if test="relatedWorkOrder != null and relatedWorkOrder != ''">
                    and t.related_work_order  = #{relatedWorkOrder}
                </if>
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="whetherToIncludeInScheduling != null and whetherToIncludeInScheduling != ''">
                    and t.whether_to_include_in_scheduling  = #{whetherToIncludeInScheduling}
                </if>
                <if test="estimatedCompletionTime != null">
                    and t.estimated_completion_time  = #{estimatedCompletionTime}
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  = #{quantity}
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="workOrderStatus != null and workOrderStatus != ''">
                    and t.work_order_status  = #{workOrderStatus}
                </if>
                <if test="responsiblePerson != null and responsiblePerson != ''">
                    and t.responsible_person  = #{responsiblePerson}
                </if>
                <if test="productionStage != null and productionStage != ''">
                    and t.production_stage  = #{productionStage}
                </if>
                <if test="wtstabr != null and wtstabr != ''">
                    and t.wtstabr  = #{wtstabr}
                </if>
                <if test="whetherToEnterComponents != null and whetherToEnterComponents != ''">
                    and t.whether_to_enter_components  = #{whetherToEnterComponents}
                </if>
                <if test="whetherToEnterDocuments != null and whetherToEnterDocuments != ''">
                    and t.whether_to_enter_documents  = #{whetherToEnterDocuments}
                </if>
                <if test="completionTime8 != null">
                    and t.completion_time8  = #{completionTime8}
                </if>
                <if test="wmfcnr != null and wmfcnr != ''">
                    and t.wmfcnr  = #{wmfcnr}
                </if>
                <if test="irretrievableReason != null and irretrievableReason != ''">
                    and t.irretrievable_reason  = #{irretrievableReason}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectAwaitingProductionOrderListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
		select
		<include refid="Base_Column_List" />,cim.entrusted_unit as entrustedUnit,pl.product_model as productModel,pl.manufacturer as manufacturer,
        pl.product_name as productName,pl.production_batch as prodctionBatch,pl.production_batch as productionBatch,
        pc.category_name as productCategory,pl.standard_specification_id as standardSpecificationId ,
        (SELECT GROUP_CONCAT(ss.controlled_number)
        FROM standard_specification ss
        WHERE FIND_IN_SET(ss.id, pl.standard_specification_id) > 0) AS standardSpecificationNumber,
        sg.group_name as groupName,
        (SELECT GROUP_CONCAT(ss.attachment)
        FROM standard_specification ss
        WHERE FIND_IN_SET(ss.id, pl.standard_specification_id) > 0) AS standardSpecificationAttachment,
        po_pre.work_order_number as predecessorWorkOrderNumber,po_rel.work_order_number as relatedWorkOrderNumber,
        pl.deadline as deadline,pl.task_level as taskLevel,su.user_name  as responsiblePerson,su1.user_name as preparedBy,pl.test_type as testType
        from production_order t left join evaluation_order eo on t.order_number =eo.order_number
        left join customer_information_management cim on eo.customer_id  = cim.id
        left join product_list pl on t.product  = pl.id
        left join sys_user su on t.responsible_person  = su.id
        left join sys_user su1 on t.prepared_by = su1.id
        left join sys_group sg on t.codex_torch_group_id =sg.id
        LEFT JOIN production_order po_pre ON t.predecessor_work_order = po_pre.id
        LEFT JOIN production_order po_rel ON t.related_work_order = po_rel.id
        left join product_category pc on pl.product_category  = pc.id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>

	<select id="selectProductionOrderByModelAndBatch"
            resultType="com.huatek.frame.modules.business.domain.ProductionOrder">
        select
        <include refid="Base_Column_List" />
        from production_order t
        left join product_list pl on t.product  = pl.id
        where pl.evaluation_order_id =#{evaluationOrderId}
        and pl.product_model = #{productModel}
        and t.work_order_number like concat( #{prefix} ,'%')
<!--        and pl.production_batch = #{productionBatch}-->
        order by t.work_order_number desc limit 1
    </select>

	<select id="selectProductionOrderById"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
        select
        t.id,
        t.order_number as orderNumber,
        t.source_order as sourceOrder,
        cim.entrusted_unit as entrustedUnit,
        eo.engineering_code as engineeringCode,
        eo.report_requirements as reportRequirements,
        eo.report_format as reportFormat,
        eo.data_req_e_rep as dataReqERep,
        eo.data_reqs_papereport as dataReqsPapereport,
        pl.product_model as productModel ,
        pl.product_name as productName,
        pl.production_batch as productionBatch,
        pl.manufacturer ,
        pl.quantity as inspectionQuantity,
        pl.quality_grade as qualityGrade,
        pc.category_name as productCategory,
        pl.sample_total_count as sampleTotalCount,
        pl.work_order_inspection_number as workOrderInspectionNumber,
        (SELECT GROUP_CONCAT(ss.controlled_number)
        FROM standard_specification ss
        WHERE FIND_IN_SET(ss.id, pl.standard_specification_id) > 0) AS standardSpecificationNumber,
        pl.standard_specification_id as standardSpecificationId,
        pl.task_level as taskLevel,
        pl.test_type as testType,
        pl.deadline ,
        t.test_methodology as testMethodology,
        sg.group_name as groupName,
        t.predecessor_work_order as predecessorWorkOrder,
        t1.work_order_number as predecessorWorkOrderNumber,
        t.related_work_order as relatedWorkOrder,
        t2.work_order_number  as relatedWorkOrderNumber,
        t.pda ,
        t.whether_to_include_in_scheduling as whetherToIncludeInScheduling,
        t.estimated_completion_time as estimatedCompletionTime,
        t.quantity ,
        t.package_form as packageForm,
        su.user_name as responsiblePerson,
        t.wtstabr,
        t.attachment,
        eo.comment as orderRemarks,
        cps.comment as remark,
        cps.report_comment as reportRemarks,
        pl.other_tests as otherTests ,
        eo.report_requirements as reportRequirements,
        eo.report_format as reportFormat,
        eo.data_reqs_papereport as dataReqsPapereport,
        pl.experiment_project as experimentProject,
        pl.special_analysis_test_project as specialAnalysisTestProject,
        pl.inspection_test_project as inspectionTestProject,
        pl.quality_consistency_test_items as qualityConsistencyTestItems,
        pl.dpa_test_project as dpaTestProject,
        t.completion_time as completionTime,
        pi2.disk_burning_data as diskBurningData,
        t.product as productListId,
        pl.technical_liaison_name as technicalLiaisonName,
        pl.technical_liaison_phone_number as technicalLiaisonPhoneNumber,
        pl.group_type as groupType,
        ss.attachment as standardSpecificationAttachment,
        t.work_order_number as workOrderNumber,
        su1.user_name as preparedBy,
        t.production_stage as productionStage,
        t.work_order_status as workOrderStatus
        from
        production_order t
        left join customer_process_scheme cps on	t.id = cps.id
        left join evaluation_order eo on	t.order_number = eo.order_number
        left join customer_information_management cim on	eo.customer_id = cim.id
        left join product_list pl on	t.product = pl.id
        left join standard_specification ss on	pl.standard_specification_id = ss.id
        left join sys_user su on	t.responsible_person = su.id
        left join sys_user su1 on	t.prepared_by = su1.id
        left join sys_group sg on	t.codex_torch_group_id = sg.id
        left join product_category pc on	pl.product_category = pc.id
        left join product_inventory pi2 on	pi2.work_order_number = t.work_order_number
        left join production_order t1 on t.predecessor_work_order = t1.id
        left join production_order t2 on t.related_work_order = t2.id
        where t.id =#{id}

    </select>

	<select id="selectSameModelBatchManufacture"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderVO">
        select t.id,t.work_order_number ,pl.product_model ,pl.production_batch ,pl.manufacturer
        from production_order t left join product_list pl on t.product =pl.id
        where pl.product_model =#{productModel} and pl.production_batch =#{productionBatch}
         and pl.manufacturer =#{manufacturer} and pl.codex_torch_group_id =#{codexTorchGroupId} and t.id !=#{productionOrderId}
         and t.work_order_status ='7'
    </select>

	<select id="selectCurrentUserRoles" resultType="java.lang.String">
        select sr.role from sys_user_role sur left join sys_role sr on sur.role_id  = sr.id where sur.user_id =#{currentUserId}
    </select>

    <select id="selectOptionsByProcessCode3" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.process_name2 label,
        t.id value
        from standard_process_management t
        WHERE t.step_number != ''
    </select>
    <select id="selectOptionsByAssoWoPredProc" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        SELECT
        s.process_name2 as label,
        s.id as value
        FROM production_order t
        LEFT JOIN customer_process_scheme cps ON t.id = cps.work_order
        LEFT JOIN customer_experiment_project cep ON cps.id = cep.CODEX_TORCH_MASTER_FORM_ID
        LEFT JOIN standard_process_management s ON cep.process_id = s.id
        WHERE FIND_IN_SET(t.id, #{orderId}) > 0
        AND t.work_order_status NOT IN ('9','0','1','3')
        AND s.id IS NOT NULL
        GROUP BY s.id, s.process_name2
        HAVING COUNT(DISTINCT t.id) = (
        SELECT COUNT(DISTINCT order_id)
        FROM (
        SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(#{orderId}, ',', n), ',', -1) as order_id
        FROM (
        SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
        UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
        UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15
        UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20
        ) numbers
        WHERE n &lt;= LENGTH(#{orderId}) - LENGTH(REPLACE(#{orderId}, ',', '')) + 1
        ) temp
        )
    </select>
    <select id="selectOptionsByWorkstation" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.workstation_name label,
        t.id value
        from workstation t
        WHERE t.workstation_number != ''
    </select>
    <select id="selectOptionsByProductInformation1" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.specification_name label,
        t.id value
        from standard_specification t
        WHERE t.specification_number != ''
    </select>
    <select id="selectOptionsByTestingTeam" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.group_name label,
        t.id value
        from sys_group t
        WHERE t.group_code != ''
    </select>
    <select id="selectOptionsByDeviceType" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.device_type_name label,
        t.device_type_name value
        from device_type t
        WHERE t.device_type_name != ''
    </select>
    <select id="getInfoByOrderNumber"
            resultType="com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO">
        select
        t.order_number as orders,
        t.quantity as quantity,
        p.product_model as productModel,
        p.manufacturer as manufacturer,
        p.product_name as productName,
        p.production_batch as batchNumber,
        c.entrusted_unit as entrustedUnit
        from production_order  t
        left join product_list p on p.id = t.product
        left join evaluation_order e on e.order_number = t.order_number
        left join customer_information_management c on c.id = e.customer_id
        where t.work_order_number = #{workOrderNumber}

    </select>
    <sql id="Product_Order_Info">
        p.work_order_number as workOrderNumber,
        p.order_number as orderNumber,
        p.product as productId
    </sql>
    <sql id="Product_Info">
        pl.evaluation_order_id as evaluationOrderId,
        pl.standard_specification_id as standardSpecificationId,
        pl.product_model as productModel,
        pl.manufacturer as manufacturer,
        pl.product_category as productCategory,
        pl.product_name as productName,
        pl.production_batch as productionBatch,
        pl.test_type as testType
    </sql>
    <select id="getDetailByOrderNumber"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderInfoVO">
        select
        <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>,
        GROUP_CONCAT(DISTINCT ss.specification_number) as specificationNumber, c.entrusted_unit as entrustedUnit
        from production_order p
        LEFT JOIN product_list pl ON p.product = pl.id
        LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
        LEFT JOIN standard_specification ss ON FIND_IN_SET(ss.id, pl.standard_specification_id)
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        where p.work_order_number = #{productionWorkOrderNumber}
        GROUP BY p.id
    </select>

</mapper>