package com.huatek.frame.modules.business.service;

import com.huatek.frame.modules.business.domain.SalesOutbound;
import com.huatek.frame.modules.business.service.dto.SalesOutboundDTO;
import com.huatek.frame.modules.business.domain.vo.SalesOutboundVO;
import com.huatek.frame.common.response.TorchResponse;
import java.util.List;


/**
* @description 销售出库Service
* <AUTHOR>
* @date 2025-08-06
**/
public interface SalesOutboundService {
    
    /**
	 * 分页查找查找 销售出库
	 * 
	 * @param dto 销售出库dto实体对象
	 * @return 
	 */
	TorchResponse<List<SalesOutboundVO>> findSalesOutboundPage(SalesOutboundDTO dto);

    /**
	 * 添加 \修改 销售出库
	 * 
	 * @param salesOutboundDto 销售出库dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(SalesOutboundDTO salesOutboundDto);
	
	/**
	 * 通过id查找销售出库
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<SalesOutboundVO> findSalesOutbound(String id);
	
	/**
	 * 删除 销售出库
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 销售出库
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<SalesOutboundVO>> getOptionsList(String id);




    /**
     * 根据条件查询销售出库列表
     *
     * @param dto 销售出库信息
     * @return 销售出库集合信息
     */
    List<SalesOutboundVO> selectSalesOutboundList(SalesOutboundDTO dto);

    /**
     * 导入销售出库数据
     *
     * @param salesOutboundList 销售出库数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importSalesOutbound(List<SalesOutboundVO> salesOutboundList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取销售出库数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectSalesOutboundListByIds(List<String> ids);


	TorchResponse<String> executeOutbound(SalesOutboundDTO dto);

	/**
	 * 批量插入
	 * @param salesOutboundList
	 */
	int insertIntoSalesOutboundBatch(List<SalesOutbound> salesOutboundList);
}