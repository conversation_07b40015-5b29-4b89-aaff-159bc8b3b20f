package com.huatek.frame.modules.business.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * 监制验收工单查询产品明细响应DTO
 */
@Data
public class SawOrderProductVO {
    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 测评订单ID
     **/
    @ApiModelProperty("测评订单ID")
    private String evaluationOrderId;


    /**
     * 产品管理id
     */
    @ApiModelProperty("产品管理ID")
    private String productId;

    /**
     * 监制验收产品状态
     **/
    @ApiModelProperty("监制验收产品状态")
    @Excel(name = "监制验收产品状态",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String sawStatus;

    /**
     * 产品型号
     */
    @Excel(name = "产品型号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 生产批次
     **/
    @ApiModelProperty("生产批次")
    @Excel(name = "生产批次",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productionBatch;

    /**
     * 质保依据
     */
    @ApiModelProperty("质保依据")
    private String qualityAssuranceBasis;

    /**
     * 数量
     **/
    @ApiModelProperty("数量")
    @Excel(name = "数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer quantity;



    /**
     * 质量等级
     **/
    @ApiModelProperty("质量等级")
    @Excel(name = "质量等级",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String qualityGrade;

    /**
     * 标准规范编号
     */
    @ApiModelProperty("标准/规范编号")
    @Excel(name = "标准/规范编号", cellType = Excel.ColumnType.STRING, type = Excel.Type.ALL)
    private String standardSpecificationNumber;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String comment;

    /**
     * 完成日期
     */
    @ApiModelProperty("完成日期")
    private Date completeDate;

    /**
     * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
     * 更新人
     **/
    @ApiModelProperty("更新人")
//    @Excel(name = "更新人",
//        cellType = Excel.ColumnType.STRING,
//        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
     * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
     * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
     * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;
}
