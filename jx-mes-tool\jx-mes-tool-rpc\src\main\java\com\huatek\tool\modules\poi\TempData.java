package com.huatek.tool.modules.poi;

import java.io.File;
import java.io.FileInputStream;

import org.apache.poi.util.StringUtil;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class TempData {

	public static void main(String[] args) {
		System.out.println(JSONObject.toJSONString(createTempData()));
	}

	public static JSONObject createTempData() {
		JSONObject jsonData = new JSONObject();
		jsonData.put("p0", "K202401196(07)");
		jsonData.put("p1", "微波混频器");
		jsonData.put("p2", "HC701");
		jsonData.put("p3", "DPA分析");
		jsonData.put("p4", "广州瀚辰");
		jsonData.put("p5", "河北晶禾电子技术股份有限公司");
		jsonData.put("p6", "合格");
		jsonData.put("p7", "029-89697221-606");
		jsonData.put("p8", "029-89697221-610");
		jsonData.put("p9", "029-89697221-608");
		jsonData.put("p10", "710199");
		jsonData.put("p11", "陕西省西安市高新区毕原二路3号先导院南区7号楼、10号楼");
		jsonData.put("p12", "杨帆");
		jsonData.put("p13", "2024年01月30日");
		jsonData.put("p14", "杨帆");
		jsonData.put("p15", "2024年01月30日");
		jsonData.put("p16", "杨帆");
		jsonData.put("p17", "2024年01月30日");
		jsonData.put("p18", "2024年01月25日");
		jsonData.put("p19", "2024年01月30日");
		jsonData.put("p20", "微波混频器");
		jsonData.put("p21", "HC701");
		jsonData.put("p22", "HF2224");
		jsonData.put("p23", "4");
		jsonData.put("p24", "/");
		jsonData.put("p25", "工业级");
		jsonData.put("p26", "客户送样");
		jsonData.put("p27", "6-12");
		jsonData.put("p28", "2024年01月30日");
		jsonData.put("p29", "HC701");
		jsonData.put("p30", "HC702");
	    jsonData.put("p31", "商品价格清单");
	    jsonData.put("p32", createTable());
	    jsonData.put("p33", createArr());
	    
        JSONArray records = new JSONArray();
        JSONObject r0 = createRecord(0);
        JSONObject r1 = createRecord(1);
        JSONObject r2 = createRecord(2);
        JSONObject r3 = createRecord(2);
        r0.put("result", loopRow());
        
        JSONObject df = new JSONObject(); 
        df.put("content", "无异常，合格。");
        df.put("config", new StyleConfig(300, null, null, null, null, new Integer[]{5, 5, 5, 5}));
        
        r1.put("defaultResult", df);
        r2.put("result", commonRows());
        r3.put("result", createTable());
		records.add(r0);
		records.add(r1);
		records.add(r2);
		records.add(r3);
		jsonData.put("records", records);
	    
	    return jsonData;
	}
	
	private static JSONArray createArr() {
		JSONArray arr = new JSONArray();
		JSONObject td21 = new JSONObject(); td21.put("p330", "a"); td21.put("p331", "b"); 
		JSONObject td22 = new JSONObject(); td22.put("p330", "a"); td22.put("p331", "b"); 
		JSONObject td23 = new JSONObject(); td23.put("p330", "a"); td23.put("p331", "b"); 
		JSONObject td24 = new JSONObject(); td24.put("p330", "a"); td24.put("p331", "b"); 
		JSONObject td25 = new JSONObject(); td25.put("p330", "a"); td25.put("p331", "b"); 

		arr.add(td21);
		arr.add(td22);
		arr.add(td23);
		arr.add(td24);
		arr.add(td25);
		return arr;
	}
	
	private static JSONObject createTable() {
		JSONObject tab = new JSONObject();
		JSONArray rows = new JSONArray();
	    tab.put("dataType", "table");
	    tab.put("rows", rows);
	    JSONArray r1 = new JSONArray();
	    JSONObject th1 = new JSONObject(); th1.put("value", "序号"); r1.add(th1);
	    JSONObject th2 = new JSONObject(); th2.put("value", "编码"); r1.add(th2);
	    JSONObject th3 = new JSONObject(); th3.put("value", "名称"); r1.add(th3);
	    JSONObject th4 = new JSONObject(); th4.put("value", "售价"); r1.add(th4);
	    
	    JSONArray r2 = new JSONArray();
	    JSONObject td21 = new JSONObject(); td21.put("value", "1"); r2.add(td21);
	    JSONObject td22 = new JSONObject(); td22.put("value", "NO001"); r2.add(td22);
	    JSONObject td23 = new JSONObject(); td23.put("value", "商品1"); r2.add(td23);
	    JSONObject td24 = new JSONObject(); td24.put("value", 1.0); r2.add(td24);
	    
	    JSONArray r3 = new JSONArray();
	    JSONObject td31 = new JSONObject(); /*td31.put("value", "2");*/ r3.add(td31);
	    JSONObject td32 = new JSONObject(); td32.put("value", "NO002"); r3.add(td32);
	    JSONObject td33 = new JSONObject(); td33.put("value", "商品2"); r3.add(td33);
	    JSONObject td34 = new JSONObject(); td34.put("value", 2.0); r3.add(td34);
	    
	    JSONArray r4 = new JSONArray();
	    JSONObject td41 = new JSONObject(); td41.put("value", "3"); td41.put("cellSpan", 2); r4.add(td41);
	    JSONObject td42 = new JSONObject(); /*td42.put("value", "NO003");*/ r4.add(td42);
	    JSONObject td43 = new JSONObject(); td43.put("value", "商品3"); r4.add(td43);
	    JSONObject td44 = new JSONObject(); td44.put("value", 3.0); r4.add(td44);

	    JSONArray r5 = new JSONArray();
	    JSONObject td51 = new JSONObject(); td51.put("value", "4"); r5.add(td51);
	    JSONObject td52 = new JSONObject(); td52.put("value", "NO004"); r5.add(td52);
	    JSONObject td53 = new JSONObject(); td53.put("value", "商品4"); r5.add(td53);
	    JSONObject td54 = new JSONObject(); td54.put("value", 4.0); r5.add(td54);

	    JSONArray r6 = new JSONArray();
	    JSONObject td61 = new JSONObject(); td61.put("value", "5"); r6.add(td61);
	    JSONObject td62 = new JSONObject(); td62.put("value", "NO005"); r6.add(td62);
	    JSONObject td63 = new JSONObject(); td63.put("value", "商品5"); r6.add(td63);
	    JSONObject td64 = new JSONObject(); td64.put("value", 5.0); r6.add(td64);

	    JSONArray r7 = new JSONArray();
	    JSONObject td71 = new JSONObject(); td71.put("value", "6"); r7.add(td71);
	    JSONObject td72 = new JSONObject(); td72.put("value", "NO006"); r7.add(td72);
	    JSONObject td73 = new JSONObject(); td73.put("value", "商品6"); r7.add(td73);
	    JSONObject td74 = new JSONObject(); td74.put("value", 6.0); r7.add(td74);

	    JSONArray r8 = new JSONArray();
	    JSONObject td81 = new JSONObject(); td81.put("value", "7"); r8.add(td81);
	    JSONObject td82 = new JSONObject(); td82.put("value", "NO007"); r8.add(td82);
	    JSONObject td83 = new JSONObject(); td83.put("value", "商品7"); r8.add(td83);
	    JSONObject td84 = new JSONObject(); td84.put("value", 7.0); r8.add(td84);

	    JSONArray r9 = new JSONArray();
	    JSONObject td91 = new JSONObject(); td91.put("value", "8"); r9.add(td91);
	    JSONObject td92 = new JSONObject(); td92.put("value", "NO008"); r9.add(td92);
	    JSONObject td93 = new JSONObject(); td93.put("value", "商品8"); r9.add(td93);
	    JSONObject td94 = new JSONObject(); td94.put("value", 8.0); r9.add(td94);
	    
	    rows.add(r1);
	    rows.add(r2);
	    rows.add(r3);
	    rows.add(r4);
	    rows.add(r5);
	    rows.add(r6);
	    rows.add(r7);
	    rows.add(r8);
	    rows.add(r9);
	    return tab;
	} 
	
	
	private static JSONObject createRecord(int index) {
		String[] datas = new String[]{
			"微波混频器|HC701|HF2224|外部目检|23℃～27℃|40%RH～60%RH|立体显微镜|SMZ745T|/|/|JXYQ022|/|2025/09/26|/|GJB548B-2005 方法2009.1|4|GJB4027A-2006 项目1103 塑封半导体集成电路|本项目分析4只合格，0只不合格。|/|孙越海|2024/01/26|杨  帆|2024/01/26"
			, "微波混频器|HC701|HF2224|X射线检查|23℃～27℃|40%RH～60%RH|X光检测系统|EVO|/|/|JXYQ242|/|2025/09/26|/|GJB548B-2005 方法2009.1|4|GJB4027A-2006 项目1103 塑封半导体集成电路|本项目分析4只合格，0只不合格。|样品X射线检查均合格，只附其中1只样品的检查照片。|王骁勇|2024/01/26|杨  帆|2024/01/26"
			, "微波混频器|HC701|HF2224|声学扫描显微镜检查|23℃～27℃|40%RH～60%RH|超声波扫描检测系统|SONOSCAN D9650|/|/|JXYQ007|/|2025/09/26|/|GJB548B-2005 方法2009.1|4|GJB4027A-2006 项目1103 塑封半导体集成电路|本项目分析4只合格，0只不合格。|/|贠  丹|2024/01/26|杨  帆|2024/01/26"
		
		};
		JSONObject record = new JSONObject();
		String[] r1 = datas[index].split("\\|");
		for(int i = 0; i < r1.length; i++) {
			record.put("rp" + (i + 1), r1[i]);
		}
		return record;
	}
	
	public static JSONObject loopRow() {
		JSONObject row = new JSONObject();
		JSONObject config = new JSONObject();
		config.put("cellSpans", new int[]{3, 3});
		config.put("margin", new int[]{3, 3, 3, 3});
		config.put("rowHeights", new Integer[]{150, 15});
		row.put("config", config);
		JSONArray datas = new JSONArray();
		JSONObject d1 = createRowData("D:/file/doc/1.png","样品正面X射线检查照片，无异常");
		JSONObject d2 = createRowData("D:/file/doc/2.jpg","样品正面局部X射线检查照片，无异常");
		JSONObject d3 = createRowData("","样品侧面X射线检查照片，无异常");
		JSONObject d4 = createRowData("","样品侧面局部X射线检查照片，无异常");
		datas.add(d1);
		datas.add(d2);
		datas.add(d3);
		datas.add(d4);
		row.put("datas", datas);
		return row;
	}
	
	private static JSONObject commonRows() {
		JSONObject r = new JSONObject();
		JSONArray rows = new JSONArray();
		JSONObject row1 = new JSONObject();
		JSONObject config = new JSONObject();
		config.put("margin", new int[]{3, 3, 3, 3});
		config.put("height", 15);
		row1.put("config", config);
		row1.put("cells", new String[]{"101", "102", "103", "104", "105", "106"});
		JSONObject row2 = new JSONObject();
		row2.put("config", config);
		row2.put("cells", new String[]{null, null, "203", "204", "205", "206"});
		JSONObject row3 = new JSONObject();
		row3.put("config", config);
		row3.put("cells", new String[]{null, "202", "203", "204", "205", "206"});
		JSONObject row4 = new JSONObject();
		row4.put("config", config);
		row4.put("cells", new String[]{null, null, "203", "204", "205", "206"});
		rows.add(row1);
		rows.add(row2);
		rows.add(row3);
		rows.add(row4);
		r.put("rows", rows);
		return r;
	}
	
	private static JSONObject createRowData(String img, String remark) {
		JSONObject data = new JSONObject();
		JSONArray dr = new JSONArray();
		
		JSONObject image = new JSONObject(); dr.add(image);
		image.put("dataType", "image");
		if(StringUtil.isNotBlank(img)) {
			image.put("content", readImage(img));
			image.put("w", 200);
			image.put("h", 150);
		}
		
		JSONObject explan = new JSONObject(); dr.add(explan);
		explan.put("dataType", "string");
		explan.put("height", 30);
		explan.put("content", remark);
		
		data.put("rows", dr);
		return data;
	}
	
	private static byte[] readImage(String img) {
		try {
			File file = new File(img);
			FileInputStream fis = new FileInputStream(file);
			byte[] bs = new byte[(int)file.length()];
			fis.read(bs);
			fis.close();
			return bs;
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("文件未找到");
		}
		return new byte[0];
	}
	
}
