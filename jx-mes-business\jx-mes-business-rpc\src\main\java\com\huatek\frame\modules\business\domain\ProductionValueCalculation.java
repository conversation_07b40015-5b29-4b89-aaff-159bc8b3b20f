package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
* @description 产值计算
* <AUTHOR>
* @date 2025-08-22
**/
@Setter
@Getter
@TableName("production_value_calculation")
public class ProductionValueCalculation implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 工单编号
     **/
    @TableField(value = "work_order_number"
    )
    private String workOrderNumber;

    
    /**
	 * 工单类型
     **/
    @TableField(value = "ticket_type"
    )
    private String ticketType;

    
    /**
	 * 状态
     **/
    @TableField(value = "status"
    )
    private String status;

    /**
     * 实际完成时间
     */
    @TableField(value = "completion_time")
    private Timestamp completionTime;

    /**
     * 发货日期
     */
    @TableField(value = "shipping_date")
    private Date shippingDate;

    /**
     * 快递接收人
     */
    @TableField(value = "recipient_customer")
    private String recipientCustomer;

    
    /**
	 * 类型
     **/
    @TableField(value = "`type`"
    )
    private String type;

    
    /**
	 * 对账日期
     **/
    @TableField(value = "settlement_date"
    )
    private Date settlementDate;

    
    /**
	 * 产品名称
     **/
    @TableField(value = "product_name"
    )
    private String productName;

    
    /**
	 * 产品型号
     **/
    @TableField(value = "product_model"
    )
    private String productModel;

    
    /**
	 * 生产厂家
     **/
    @TableField(value = "manufacturer"
    )
    private String manufacturer;

    
    /**
	 * 批次号
     **/
    @TableField(value = "batch_number"
    )
    private String batchNumber;

    
    /**
	 * 产品分类
     **/
    @TableField(value = "product_category"
    )
    private String productCategory;

    
    /**
	 * 产品资料名称
     **/
    @TableField(value = "product_information_name"
    )
    private String productInformationName;

    
    /**
	 * 产品资料
     **/
    @TableField(value = "product_information1"
    )
    private String productInformation1;

    
    /**
	 * 委托单位
     **/
    @TableField(value = "entrusted_unit"
    )
    private String entrustedUnit;

    
    /**
	 * 产品数量
     **/
    @TableField(value = "product_quantity"
    )
    private Integer productQuantity;

    
    /**
	 * 收费标准名称
     **/
    @TableField(value = "charging_standard_name"
    )
    private String chargingStandardName;

    
    /**
	 * 内部核算价格
     **/
    @TableField(value = "internal_accounting_price"
    )
    private BigDecimal internalAccountingPrice;

    
    /**
	 * 折扣
     **/
    @TableField(value = "discount"
    )
    private BigDecimal discount;

    
    /**
	 * 客户核算价格
     **/
    @TableField(value = "customer_accounting_price"
    )
    private BigDecimal customerAccountingPrice;

    
    /**
	 * 对账价格
     **/
    @TableField(value = "settlement_price"
    )
    private BigDecimal settlementPrice;

    
    /**
	 * 内部价格分类
     **/
    @TableField(value = "internal_price_classification"
    )
    private String internalPriceClassification;

    
    /**
	 * 客户价格分类
     **/
    @TableField(value = "customer_price_classification"
    )
    private String customerPriceClassification;

    
    /**
	 * 订单编号
     **/
    @TableField(value = "order_number"
    )
    private String orderNumber;

    
    /**
	 * 结算单位
     **/
    @TableField(value = "settlement_unit"
    )
    private String settlementUnit;

    
    /**
	 * 合格数
     **/
    @TableField(value = "number_of_qualified_productions"
    )
    private Integer numberOfQualifiedProductions;

    
    /**
	 * 不合格数
     **/
    @TableField(value = "num_non_qual_prod"
    )
    private Integer numNonQualProd;

    
    /**
	 * 不合格工序
     **/
    @TableField(value = "non_quality_process"
    )
    private String nonQualityProcess;

    
    /**
	 * 试验类型
     **/
    @TableField(value = "test_type"
    )
    private String testType;

    
    /**
	 * 委托日期
     **/
    @TableField(value = "date_of_entrustment"
    )
    private Date dateOfEntrustment;

    
    /**
	 * 订单送检编号
     **/
    @TableField(value = "order_inspection_number"
    )
    private String orderInspectionNumber;

    
    /**
	 * 工单送检编号
     **/
    @TableField(value = "work_order_inspection_number1"
    )
    private String workOrderInspectionNumber1;

    
    /**
	 * 对账单号
     **/
    @TableField(value = "bill_statement_number"
    )
    private String billStatementNumber;

    
    /**
	 * 合同编号
     **/
    @TableField(value = "contract_number"
    )
    private String contractNumber;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}