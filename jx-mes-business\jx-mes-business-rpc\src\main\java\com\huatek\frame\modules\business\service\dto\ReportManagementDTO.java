package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 报告管理DTO 实体类
* <AUTHOR>
* @date 2025-08-15
**/
@Data
@ApiModel("报告管理DTO实体类")
public class ReportManagementDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 报告编号
     **/
    @ApiModelProperty("报告编号")
    private String reportNumber;
    
    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;

    /**
	 * 试验类型
     **/
    @ApiModelProperty("试验类型")
    private String testType;

    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    private String entrustedUnit;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    private String productName;

    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    private String productCategory;

    /**
	 * 报告编制人
     **/
    @ApiModelProperty("报告编制人")
    private String preparerOfTheReport;
    
    /**
	 * 编制时间
     **/
    @ApiModelProperty("编制时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp compilationTime;
    
    /**
	 * 报告状态
     **/
    @ApiModelProperty("报告状态")
    private String reportStatus;

    
    /**
	 * 附件
     **/
    @ApiModelProperty("附件")
    private String attachment;
    
    /**
	 * 申请人
     **/
    @ApiModelProperty("申请人")
    private String codexTorchApplicant;
    
    /**
	 * 待审批人
     **/
    @ApiModelProperty("待审批人")
    private String codexTorchApprover;
    
    /**
	 * 审批人列表
     **/
    @ApiModelProperty("审批人列表")
    private String codexTorchApprovers;
    
    /**
	 * 流程状态
     **/
    @ApiModelProperty("流程状态")
    private String codexTorchApprovalStatus;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;

	/**
	 * 工作流查询角色
     **/
    @ApiModelProperty("工作流查询角色")
    private String workflowQueryRole;


	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;


}