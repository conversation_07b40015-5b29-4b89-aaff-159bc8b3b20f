package com.huatek.frame.modules.business.rest;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.service.EquipmentInventoryService;
import com.huatek.frame.modules.business.service.EvaluationOrderService;
import com.huatek.frame.modules.business.service.OutputValueReportService;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "首页")
@RestController
@RequestMapping("/api/home")
public class HomeController {

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Autowired
    private OutputValueReportService outputValueReportService;

    @Autowired
    private EvaluationOrderService evaluationOrderService;

    @Autowired
    private EquipmentInventoryService equipmentInventoryService;

    /**
     * 获取首页看板统计数据--核算金额
     * @return 统计数据
     */
    @Log("获取首页看板统计数据--核算金额")
    @ApiOperation(value = "获取首页看板统计数据--核算金额")
    @GetMapping(value = "/dashboard", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("outputValueReport:dashboard")
    public TorchResponse getDashboardStats() {
        return outputValueReportService.getDashboardStats();
    }

    /**
     * 计算交付率
     * @return
     */
    @Log("计算交付率")
    @ApiOperation(value = "首页看板统计数据--计算交付率")
    @PostMapping(value = "/onTimeDeliveryRate", produces = {"application/json;charset=utf-8"})
    public TorchResponse onTimeDeliveryRate(){
        return  evaluationOrderService.onTimeDeliveryRate();
    }

    /**
     * 设备及时校准率
     * @return
     */
    @Log("设备及时校准率")
    @ApiOperation(value = "首页看板统计数据--设备及时校准率")
    @PostMapping(value = "/calibrationRate", produces = {"application/json;charset=utf-8"})
    public TorchResponse calibrationRate(){
        return equipmentInventoryService.calibrationRate();
    }

    /**
     * 本年度销售订单
     */
    @Log("本年度销售订单数量")
    @ApiOperation(value = "首页看板统计数据--本年度销售订单数量")
    @PostMapping(value = "/salesOrderCount", produces = {"application/json;charset=utf-8"})
    public TorchResponse getSalesOrderCount(){
        return evaluationOrderService.getSalesOrderCount();
    }

    /**
     * 预警查询
     *
     * @param dto     预警查询
     * @param request PlanDeviationWarningPagedDto
     *                PlanDeviationType 查所有:-1  开工延迟预警:0  完工延迟预警:1  未开工预警:2  未完工预警:3
     * @return
     */
    @Log("计划vs实际偏差预警查询")
    @ApiOperation("计划vs实际偏差预警查询")
    @PostMapping(value = "/PlanDeviationWarning", produces = {"application/json;charset=utf-8"})
    public Object GetPlanDeviationWarningAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/PlanDeviationWarning");
        return httpClientUtil.callMes(request, inputParamDto);
    }



}
